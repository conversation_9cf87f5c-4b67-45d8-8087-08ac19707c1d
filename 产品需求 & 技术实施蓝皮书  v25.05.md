# # 微信小程序 **「一句话全能助手」**  — 产品需求 & 技术实施蓝皮书  v25.05

> **文件角色** | 1) 产品需求文档 (PRD)  2) 技术选型白皮书 (Tech Design)  3) 开发/部署手册 (Dev Guide)。
> **存储位置** | `docs/PRD_StarterAuto.md` — 此文件为**单一真相源 (SSOT)**，任何修改请提交 PR 同步。

---

## 一、产品定位 ∙ Why & What

| 维度      | 描述                                                        |
| ------- | --------------------------------------------------------- |
| **一句话** | “一句话就能记日程、记账、排待办，并同步到任何日历的极简助手。”                          |
| 目标痛点    | 输入门槛高、跨 App 分类麻烦、汇总统计难、跨设备日历不同步。                          |
| 核心人群    | 25‑45 岁自由职业、SOHO、小微老板，重视时间&财务双管理。                         |
| 核心指标    | MVP 12 周：DAU ≥ 500、词错误率 (WER) ≤ 12 %、CalDAV 写入成功率 ≥ 99 %。 |
| 商业模式    | Freemium：基础永久免费；Pro ¥18/月（OCR、预算预警、PDF 报表、多人共享）。          |

---

## 二、功能分层 & 里程碑

| 层级           | MVP (0‑6w)                  | Beta (6‑12w)   | Growth (>12w) |
| ------------ | --------------------------- | -------------- | ------------- |
| **Capture**  | 语音 & 文本输入相对时间解析本地 NLP 分类    | OCR 票据         | 语音+OCR 多意图    |
| **Organize** | 日程/财务/待办 三表SQLite 离线缓存四象限拖放 | 多人账本预算上限提醒     | AI 消费分析       |
| **Share**    | 双通道日历同步 (CalDAV+系统API)      | 企业报销 Excel/PDF | SaaS 数据洞察 API |
| **Insight**  | 周/月报表 (ECharts)导出 PDF       | 聚合趋势、预算漏斗      | AI 建议 & 预测    |

### 里程碑验收表

| 周数  | 交付物         | 量化指标                           |
| --- | ----------- | ------------------------------ |
| 2w  | 流式 ASR Demo | 本地 WER ≤ 20 %                  |
| 4w  | MVP 内测版     | 语音→日历全链打通；P2+ Bug ≤ 10         |
| 6w  | 公测 v0.9     | DAU 100；WER ≤ 15 %；Crash ≤ 1 ‰ |
| 8w  | Pro 功能      | PDF+OCR 上线；订阅试付费 ≥ 30          |
| 12w | GA v1.0     | DAU 500；D7 留存 ≥ 35 %           |

---

## 三、技术选型决策

| 模块         | 备选                                               | 评估 (性能 / 成本 / 生态)                                                       | 结论                             |
| ---------- | ------------------------------------------------ | ----------------------------------------------------------------------- | ------------------------------ |
| **语音 ASR** | 微信 voicetotext 插件 / 腾讯云实时 ASR / 科大讯飞 SDK         | **微信插件**：延迟低但只出识别结果，无热词；**腾讯流式**：延迟 < 300 ms，支持自定义热词；**讯飞**：需企业 license | **双通路：腾讯流式 + 讯飞一句话兜底**         |
| **NLP 分类** | 云端 FastAPI / Edge ML BERT-mini / 规则正则            | 云端需要网络；BERT-mini 8 MB 量化后 ≤ 2 MB，端侧推理 25 ms                             | **Edge BERT-mini** + 规则兜底      |
| **数据库**    | CloudBase Mongo / 云开发 MySQL                      | Mongo 文档型符合非结构化数据；MySQL 冗长                                              | **CloudBase Mongo**            |
| **离线缓存**   | IndexedDB / SQLite (wx)                          | IndexedDB 不支持小程序；微信官方 SQLite API 稳定                                     | **SQLite**                     |
| **报表图表**   | ECharts / F2                                     | ECharts 官方插件，小程序兼容佳                                                     | **ECharts**                    |
| **UI 框架**  | TDesign / ColorUI / 自研                           | TDesign = 微信腾讯官方，组件齐；ColorUI 停更。                                        | **TDesign miniprogram** NPM 引入 |
| **CI/CD**  | 微信云 hosting CI / GitHub Actions + miniprogram‑ci | 云 hosting 功能浅；GitHub 生态丰富                                               | **Actions**                    |
| **云函数**    | CloudBase CF / 云开发 Node 服务                       | CF 免运维且与小程序同域                                                           | **CloudBase 云函数 Node 18**      |
| **存储**     | COS / OSS / 七牛                                   | 同云函数同家厂商免流量                                                             | **COS 私有桶**                    |

> **风险**：Google CalDAV 严格 OAuth 审核 → 预留系统日历 API 降级；讯飞 SDK 报价 → 超预算则改腾讯 “短语识别” API。

---

## 四、系统架构 & 数据流程

```mermaid
flowchart LR
  subgraph Client[小程序]
    R((录音)) --PCM--> ASR[腾讯流式插件]
    ASR --JSON--> NLP
    NLP --slots--> DBQueue[SQLite queue]\nstatus:0
    style NLP fill:#FEE
    BTN((FAB))
  end
  DBQueue -->|online| APIGW((云函数网关))
  APIGW --> CFwrite[云函数 write]
  CFwrite --> MONGO>CloudBase Mongo]
  CFwrite --> COSrecord((COS临时录音))
  MONGO --> CAL(CalDAV Push) --> Google/系统日历
  click MONGO "https://cloud.tencent.com/product/tcb" "Mongo" _blank
```

* **三保险语音链**

  1. 插件流式：300 ms 内返回；若 `Confidence < 0.85` 写 error 码。
  2. 讯飞一句话：异步识别。成功覆盖返回并更新文本字段。
  3. 每周热词上传脚本：统计最新客户/项目名 → `HotwordId` 动态刷新。

* **SQLite 持久化格式**

  ```ts
  interface LocalRecord {
    _id: string;
    type: 'schedule' | 'expense' | 'todo';
    title: string;
    amount?: number;
    startTime?: string;  // ISO
    urgency?: 0 | 1;
    importance?: 0 | 1;
    status: 0 | 1;       // 0=待同步 1=已同步
  }
  ```

---

## 五、界面规范 (TDesign + rpx)

| 元素     | 规格                                     | 说明                                  |
| ------ | -------------------------------------- | ----------------------------------- |
| FAB    | 56 px 圆形，主色 `@primary-color` `#2563EB` | 合盖时固定右下，展开时居中偏下 80rpx；滚动透明度 0.4     |
| TabBar | 5 项 + SafeArea                         | 文字 10 px，Icon 24 px Line            |
| 列表卡片   | `t-cell` large / 左彩条 4 px              | 彩条颜色映射来源日历                          |
| 四象限    | `t-row`×2 ➕ `t-col`×2                  | 象限标题 Tag 高度 24 px；`movable-view` 拖动 |
| 折叠屏适配  | `onResize` 判断 `windowWidth>700` → 两栏   | 第二栏 w = 50 %‑12rpx                  |

---

## 六、用户管理 & 登录体系

### 6.1 功能目标

* **微信一键登录**：使用 `wx.login → code2Session` 获取 `openid + unionid`，免密码。
* **用户中心页面** (`pages/profile/index`)：展示头像昵称、订阅状态、设备同步信息。
* **数据隔离**：各集合 (`schedule / expense / todo`) 按 `uid`（= openid）分区存储，确保跨用户数据安全。
* **多端绑定**：同一 `unionid` 下，iOS / Android / Web 小程序共享数据。
* **注销 & 数据清除**：用户可一键删除所有云端数据，遵守《个人信息保护法》。

### 6.2 技术实现

| 步骤 | 端侧                    | 云侧                        | 说明                              |
| -- | --------------------- | ------------------------- | ------------------------------- |
| ①  | `wx.login()`          | `tcb/auth.getWxContext()` | 获取 `openid` 保存 `globalData.uid` |
| ②  | `wx.getUserProfile()` | `users` 集合 `upsert`       | 存昵称、头像、注册时间                     |
| ③  | 请求业务接口                | 云函数获取 `context.OPENID`    | 所有 CRUD 默认带 `uid` 条件            |
| ④  | 注销                    | 调用 `deleteAccount` 云函数    | 删除子集合 & COS 文件；返回 0 = 成功        |

#### 数据结构

```ts
collection users {
  _id: string; // openid or unionid
  nick: string;
  avatar: string;
  createdAt: Date;
  proUntil?: Date;   // Pro 订阅到期
}
```

### 6.3 UI & 交互

* **登录页** (`login.wxml`)：`t-button theme="primary" bindtap="handleLogin"`。
* 首次进入任何功能页，若 `!uid` 则 `navigateTo('/pages/login')`。
* 头像 + 昵称：使用 `t-avatar` + `t-cell`；Pro 订阅显示 `t-tag theme="success"`。
* "注销账号" 按钮置底红色 `t-button theme="danger" variant="outline"`。

### 6.4 安全

* `session_key` 加密存储于 CloudBase `auth`；前端不落地。
* 敏感 API 使用云函数校验 `context.OPENID === record.uid`。
* OAuth Scope 仅申请 `snsapi_base`；获取个人信息需显式 `wx.getUserProfile()`。

---

## 七、开发环境部署

### 1. Clone & Bootstrap

```bash
# 环境：Node 20 + pnpm 9 + Git + 微信 DevTools 1.07+

# Clone
$ <NAME_EMAIL>:your-org/starter-auto.git && cd starter-auto
$ pnpm install
# 拉 TDesign Starter 以及 Demo
$ npm run starter:init
# 本地构建 NPM & 打开微信 IDE
$ npm run tdesign:build
```

### 2. 微信 DevTools Checklist

1. 工具 ▶ 详情 ▶ 关闭“跟随编译” → 手动构建 NPM。
2. 域名白名单：`asr.tencentcloudapi.com` / `cos.<region>.myqcloud.com` etc.
3. 真机调试检查：折叠/展开 Fold5 无闪白。

### 3. Cursor IDE Task 快捷键

| 任务         | 命令                          | 触发键 (⌘ ⇧ P)             |
| ---------- | --------------------------- | ----------------------- |
| Starter 更新 | `npm run starter:init`      | *proj\:starter\:init*   |
| 新页面        | `npm run page:new <name>`   | *proj\:page\:new*       |
| 构建 + 预览    | `npm run tdesign:build`     | *proj\:build*           |
| 改主题色       | `npm run theme:set #RRGGBB` | *proj\:setTheme*        |
| 上传体验       | `npm run ci:upload`         | *proj*\*:upload\*\*体验\* |

### 4. CloudFunctions

```
cloudfunctions/
├ asrRetry/      # 置信度兜底识别
├ ocrTicket/     # 发票 & 小票 OCR
└ reportGen/     # Puppeteer ↦ PDF 周/月报
```

* Node 18	Runtime；256 MiB；超时 60‑128 s。
* `tencentcloud-sdk-nodejs@^4`；COS 私有桶临时文件 7 天 TTL。

### 5. GitHub Action `ci.yml`

```yaml
name: weapp-ci
on:
  push:
    branches: [main]
jobs:
  build-upload:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: 9
      - run: pnpm i --frozen-lockfile
      - name: Build & Lint
        run: |
          npm run lint
          npm run tdesign:build --omit-dev
      - name: Upload MiniProgram
        env:
          PRIVATE_KEY: ${{ secrets.MINIPROGRAM_PRIVATE_KEY }}
        run: |
          echo "$PRIVATE_KEY" > private.key
          node scripts/ci-upload.js
```

---

## 八、代码骨架示例

### 8.1 云函数 `asrRetry`

```js
const cloud = require('wx-server-sdk');
cloud.init();
const tencentcloud = require('tencentcloud-sdk-nodejs');
const AsrClient = tencentcloud.asr.v20190614.Client;
const client = new AsrClient({
  credential: { secretId: process.env.SECRET_ID, secretKey: process.env.SECRET_KEY },
  region: 'ap-shanghai',
  profile: { httpProfile: { endpoint: 'asr.tencentcloudapi.com' } },
});
exports.main = async ({ recordUrl, docId }) => {
  const { Result } = await client.SentenceRecognition({
    EngSerViceType: '16k_zh_video',
    SourceType: 0,
    Url: recordUrl,
  });
  const db = cloud.database();
  await db.collection('records').doc(docId).update({ data: { text: Result } });
  return Result;
};
```

### 8.2 本地 NLP 轻量 BERT 推理（片段）

```js
import * as qna from '@tensorflow-models/qna';
const model = await qna.load();
const answers = await model.findAnswers(text, text);
// 根据 answers 做意图与槽位映射
```

### 8.3 热词自动更新脚本

```bash
# scripts/hotword-extract.js
const fs = require('fs');
// 分析最近识别失败记录，输出高频新词列表...
```

---

## 九、FAQ & 常见故障排查

| 问题             | 排查方案                                                       |
| -------------- | ---------------------------------------------------------- |
| 流式 ASR 断线 1004 | 请求域名未加入 request 白名单；或真机网络劫持 HTTPS。                         |
| TDesign 样式全失效  | 忘记在页面 json 设置 `styleIsolation:"apply-shared"`；或 NPM 未重新构建。 |
| CalDAV 401     | Google OAuth token 过期；触发 `refresh_token` 流程或提示重新授权。        |
| 折叠屏展开闪白        | 布局需在 `onResize` debounce 100 ms 后统一 setData。               |

---

## 十、模块化开发推进计划

为降低并行风险，我们采用“**单模块→上线→回收反馈→下一个模块**”的滚动迭代方式。每个模块遵循 **2 周一个 Sprint**、**4 个阶段 (设计→开发→联调→验收)**、**3 个出厂门禁 (代码评审 / 单元测试 / 体验版)**。

| 序号 | 模块              | 目标功能                               | 负责人  | 预计周期 | 验收门禁                       |
| -- | --------------- | ---------------------------------- | ---- | ---- | -------------------------- |
| ①  | **Capture 输入链** | 录音 + 流式 ASR + Async 兜底 + SQLite 缓存 | FE-A | 2 周  | *WER ≤ 20 %*；离线重连成功率 100 % |
| ②  | **Organize 三表** | 日程 / 财务 / 待办 CRUD + 四象限拖放          | FE-B | 2 周  | CRUD 100 % 通过；拖拽 < 200 ms  |
| ③  | **Share 日历同步**  | CalDAV 写 Google + 系统日历 API         | FE-C | 2 周  | 写入成功率 ≥ 98 %；冲突回滚 ≤1 %     |
| ④  | **Insight 报表**  | ECharts 周/月报 + PDF 导出              | FE-D | 2 周  | 报表生成 < 3 s；PDF 字体/颜色正确     |
| ⑤  | **User Auth**   | 微信登录 + 用户中心 + 数据隔离                 | FE-A | 1 周  | 登录耗时 < 600 ms；跨端数据一致       |
| ⑥  | **OCR 票据**      | 相册/相机拍照 → 云函数识别                    | FE-B | 2 周  | 主要字段识别准确率 ≥ 90 %           |
| ⑦  | **预算 & 预警**     | 科目预算设置 + 超额推送                      | FE-C | 1 周  | 推送延迟 < 1 min；误报 ≤ 2 %      |
| ⑧  | **订阅 & 支付**     | Pro 升级 & 订阅校验                      | FE-D | 2 周  | 支付成功回调 ≤ 3 s；漏付 0          |

> **推进规则**
>
> 1. 同一 Sprint 内 **只开放 1 个主干分支**；其他模块只能起 feature 分支，不进 `dev`。
> 2. 模块上线进入 **灰度 3 天**；若关键指标达标即切至全量，同时开启下一个模块开发。
> 3. 如上一模块 Bug 累计 ≥ 3 个 P1，则立即 **封板**，暂停后续迭代直到修复。

**建议：**

* 代码评审前置：每个 PR 至少 2 人 Review + SonarLint 无红灯。
* 自动化：新增 module 时必须补对应 Jest 单测 & 云函数集成测试脚本。
* 监控：上线当日在 CLS 设置 Error Rate 告警阈值，任何 Error > 0.5 % 触发 PagerDuty。

---

## 十一、贡献 & 版本管理

1. **分支模型**：`main` ➜ 生产；`dev` ➜ 日常集成；feature/xx ➜ 功能分支。
2. **PRS**：至少 1 Reviewer；CI 绿色才可合并。任何代码

---

> 更新者：ChatGPT | 最后更新时间：2025‑05‑28变更需同步此蓝皮

1. **版本号**：`YY.MM.patch`，示例 `25.06.0`。
2. **文档更新**：书内容。

1) **发布节奏**：周一切 release 分支 → 周三灰度 → 周五全量。
