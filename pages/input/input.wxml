<!--pages/input/input.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-top">
      <text class="logout-btn" bind:tap="handleLogout">退出登录</text>
    </view>
    <text class="page-title">快速输入</text>
    <text class="page-subtitle">AI智能识别 · 自动分类 · 高效记录</text>
    
    <!-- 用户信息显示 -->
    <view class="user-info" bind:tap="showUserDetail">
      <text class="user-info-text">{{userDisplayText}}</text>
      <text class="user-info-detail">点击查看详情</text>
    </view>
    
    <!-- AI服务状态指示器 -->
    <view class="ai-status-indicator">
      <view class="ai-status {{aiStatus.configured ? 'ai-enhanced' : 'ai-local'}}">
        <text class="ai-icon">{{aiStatus.configured ? '🚀' : '🔧'}}</text>
        <text class="ai-text">{{aiStatus.configured ? 'DeepSeek AI' : '本地规则'}}</text>
      </view>
      
      <!-- 🎤 语音服务状态指示器 -->
      <view class="voice-status {{voiceStatus.hasPermission ? 'voice-enabled' : 'voice-disabled'}}">
        <text class="voice-icon">{{voiceStatus.hasPermission ? '🎤' : '🔇'}}</text>
        <text class="voice-text">{{voiceStatus.hasPermission ? '语音可用' : '需要权限'}}</text>
      </view>
    </view>
  </view>

  <!-- 🎤 语音状态显示区域 -->
  <view class="voice-status-section" wx:if="{{voiceStatus.isRecording || voiceStatus.isProcessing || voiceStatus.currentText}}">
    <view class="voice-status-card {{voiceStatus.source}}">
      <view class="voice-status-header">
        <text class="voice-status-icon">
          {{voiceStatus.isRecording ? '🎤' : voiceStatus.isProcessing ? '🔄' : voiceStatus.source === 'error' ? '❌' : '✅'}}
        </text>
        <text class="voice-status-text">
          {{voiceStatus.isRecording ? '正在录音...' : voiceStatus.isProcessing ? '正在识别...' : voiceStatus.source === 'error' ? '识别失败' : '识别完成'}}
        </text>
        <view class="voice-confidence" wx:if="{{voiceStatus.confidence > 0}}">
          <text class="confidence-text">{{Math.round(voiceStatus.confidence * 100)}}%</text>
        </view>
      </view>
      
      <view class="voice-result-text" wx:if="{{voiceStatus.currentText}}">
        <text class="result-content">{{voiceStatus.currentText}}</text>
      </view>
      
      <!-- 录音控制按钮 -->
      <view class="voice-controls" wx:if="{{voiceStatus.isRecording}}">
        <button class="voice-control-btn stop" bind:tap="stopVoiceInput">
          <text class="btn-icon">⏹️</text>
          <text class="btn-text">停止录音</text>
        </button>
      </view>
    </view>
  </view>

  <!-- AI功能说明 -->
  <view class="ai-intro-card">
    <view class="ai-intro-header">
      <text class="ai-intro-icon">🤖</text>
      <text class="ai-intro-title">AI智能识别</text>
      <view class="ai-status-badge {{aiStatus.configured ? 'status-enhanced' : 'status-local'}}">
        {{aiStatus.configured ? 'DeepSeek增强' : '本地规则'}}
      </view>
    </view>
    <view class="ai-intro-content">
      <text class="ai-intro-desc">
        {{aiStatus.configured ? 
          'DeepSeek AI提供更准确的语义理解和智能分类' : 
          '使用本地规则进行基础识别，建议配置DeepSeek API获得更好效果'
        }}
      </text>
      
      <!-- AI能力展示 -->
      <view class="ai-capabilities">
        <view class="capability-item {{aiStatus.configured ? 'enhanced' : 'basic'}}">
          <text class="capability-icon">⏰</text>
          <text class="capability-text">时间解析</text>
        </view>
        <view class="capability-item {{aiStatus.configured ? 'enhanced' : 'basic'}}">
          <text class="capability-icon">💰</text>
          <text class="capability-text">金额识别</text>
        </view>
        <view class="capability-item {{aiStatus.configured ? 'enhanced' : 'basic'}}">
          <text class="capability-icon">📝</text>
          <text class="capability-text">语义理解</text>
        </view>
        <view class="capability-item {{aiStatus.configured ? 'enhanced' : 'basic'}}">
          <text class="capability-icon">🎯</text>
          <text class="capability-text">智能分类</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 输入方式 -->
  <view class="input-section">
    <view class="section-title">输入方式</view>
    <view class="input-methods">
      <!-- 🎤 语音输入突出显示 -->
      <view class="input-method voice-method {{voiceStatus.isRecording ? 'recording' : voiceStatus.isProcessing ? 'processing' : ''}}" bind:tap="showVoiceInput">
        <view class="method-icon voice-icon">
          {{voiceStatus.isRecording ? '🎙️' : voiceStatus.isProcessing ? '🔄' : '🎤'}}
        </view>
        <text class="method-text">语音输入</text>
        <text class="method-desc">
          {{voiceStatus.hasPermission ? 
            (voiceStatus.isRecording ? '录音中...' : voiceStatus.isProcessing ? '识别中...' : '点击开始录音') : 
            '需要录音权限'
          }}
        </text>
        <view class="voice-status-indicator {{voiceStatus.hasPermission ? 'enabled' : 'disabled'}}">
          <text class="status-text">{{voiceStatus.hasPermission ? '可用' : '权限'}}</text>
        </view>
      </view>
      
      <view class="input-method ai-method" bind:tap="showTextInput">
        <view class="method-icon">🤖</view>
        <text class="method-text">AI智能识别</text>
        <text class="method-desc">自动识别分类内容</text>
      </view>
      
      <view class="input-method" bind:tap="showQuickAdd">
        <view class="method-icon">⚡</view>
        <text class="method-text">快速添加</text>
        <text class="method-desc">预设常用模板</text>
      </view>
      <view class="input-method" bind:tap="showTemplates">
        <view class="method-icon">📋</view>
        <text class="method-text">模板</text>
        <text class="method-desc">使用现成模板</text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-section">
    <view class="section-title">快速操作</view>
    <view class="quick-actions">
      <view class="quick-action" bind:tap="addSchedule">
        <view class="action-icon schedule">📅</view>
        <text class="action-text">添加日程</text>
      </view>
      <view class="quick-action" bind:tap="addTodo">
        <view class="action-icon todo">✅</view>
        <text class="action-text">添加待办</text>
      </view>
      <view class="quick-action" bind:tap="addNote">
        <view class="action-icon note">📝</view>
        <text class="action-text">记录笔记</text>
      </view>
      <view class="quick-action debug-action" bind:tap="showSystemDebugMenu">
        <view class="action-icon debug">🔧</view>
        <text class="action-text">系统调试</text>
      </view>
    </view>
  </view>

  <!-- 最近输入记录 -->
  <view class="recent-section" wx:if="{{recentInputs.length > 0}}">
    <view class="section-title">
      <text>最近输入</text>
      <text class="clear-btn" bind:tap="clearRecentInputs">清空</text>
    </view>
    <view class="recent-list">
      <view class="recent-item" wx:for="{{recentInputs}}" wx:key="id" bind:tap="onRecentClick" data-item="{{item}}">
        <view class="recent-icon {{item.type}}">
          <text class="recent-emoji">{{item.icon}}</text>
        </view>
        <view class="recent-content">
          <text class="recent-title">{{item.content}}</text>
          <text class="recent-time">{{item.time}}</text>
          <text class="recent-type-tag">{{item.typeText}}</text>
        </view>
        <view class="recent-actions" bind:tap="showRecentActions" data-item="{{item}}">
          <text class="action-more">⋯</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{recentInputs.length === 0}}">
    <view class="empty-icon">💭</view>
    <text class="empty-text">暂无输入记录</text>
    <text class="empty-subtitle">开始记录你的第一个想法吧</text>
    <!-- 🎤 语音输入引导 -->
    <view class="empty-guide" wx:if="{{voiceStatus.hasPermission}}">
      <button class="guide-btn voice-guide" bind:tap="showVoiceInput">
        <text class="guide-icon">🎤</text>
        <text class="guide-text">试试语音输入</text>
      </button>
    </view>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab {{voiceStatus.isRecording ? 'recording' : ''}}" bind:tap="showAddMenu">
    <text class="fab-icon">{{voiceStatus.isRecording ? '🎤' : '+'}}</text>
  </view>

  <!-- 浮动按钮调试提示 -->
  <view class="fab-debug-tip" wx:if="{{!voiceStatus.isRecording}}">
    <text class="debug-tip-text">点击+按钮打开菜单</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 