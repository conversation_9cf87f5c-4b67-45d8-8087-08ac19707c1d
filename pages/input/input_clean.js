// pages/input/input.js - 一句话全能助手核心页面（基于蓝皮书v25.05重构）

const app = getApp();
const dataApi = require('../../utils/dataApi');
const aiService = require('../../utils/aiService');

Page({
  data: {
    // UI状态
    voiceInputVisible: false,
    isRecording: false,
    
    // 服务状态
    aiEnabled: true,
    voiceEnabled: true,
    
    // 数据
    recentInputs: [],
    
    // 调试模式（开发时启用，生产时禁用）
    debugMode: true
  },

  async onLoad() {
    console.log('🚀 一句话全能助手启动');
    await this.initServices();
    await this.loadRecentInputs();
  },

  async onShow() {
    await this.loadRecentInputs();
    this.updateStatus();
  },

  // 🔧 核心服务初始化
  async initServices() {
    try {
      // 初始化数据API
      if (dataApi && dataApi.init) {
        await dataApi.init();
        console.log('✅ 数据服务初始化成功');
      }

      // 初始化AI服务
      if (aiService && aiService.isConfigured()) {
        console.log('✅ AI服务已配置');
        this.setData({ aiEnabled: true });
      } else {
        console.log('⚠️ AI服务未配置');
        this.setData({ aiEnabled: false });
      }

      // 初始化语音服务
      this.setData({ voiceEnabled: true });
      
    } catch (error) {
      console.error('❌ 服务初始化失败:', error);
    }
  },

  // 🎤 核心功能：语音输入
  async startVoiceInput() {
    if (!this.data.voiceEnabled) {
      wx.showToast({ title: '语音服务不可用', icon: 'error' });
      return;
    }

    try {
      this.setData({ 
        voiceInputVisible: true, 
        isRecording: true 
      });

      console.log('🎤 开始语音识别...');
      
      // 使用微信原生语音识别
      wx.startRecord({
        success: () => {
          console.log('📱 录音开始');
        },
        fail: (error) => {
          console.error('❌ 录音失败:', error);
          this.handleVoiceError('录音失败');
        }
      });

      // 10秒后自动停止
      setTimeout(() => {
        if (this.data.isRecording) {
          this.stopVoiceInput();
        }
      }, 10000);

    } catch (error) {
      console.error('❌ 语音输入启动失败:', error);
      this.handleVoiceError('语音服务启动失败');
    }
  },

  stopVoiceInput() {
    this.setData({ isRecording: false });
    
    wx.stopRecord({
      success: (res) => {
        console.log('🎤 录音结束，开始识别...');
        this.processVoiceRecord(res.tempFilePath);
      },
      fail: (error) => {
        console.error('❌ 停止录音失败:', error);
        this.handleVoiceError('停止录音失败');
      }
    });
  },

  // 处理语音记录
  async processVoiceRecord(tempFilePath) {
    try {
      // 使用微信语音转文字
      wx.translateVoice({
        filePath: tempFilePath,
        isShowProgressTips: 1,
        success: (res) => {
          console.log('🎯 语音识别成功:', res.result);
          this.setData({ voiceInputVisible: false });
          this.processTextInput(res.result);
        },
        fail: (error) => {
          console.error('❌ 语音识别失败:', error);
          this.handleVoiceError('语音识别失败');
        }
      });
    } catch (error) {
      console.error('❌ 处理语音记录失败:', error);
      this.handleVoiceError('处理语音失败');
    }
  },

  // 处理语音错误
  handleVoiceError(message) {
    this.setData({ 
      voiceInputVisible: false, 
      isRecording: false 
    });
    wx.showToast({ 
      title: message, 
      icon: 'error' 
    });
  },

  // 📝 文本输入
  async showTextInput() {
    try {
      const result = await new Promise((resolve, reject) => {
        wx.showModal({
          title: '输入内容',
          editable: true,
          placeholderText: '说说你想记录的内容...',
          success: resolve,
          fail: reject
        });
      });

      if (result.confirm && result.content) {
        await this.processTextInput(result.content);
      }
    } catch (error) {
      console.error('❌ 文本输入失败:', error);
    }
  },

  // 🤖 核心功能：文本处理与AI分类
  async processTextInput(content) {
    if (!content || content.trim().length === 0) {
      wx.showToast({ title: '内容不能为空', icon: 'error' });
      return;
    }

    wx.showLoading({ title: '智能分析中...' });

    try {
      console.log('🤖 开始AI智能分析:', content);

      // 调用AI分类
      const aiResult = await aiService.intelligentClassify(content);
      
      if (aiResult && aiResult.items && aiResult.items.length > 0) {
        const classificationResult = aiResult.items[0];
        console.log('✅ AI分类成功:', classificationResult);
        
        // 根据分类结果创建数据
        await this.createDataFromClassification(content, classificationResult);
        
        // 更新最近输入
        await this.addToRecentInputs(content, classificationResult.primaryType);
        
        wx.hideLoading();
        wx.showToast({ 
          title: `已保存为${this.getTypeDisplayName(classificationResult.primaryType)}`,
          icon: 'success' 
        });
        
      } else {
        throw new Error('AI分类无有效结果');
      }
      
    } catch (error) {
      console.error('❌ AI分析失败:', error);
      wx.hideLoading();
      
      // 降级到手动选择
      this.showManualClassification(content);
    }
  },

  // 根据AI分类创建数据
  async createDataFromClassification(content, classification) {
    const { primaryType } = classification;
    
    try {
      switch (primaryType) {
        case 'schedule':
          await this.createScheduleData(content, classification);
          break;
        case 'finance':
          await this.createFinanceData(content, classification);
          break;
        default:
          await this.createTodoData(content, classification);
      }
      
      console.log(`✅ ${primaryType} 数据创建成功`);
      
    } catch (error) {
      console.error(`❌ 创建${primaryType}数据失败:`, error);
      throw error;
    }
  },

  // 创建日程数据
  async createScheduleData(content, classification) {
    const scheduleData = {
      title: classification.title || content,
      description: content,
      start_time: classification.dateTime || classification.timeInfo?.dateTime || new Date().toISOString(),
      end_time: new Date(new Date(classification.dateTime || new Date()).getTime() + 60*60*1000).toISOString(),
      location: classification.location || '',
      category: classification.category || '一般',
      source: 'AI智能分析'
    };

    await dataApi.createSchedule(scheduleData);
    console.log('📅 日程创建成功:', scheduleData);
  },

  // 创建财务数据
  async createFinanceData(content, classification) {
    const financeData = {
      title: classification.title || content,
      amount: classification.financialInfo?.amount || 0,
      type: 'expense',
      category: classification.financialInfo?.category || '其他',
      description: content,
      date: new Date().toISOString().split('T')[0],
      source: 'AI智能分析'
    };

    await dataApi.createExpense(financeData);
    console.log('💰 财务记录创建成功:', financeData);
  },

  // 创建待办数据
  async createTodoData(content, classification) {
    const todoData = {
      title: classification.title || content,
      description: content,
      priority: this.getPriorityFromClassification(classification),
      category: classification.category || '一般',
      due_date: classification.dateTime || null,
      source: 'AI智能分析'
    };

    await dataApi.createTodo(todoData);
    console.log('✅ 待办创建成功:', todoData);
  },

  // 手动分类选择
  showManualClassification(content) {
    wx.showActionSheet({
      itemList: ['📅 保存为日程', '💰 保存为财务记录', '✅ 保存为待办事项'],
      success: async (res) => {
        const types = ['schedule', 'finance', 'todo'];
        const selectedType = types[res.tapIndex];
        
        try {
          wx.showLoading({ title: '保存中...' });
          
          const basicClassification = {
            primaryType: selectedType,
            title: content,
            category: '一般'
          };
          
          await this.createDataFromClassification(content, basicClassification);
          await this.addToRecentInputs(content, selectedType);
          
          wx.hideLoading();
          wx.showToast({ 
            title: `已保存为${this.getTypeDisplayName(selectedType)}`,
            icon: 'success' 
          });
          
        } catch (error) {
          wx.hideLoading();
          wx.showToast({ title: '保存失败', icon: 'error' });
          console.error('❌ 手动分类保存失败:', error);
        }
      }
    });
  },

  // 🔄 最近输入管理
  async loadRecentInputs() {
    try {
      const recentInputs = wx.getStorageSync('recent_inputs') || [];
      this.setData({ recentInputs });
    } catch (error) {
      console.error('❌ 加载最近输入失败:', error);
    }
  },

  async addToRecentInputs(content, type) {
    try {
      const recentInputs = wx.getStorageSync('recent_inputs') || [];
      
      const newInput = {
        id: Date.now(),
        content,
        type,
        timestamp: new Date().toISOString()
      };

      // 添加到开头，保留最近20条
      recentInputs.unshift(newInput);
      const limitedInputs = recentInputs.slice(0, 20);
      
      wx.setStorageSync('recent_inputs', limitedInputs);
      this.setData({ recentInputs: limitedInputs });
      
    } catch (error) {
      console.error('❌ 保存最近输入失败:', error);
    }
  },

  // 清空最近输入
  clearRecentInputs() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有最近输入吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('recent_inputs');
          this.setData({ recentInputs: [] });
          wx.showToast({ title: '已清空', icon: 'success' });
        }
      }
    });
  },

  // 🔧 调试功能（仅在开发模式下显示）
  showDebugMenu() {
    if (!this.data.debugMode) return;
    
    wx.showActionSheet({
      itemList: [
        '🤖 测试AI分类',
        '📊 查看存储数据',
        '🔄 重新初始化服务',
        '🧹 清空所有数据'
      ],
      success: (res) => {
        const actions = [
          () => this.testAIClassification(),
          () => this.showStorageData(),
          () => this.initServices(),
          () => this.clearAllData()
        ];
        actions[res.tapIndex]();
      }
    });
  },

  // 测试AI分类
  async testAIClassification() {
    const testTexts = [
      '明天开会',
      '买咖啡花了15元',
      '7天后去杭州',
      '记得完成报告'
    ];
    
    wx.showLoading({ title: '测试中...' });
    
    for (const text of testTexts) {
      try {
        console.log(`🧪 测试文本: ${text}`);
        const result = await aiService.intelligentClassify(text);
        console.log(`✅ 分类结果:`, result);
      } catch (error) {
        console.error(`❌ 测试失败: ${text}`, error);
      }
    }
    
    wx.hideLoading();
    wx.showToast({ title: '测试完成，查看控制台', icon: 'success' });
  },

  // 显示存储数据
  showStorageData() {
    const keys = ['local_schedules', 'local_expenses', 'local_todos', 'recent_inputs'];
    let summary = '';
    
    keys.forEach(key => {
      const data = wx.getStorageSync(key) || [];
      const count = Array.isArray(data) ? data.length : (typeof data === 'object' ? Object.keys(data).length : 0);
      summary += `${key}: ${count}条\n`;
    });
    
    wx.showModal({
      title: '存储数据概览',
      content: summary,
      showCancel: false
    });
  },

  // 清空所有数据
  clearAllData() {
    wx.showModal({
      title: '⚠️ 危险操作',
      content: '确定要清空所有本地数据吗？此操作不可恢复！',
      confirmText: '确定清空',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          const keys = ['local_schedules', 'local_expenses', 'local_todos', 'recent_inputs'];
          keys.forEach(key => wx.removeStorageSync(key));
          this.setData({ recentInputs: [] });
          wx.showToast({ title: '已清空所有数据', icon: 'success' });
        }
      }
    });
  },

  // 🔄 UI事件处理
  onFabClick() {
    this.startVoiceInput();
  },

  onTextInputClick() {
    this.showTextInput();
  },

  onRecentItemClick(e) {
    const { content, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '最近输入',
      content: `${content}\n\n类型：${this.getTypeDisplayName(type)}`,
      confirmText: '重新处理',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.processTextInput(content);
        }
      }
    });
  },

  onDebugClick() {
    this.showDebugMenu();
  },

  onClearRecentClick() {
    this.clearRecentInputs();
  },

  // 🔧 工具方法
  updateStatus() {
    this.setData({
      aiEnabled: aiService.isConfigured(),
      voiceEnabled: true
    });
  },

  getTypeDisplayName(type) {
    const names = {
      'schedule': '日程',
      'finance': '财务记录',
      'todo': '待办事项'
    };
    return names[type] || '未知';
  },

  getPriorityFromClassification(classification) {
    if (classification.priority) {
      const priorityMap = { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 };
      return priorityMap[classification.priority] || 3;
    }
    return 3; // 默认中等优先级
  },

  formatTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }
}); 