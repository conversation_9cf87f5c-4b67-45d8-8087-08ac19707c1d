/* pages/input/input.wxss */

/* 全局容器样式 */
.container {
  padding: 20rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  padding: 20rpx 0 30rpx;
  position: relative;
}

.header-top {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.logout-btn {
  font-size: 24rpx;
  color: #999;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 用户信息样式 */
.user-info {
  margin-top: 20rpx;
  padding: 15rpx 0;
}

.user-info-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.user-info-detail {
  font-size: 22rpx;
  color: #999;
  margin-top: 5rpx;
}

/* AI状态指示器 */
.ai-status-indicator {
  display: flex;
  margin-top: 20rpx;
  align-items: center;
  flex-wrap: wrap;
}

.ai-status {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  margin-right: 20rpx;
}

.ai-enhanced {
  background-color: #e6f7ff;
  color: #1890ff;
}

.ai-local {
  background-color: #f5f5f5;
  color: #666;
}

.ai-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.ai-text {
  font-size: 24rpx;
}

/* 🎤 语音状态指示器 */
.voice-status {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.voice-enabled {
  background-color: #f0f9eb;
  color: #52c41a;
}

.voice-disabled {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.voice-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.voice-text {
  font-size: 24rpx;
}

/* 🎤 语音状态区域 */
.voice-status-section {
  margin: 20rpx 0;
}

.voice-status-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.voice-status-card.recording {
  background-color: #fff8e6;
  border-left: 8rpx solid #faad14;
}

.voice-status-card.processing {
  background-color: #e6f7ff;
  border-left: 8rpx solid #1890ff;
}

.voice-status-card.error {
  background-color: #fff2f0;
  border-left: 8rpx solid #ff4d4f;
}

.voice-status-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.voice-status-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.voice-status-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.voice-confidence {
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.confidence-text {
  font-size: 24rpx;
  color: #666;
}

.voice-result-text {
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.result-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.voice-controls {
  display: flex;
  justify-content: center;
  margin-top: 16rpx;
}

.voice-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.voice-control-btn.stop {
  background-color: #ff4d4f;
  color: white;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* AI功能卡片 */
.ai-intro-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ai-intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.ai-intro-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.ai-intro-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.ai-status-badge {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.status-enhanced {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-local {
  background-color: #f5f5f5;
  color: #666;
}

.ai-intro-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.ai-capabilities {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.capability-item {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin: 10rpx;
  background-color: #f5f5f5;
}

.capability-item.enhanced {
  background-color: #e6f7ff;
}

.capability-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.capability-text {
  font-size: 24rpx;
  color: #666;
}

/* 输入方式部分 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-section {
  margin-bottom: 30rpx;
}

.input-methods {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.input-method {
  flex: 1;
  min-width: calc(50% - 20rpx);
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

/* 🎤 语音输入方式特殊样式 */
.voice-method {
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.voice-method.recording {
  background-color: #fff8e6;
  border-color: #faad14;
}

.voice-method.processing {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.voice-icon {
  font-size: 48rpx;
  position: relative;
}

.voice-status-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  font-size: 18rpx;
}

.voice-status-indicator.enabled {
  background-color: #f0f9eb;
  color: #52c41a;
}

.voice-status-indicator.disabled {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.method-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.method-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.ai-method {
  background-color: #f0f9ff;
}

/* 快速操作 */
.quick-section {
  margin-bottom: 30rpx;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 20rpx;
  border: 3rpx solid #007aff;
}

.quick-section .section-title {
  color: #007aff;
  font-weight: bold;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.quick-action {
  flex: 1;
  min-width: calc(50% - 20rpx);
  background-color: white;
  border-radius: 16rpx;
  padding: 25rpx;
  margin: 10rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.quick-action:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 56rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 最近输入记录 */
.recent-section {
  margin-bottom: 30rpx;
}

.clear-btn {
  font-size: 24rpx;
  color: #999;
}

.recent-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.recent-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.recent-icon.schedule {
  background-color: #e6f7ff;
}

.recent-icon.expense, .recent-icon.finance {
  background-color: #fff7e6;
}

.recent-icon.todo {
  background-color: #f6ffed;
}

.recent-emoji {
  font-size: 40rpx;
}

.recent-content {
  flex: 1;
  overflow: hidden;
}

.recent-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-time {
  font-size: 24rpx;
  color: #999;
  display: inline-block;
  margin-right: 16rpx;
}

.recent-type-tag {
  font-size: 22rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  color: #666;
}

.recent-actions {
  display: flex;
  align-items: center;
  padding: 0 10rpx;
}

.action-more {
  font-size: 40rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
}

/* 🎤 语音输入引导 */
.empty-guide {
  margin-top: 20rpx;
}

.guide-btn {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.voice-guide {
  background-color: #f0f9eb;
  color: #52c41a;
}

.guide-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.guide-text {
  font-size: 28rpx;
}

/* 浮动按钮调试提示 */
.fab-debug-tip {
  position: fixed;
  right: 160rpx;
  bottom: 80rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10rpx 16rpx;
  border-radius: 8rpx;
  z-index: 9;
  animation: fadeInOut 3s infinite;
}

.debug-tip-text {
  font-size: 24rpx;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 浮动按钮 */
.fab {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(24, 144, 255, 0.3);
  z-index: 10;
  transition: all 0.3s;
}

.fab.recording {
  background-color: #ff4d4f;
  animation: pulse 1.5s infinite;
}

.fab-icon {
  font-size: 50rpx;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx rgba(255, 77, 79, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4rpx 30rpx rgba(255, 77, 79, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx rgba(255, 77, 79, 0.3);
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

.action-icon.note {
  background-color: #f6f6f6;
}

.action-icon.debug {
  background-color: #fff7e6;
}

.debug-action {
  border: 2rpx dashed #faad14;
  background-color: #fff7e6 !important;
  position: relative;
  transform: scale(1.02);
}

.debug-action::before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(45deg, #faad14, #ffc53d);
  border-radius: 20rpx;
  z-index: -1;
  opacity: 0.3;
}

.debug-action .action-text {
  color: #fa8c16;
  font-weight: bold;
  font-size: 26rpx;
}

.debug-action .action-icon {
  background-color: #faad14;
  color: white;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  animation: debugGlow 2s infinite;
}

@keyframes debugGlow {
  0%, 100% { 
    box-shadow: 0 0 10rpx rgba(250, 173, 20, 0.5);
  }
  50% { 
    box-shadow: 0 0 20rpx rgba(250, 173, 20, 0.8);
  }
} 