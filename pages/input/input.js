// pages/input/input.js - 一句话全能助手核心页面（基于蓝皮书v25.05重构）

const app = getApp();
const dataApi = require('../../utils/dataApi');
const aiService = require('../../utils/aiService');

Page({
  data: {
    // UI状态
    voiceInputVisible: false,
    isRecording: false,
    
    // 服务状态
    aiEnabled: true,
    voiceEnabled: true,
    
    // 数据
    recentInputs: [],
    
    // 调试模式（开发时启用，生产时禁用）
    debugMode: true
  },

  async onLoad() {
    console.log('🚀 一句话全能助手启动');
    await this.initServices();
    await this.loadRecentInputs();
  },

  async onShow() {
    await this.loadRecentInputs();
    this.updateStatus();
  },

  // 🔧 核心服务初始化
  async initServices() {
    try {
      // 初始化数据API
      if (dataApi && dataApi.init) {
        await dataApi.init();
        console.log('✅ 数据服务初始化成功');
      }

      // 初始化AI服务
      if (aiService && aiService.isConfigured()) {
        console.log('✅ AI服务已配置');
        this.setData({ aiEnabled: true });
      } else {
        console.log('⚠️ AI服务未配置');
        this.setData({ aiEnabled: false });
      }

      // 初始化语音服务
      this.setData({ voiceEnabled: true });
      
    } catch (error) {
      console.error('❌ 服务初始化失败:', error);
    }
  },

  // 🎤 核心功能：语音输入（使用新的录音管理器API）
  async startVoiceInput() {
    if (!this.data.voiceEnabled) {
      wx.showToast({ title: '语音服务不可用', icon: 'error' });
      return;
    }

    try {
      // 检查录音权限
      const hasPermission = await this.checkRecordPermission();
      if (!hasPermission) {
        wx.showToast({ title: '需要录音权限', icon: 'none' });
        return;
      }

      this.setData({
        voiceInputVisible: true,
        isRecording: true
      });

      console.log('🎤 开始语音识别...');

      // 初始化录音管理器
      if (!this.recordManager) {
        this.recordManager = wx.getRecorderManager();
        this.setupRecorderEvents();
      }

      // 开始录音
      this.recordManager.start({
        duration: 10000, // 最长10秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 48000,
        format: 'mp3'
      });

    } catch (error) {
      console.error('❌ 语音输入启动失败:', error);
      this.handleVoiceError('语音服务启动失败');
    }
  },

  // 设置录音管理器事件
  setupRecorderEvents() {
    this.recordManager.onStart(() => {
      console.log('📱 录音开始');
    });

    this.recordManager.onStop((res) => {
      console.log('🎤 录音结束，开始识别...', res);
      this.setData({ isRecording: false });
      this.processVoiceRecord(res);
    });

    this.recordManager.onError((error) => {
      console.error('❌ 录音错误:', error);
      this.handleVoiceError('录音失败');
    });
  },

  stopVoiceInput() {
    if (this.recordManager && this.data.isRecording) {
      this.recordManager.stop();
    }
  },

  // 检查录音权限
  async checkRecordPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === true) {
            resolve(true);
          } else if (res.authSetting['scope.record'] === false) {
            // 权限被拒绝，引导用户打开设置
            wx.showModal({
              title: '需要录音权限',
              content: '语音输入功能需要录音权限，请在设置中开启',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting();
                }
              }
            });
            resolve(false);
          } else {
            // 首次请求权限
            wx.authorize({
              scope: 'scope.record',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: () => resolve(false)
      });
    });
  },

  // 处理语音记录（使用云函数或本地转换）
  async processVoiceRecord(audioData) {
    try {
      wx.showLoading({ title: '识别中...' });

      // 尝试使用云函数进行语音识别
      try {
        const result = await wx.cloud.callFunction({
          name: 'speechRecognition',
          data: {
            audioPath: audioData.tempFilePath,
            duration: audioData.duration,
            fileSize: audioData.fileSize
          }
        });

        if (result.result && result.result.success) {
          const text = result.result.data.text;
          console.log('🎯 云端语音识别成功:', text);
          wx.hideLoading();
          this.setData({ voiceInputVisible: false });
          this.processTextInput(text);
          return;
        }
      } catch (cloudError) {
        console.warn('☁️ 云端识别失败，尝试本地方案:', cloudError);
      }

      // 云函数失败，降级到手动输入
      wx.hideLoading();
      this.setData({ voiceInputVisible: false });

      wx.showModal({
        title: '语音识别失败',
        content: '语音识别服务暂时不可用，是否手动输入？',
        confirmText: '手动输入',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.showTextInput();
          }
        }
      });

    } catch (error) {
      console.error('❌ 处理语音记录失败:', error);
      wx.hideLoading();
      this.handleVoiceError('语音识别失败');
    }
  },

  // 处理语音错误
  handleVoiceError(message) {
    this.setData({ 
      voiceInputVisible: false, 
      isRecording: false 
    });
    wx.showToast({ 
      title: message, 
      icon: 'error' 
    });
  },

  // 📝 文本输入
  async showTextInput() {
    try {
      const result = await new Promise((resolve, reject) => {
        wx.showModal({
          title: '输入内容',
          editable: true,
          placeholderText: '说说你想记录的内容...',
          success: resolve,
          fail: reject
        });
      });

      if (result.confirm && result.content) {
        await this.processTextInput(result.content);
      }
    } catch (error) {
      console.error('❌ 文本输入失败:', error);
    }
  },

  // 🤖 核心功能：文本处理与AI分类
  async processTextInput(content) {
    if (!content || content.trim().length === 0) {
      wx.showToast({ title: '内容不能为空', icon: 'error' });
      return;
    }

    wx.showLoading({ title: '智能分析中...' });

    try {
      console.log('🤖 开始AI智能分析:', content);

      // 调用AI分类
      const aiResult = await aiService.intelligentClassify(content);
      
      if (aiResult && aiResult.items && aiResult.items.length > 0) {
        const classificationResult = aiResult.items[0];
        console.log('✅ AI分类成功:', classificationResult);
        
        // 根据分类结果创建数据
        await this.createDataFromClassification(content, classificationResult);
        
        // 更新最近输入
        await this.addToRecentInputs(content, classificationResult.primaryType);
        
        wx.hideLoading();
        wx.showToast({ 
          title: `已保存为${this.getTypeDisplayName(classificationResult.primaryType)}`,
          icon: 'success' 
        });
        
      } else {
        throw new Error('AI分类无有效结果');
      }
      
    } catch (error) {
      console.error('❌ AI分析失败:', error);
      wx.hideLoading();
      
      // 降级到手动选择
      this.showManualClassification(content);
    }
  },

  // 根据AI分类创建数据
  async createDataFromClassification(content, classification) {
    const { primaryType } = classification;
    
    try {
      switch (primaryType) {
        case 'schedule':
          await this.createScheduleData(content, classification);
          break;
        case 'finance':
          await this.createFinanceData(content, classification);
          break;
        default:
          await this.createTodoData(content, classification);
      }
      
      console.log(`✅ ${primaryType} 数据创建成功`);
      
    } catch (error) {
      console.error(`❌ 创建${primaryType}数据失败:`, error);
      throw error;
    }
  },

  // 创建日程数据
  async createScheduleData(content, classification) {
    // 🎯 修复字段映射问题，从多个可能位置获取dateTime
    let dateTime = classification.dateTime || 
                   classification.timeInfo?.dateTime || 
                   classification.extractedInfo?.dateTime ||
                   new Date().toISOString();
    
    // 🏢 修复地点获取，从多个可能位置获取location
    let location = classification.location || 
                   classification.extractedInfo?.location || 
                   '';
    
    const scheduleData = {
      title: classification.title || classification.extractedInfo?.title || content,
      description: content,
      start_time: dateTime,
      end_time: new Date(new Date(dateTime).getTime() + 60*60*1000).toISOString(),
      location: location,
      category: classification.category || classification.extractedInfo?.category || '一般',
      source: 'AI智能分析',
      originalText: content,
      aiResult: classification
    };

    console.log('📅 准备创建日程数据:', scheduleData);
    console.log('🤖 AI分类结果详情:', classification);
    
    await dataApi.createSchedule(scheduleData);
    console.log('📅 日程创建成功:', scheduleData);
  },

  // 创建财务数据
  async createFinanceData(content, classification) {
    const financeData = {
      title: classification.title || content,
      amount: classification.financialInfo?.amount || 0,
      type: 'expense',
      category: classification.financialInfo?.category || '其他',
      description: content,
      date: new Date().toISOString().split('T')[0],
      source: 'AI智能分析'
    };

    await dataApi.createExpense(financeData);
    console.log('💰 财务记录创建成功:', financeData);
  },

  // 创建待办数据
  async createTodoData(content, classification) {
    const todoData = {
      title: classification.title || content,
      description: content,
      priority: this.getPriorityFromClassification(classification),
      category: classification.category || '一般',
      due_date: classification.dateTime || null,
      source: 'AI智能分析'
    };

    await dataApi.createTodo(todoData);
    console.log('✅ 待办创建成功:', todoData);
  },

  // 手动分类选择
  showManualClassification(content) {
    wx.showActionSheet({
      itemList: ['📅 保存为日程', '💰 保存为财务记录', '✅ 保存为待办事项'],
      success: async (res) => {
        const types = ['schedule', 'finance', 'todo'];
        const selectedType = types[res.tapIndex];
        
        try {
          wx.showLoading({ title: '保存中...' });
          
          const basicClassification = {
            primaryType: selectedType,
            title: content,
            category: '一般'
          };
          
          await this.createDataFromClassification(content, basicClassification);
          await this.addToRecentInputs(content, selectedType);
          
          wx.hideLoading();
          wx.showToast({ 
            title: `已保存为${this.getTypeDisplayName(selectedType)}`,
            icon: 'success' 
          });
          
        } catch (error) {
          wx.hideLoading();
          wx.showToast({ title: '保存失败', icon: 'error' });
          console.error('❌ 手动分类保存失败:', error);
        }
      }
    });
  },

  // 🔄 最近输入管理
  async loadRecentInputs() {
    try {
      const recentInputs = wx.getStorageSync('recent_inputs') || [];
      this.setData({ recentInputs });
    } catch (error) {
      console.error('❌ 加载最近输入失败:', error);
    }
  },

  async addToRecentInputs(content, type) {
    try {
      const recentInputs = wx.getStorageSync('recent_inputs') || [];
      
      const newInput = {
        id: Date.now(),
        content,
        type,
        timestamp: new Date().toISOString()
      };

      // 添加到开头，保留最近20条
      recentInputs.unshift(newInput);
      const limitedInputs = recentInputs.slice(0, 20);
      
      wx.setStorageSync('recent_inputs', limitedInputs);
      this.setData({ recentInputs: limitedInputs });
      
    } catch (error) {
      console.error('❌ 保存最近输入失败:', error);
    }
  },

  // 清空最近输入
  clearRecentInputs() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有最近输入吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('recent_inputs');
          this.setData({ recentInputs: [] });
          wx.showToast({ title: '已清空', icon: 'success' });
        }
      }
    });
  },

  // 🔧 调试功能（仅在开发模式下显示）
  showDebugMenu() {
    if (!this.data.debugMode) return;
    
    wx.showActionSheet({
      itemList: [
        '🤖 测试AI分类',
        '�� 查看存储数据',
        '🗓️ 测试相对日期计算',
        '🔄 重新初始化服务'
      ],
      success: (res) => {
        const actions = [
          () => this.testAIClassification(),
          () => this.showStorageData(),
          () => this.testRelativeDateCalculation(),
          () => this.initServices()
        ];
        actions[res.tapIndex]();
      }
    });
  },

  // 测试AI分类
  async testAIClassification() {
    const testTexts = [
      '明天开会',
      '买咖啡花了15元',
      '7天后去杭州',
      '记得完成报告',
      '5天后回北京',
      '三天后开会议'
    ];
    
    wx.showLoading({ title: '测试中...' });
    
    console.log('🧪 === 开始AI分类测试 ===');
    console.log('📅 当前时间:', new Date().toISOString());
    
    for (const text of testTexts) {
      try {
        console.log(`\n🧪 测试文本: "${text}"`);
        const result = await aiService.intelligentClassify(text);
        console.log(`✅ 原始AI结果:`, result);
        
        if (result.success && result.items && result.items.length > 0) {
          const classification = result.items[0];
          console.log(`📊 分类结果:`, classification);
          
          // 检查相对日期处理
          if (text.includes('天后') || text.includes('明天')) {
            console.log(`🗓️ 相对日期检查:`);
            console.log(`   - 原始文本: ${text}`);
            console.log(`   - dateTime: ${classification.dateTime}`);
            console.log(`   - timeInfo.dateTime: ${classification.timeInfo?.dateTime}`);
            console.log(`   - extractedInfo.dateTime: ${classification.extractedInfo?.dateTime}`);
            console.log(`   - location: ${classification.location || classification.extractedInfo?.location || '无'}`);
          }
        } else {
          console.log(`❌ AI分类失败，无有效结果`);
        }
        
      } catch (error) {
        console.error(`❌ 测试失败: ${text}`, error);
      }
    }
    
    wx.hideLoading();
    wx.showToast({ title: '测试完成，查看控制台', icon: 'success' });
  },

  // 显示存储数据
  showStorageData() {
    const keys = ['local_schedules', 'local_expenses', 'local_todos', 'recent_inputs'];
    let summary = '';
    
    keys.forEach(key => {
      const data = wx.getStorageSync(key) || [];
      const count = Array.isArray(data) ? data.length : (typeof data === 'object' ? Object.keys(data).length : 0);
      summary += `${key}: ${count}条\n`;
    });
    
    wx.showModal({
      title: '存储数据概览',
      content: summary,
      showCancel: false
    });
  },

  // 🗓️ 测试相对日期计算
  testRelativeDateCalculation() {
    console.log('🗓️ === 相对日期计算测试 ===');
    const now = new Date();
    console.log('当前时间:', now.toISOString());
    
    const testCases = [
      { text: '明天', days: 1 },
      { text: '后天', days: 2 },
      { text: '3天后', days: 3 },
      { text: '5天后', days: 5 },
      { text: '7天后', days: 7 },
      { text: '十天后', days: 10 }
    ];
    
    testCases.forEach(testCase => {
      const targetDate = new Date(now.getTime() + testCase.days * 24 * 60 * 60 * 1000);
      // 设置为下午14:00
      targetDate.setHours(14, 0, 0, 0);
      console.log(`${testCase.text} → ${targetDate.toISOString()}`);
    });
    
    wx.showToast({ title: '查看控制台输出', icon: 'success' });
  },

  // 🔄 UI事件处理
  onFabClick() {
    this.startVoiceInput();
  },

  onTextInputClick() {
    this.showTextInput();
  },

  onRecentItemClick(e) {
    const { content, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '最近输入',
      content: `${content}\n\n类型：${this.getTypeDisplayName(type)}`,
      confirmText: '重新处理',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.processTextInput(content);
        }
      }
    });
  },

  onDebugClick() {
    this.showDebugMenu();
  },

  onClearRecentClick() {
    this.clearRecentInputs();
  },

  // 🔧 工具方法
  updateStatus() {
    this.setData({
      aiEnabled: aiService.isConfigured(),
      voiceEnabled: true
    });
  },

  getTypeDisplayName(type) {
    const names = {
      'schedule': '日程',
      'finance': '财务记录',
      'todo': '待办事项'
    };
    return names[type] || '未知';
  },

  getPriorityFromClassification(classification) {
    if (classification.priority) {
      const priorityMap = { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 };
      return priorityMap[classification.priority] || 3;
    }
    return 3; // 默认中等优先级
  },

  formatTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }
});