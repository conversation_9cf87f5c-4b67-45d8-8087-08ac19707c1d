// pages/input/input.js
import dataApi from '../../utils/dataApi.js';
import aiService from '../../utils/aiService.js';
import voiceService from '../../utils/voiceService.js';
Page({
  data: {
    // 最近输入记录
    recentInputs: [],
    // 用户显示文本
    userDisplayText: '加载中...',
    // 数据加载状态
    loading: false,
    // 同步状态
    syncStatus: null,
    // AI服务状态
    aiStatus: {
      configured: false,
      enabled: false,
      source: 'local'
    },
    // 🎤 语音输入状态
    voiceStatus: {
      isRecording: false,
      isProcessing: false,
      currentText: '',
      confidence: 0,
      source: 'idle',
      hasPermission: false
    }
  },
  // 简单测试添加日程按钮响应
  testAddScheduleButton() {
    console.log('🔍 测试添加日程按钮被点击');
    // 直接执行数据创建测试
    wx.showModal({
      title: '🧪 立即测试数据创建',
      content: '这将测试创建一个简单的日程和待办，是否继续？',
      confirmText: '开始测试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.testBasicDataCreation();
        }
      }
    });
  },
  // 🔍 测试浮动按钮点击
  testFabClick() {
    console.log('🔍 === 浮动按钮点击测试 ===');
    console.log('✅ 浮动按钮点击事件正常工作!');
    wx.showToast({
      title: '浮动按钮工作正常！',
      icon: 'success',
      duration: 2000
    });
    // 延迟1秒后调用showAddMenu
    setTimeout(() => {
      console.log('🔄 1秒后调用showAddMenu...');
      this.showAddMenu();
    }, 1000);
  },
  async onLoad() {
    console.log('=== 输入页面 onLoad ===');
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    // 初始化数据API
    await this.initDataApi();
    // 初始化AI服务
    await this.initAIService();
    // 🎤 初始化语音服务
    await this.initVoiceService();
    // 显示当前用户信息
    this.showCurrentUser();
    // 加载最近输入记录
    await this.loadRecentInputs();
  },
  async onShow() {
    console.log('=== 输入页面 onShow ===');
    
    // 🔧 调试：检查快速操作区域
    console.log('🔧 === 快速操作区域调试 ===');
    console.log('📋 快速操作区域应该包含以下按钮:');
    console.log('1. 📅 添加日程');
    console.log('2. ✅ 添加待办'); 
    console.log('3. 📝 记录笔记');
    console.log('4. 🔧 系统调试 (橙色高亮)');
    
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    // 显示当前用户信息
    this.showCurrentUser();
    // 刷新最近输入记录
    await this.refreshRecentInputs();
    // 更新同步状态
    this.updateSyncStatus();
    // 更新AI服务状态
    this.updateAIStatus();
    // 🎤 更新语音状态
    this.updateVoiceStatus();
    
    // 🔧 在页面加载完成后显示调试提示
    setTimeout(() => {
      wx.showToast({
        title: '🔧 快速操作区域已加载',
        icon: 'none',
        duration: 2000
      });
    }, 1000);
  },
  // 初始化数据API
  async initDataApi() {
    try {
      const success = await dataApi.init();
      if (success) {
        console.log('数据API初始化成功');
      } else {
        console.warn('数据API初始化失败，将使用降级方案');
      }
    } catch (error) {
      console.error('数据API初始化出错:', error);
    }
  },
  // 初始化AI服务
  async initAIService() {
    try {
      // 检查AI服务状态（现在由aiService.js管理配置）
      const aiStatus = aiService.getStatus();
      console.log('🤖 AI服务当前状态:', aiStatus);
      this.updateAIStatus();
    } catch (error) {
      console.error('AI服务初始化失败:', error);
    }
  },
  // 🎤 初始化语音服务
  async initVoiceService() {
    try {
      console.log('🎤 初始化语音服务...');
      // 初始化语音服务
      const success = await voiceService.init();
      if (success) {
        console.log('✅ 语音服务初始化成功');
        // 绑定语音事件监听
        this.bindVoiceEvents();
        // 检查录音权限
        const hasPermission = await this.checkVoicePermission();
        this.setData({
          'voiceStatus.hasPermission': hasPermission
        });
      } else {
        console.warn('⚠️ 语音服务初始化失败');
      }
    } catch (error) {
      console.error('❌ 语音服务初始化出错:', error);
    }
  },
  // 🎤 绑定语音事件监听
  bindVoiceEvents() {
    // 录音开始
    voiceService.on('recordStart', () => {
      console.log('🎤 录音开始事件');
      this.setData({
        'voiceStatus.isRecording': true,
        'voiceStatus.isProcessing': false,
        'voiceStatus.currentText': '正在录音...',
        'voiceStatus.source': 'recording'
      });
    });
    // 录音结束
    voiceService.on('recordStop', () => {
      console.log('⏹️ 录音结束事件');
      this.setData({
        'voiceStatus.isRecording': false,
        'voiceStatus.isProcessing': true,
        'voiceStatus.currentText': '正在识别...',
        'voiceStatus.source': 'processing'
      });
    });
    // ASR实时结果
    voiceService.on('asrResult', (res) => {
      console.log('🎯 ASR实时结果:', res);
      if (res && res.result) {
        this.setData({
          'voiceStatus.currentText': res.result,
          'voiceStatus.confidence': res.confidence || 0.8
        });
      }
    });
    // 最终语音识别结果
    voiceService.on('voiceResult', (result) => {
      console.log('✅ 语音识别完成:', result);
      this.handleVoiceResult(result);
    });
    // 录音错误
    voiceService.on('recordError', (error) => {
      console.error('❌ 录音错误:', error);
      this.handleVoiceError('录音失败：' + error.errMsg);
    });
    // ASR错误
    voiceService.on('asrError', (error) => {
      console.error('❌ ASR错误:', error);
      this.handleVoiceError('语音识别失败');
    });
    // 通用错误
    voiceService.on('error', (error) => {
      console.error('❌ 语音服务错误:', error);
      this.handleVoiceError(error.message || '语音服务出错');
    });
    // 备用输入提示
    voiceService.on('fallbackInput', (data) => {
      console.log('🔄 启用备用输入:', data);
      this.handleVoiceFallback(data);
    });
  },
  // 🎤 检查语音权限
  async checkVoicePermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const hasPermission = res.authSetting['scope.record'] === true;
          resolve(hasPermission);
        },
        fail: () => resolve(false)
      });
    });
  },
  // 🎤 开始语音输入
  async startVoiceInput() {
    try {
      console.log('🎤 开始语音输入');
      // 检查权限
      if (!this.data.voiceStatus.hasPermission) {
        const hasPermission = await this.checkVoicePermission();
        if (!hasPermission) {
          wx.showModal({
            title: '需要录音权限',
            content: '语音输入需要录音权限，请在设置中开启',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
          return;
        }
        this.setData({ 'voiceStatus.hasPermission': true });
      }
      // 开始录音
      const success = await voiceService.startRecording();
      if (!success) {
        this.handleVoiceError('启动录音失败');
      }
    } catch (error) {
      console.error('❌ 开始语音输入失败:', error);
      this.handleVoiceError('语音输入启动失败');
    }
  },
  // 🎤 停止语音输入
  stopVoiceInput() {
    try {
      console.log('⏹️ 停止语音输入');
      voiceService.stopRecording();
    } catch (error) {
      console.error('❌ 停止语音输入失败:', error);
      this.handleVoiceError('停止录音失败');
    }
  },
  // 🎤 处理语音识别结果
  async handleVoiceResult(result) {
    try {
      console.log('🎯 处理语音识别结果:', result);
      // 更新状态
      this.setData({
        'voiceStatus.isRecording': false,
        'voiceStatus.isProcessing': false,
        'voiceStatus.currentText': result.result,
        'voiceStatus.confidence': result.confidence || 0.7,
        'voiceStatus.source': result.source || 'unknown'
      });
      // 显示识别结果和确认对话框
      const confirmResult = await this.showVoiceConfirmDialog(result);
      if (confirmResult.confirm) {
        // 用户确认，使用AI处理文本
        const finalText = confirmResult.text || result.result;
        await this.processTextInputWithAI(finalText);
      }
    } catch (error) {
      console.error('❌ 处理语音结果失败:', error);
      this.handleVoiceError('处理语音结果失败');
    }
  },
  // 🎤 显示语音确认对话框
  showVoiceConfirmDialog(result) {
    return new Promise((resolve) => {
      const confidenceText = `置信度: ${Math.round(result.confidence * 100)}%`;
      const sourceText = result.source === 'tencent_asr' ? '腾讯ASR' : 
                        result.source === 'cloud_recognition' ? '云端识别' : '备用识别';
      wx.showModal({
        title: '语音识别结果',
        content: `识别文本: ${result.result}\n${confidenceText}\n识别来源: ${sourceText}\n\n是否使用此文本进行AI分析？`,
        confirmText: '确认使用',
        cancelText: '重新输入',
        success: (res) => {
          if (res.confirm) {
            resolve({ confirm: true, text: result.result });
          } else {
            // 用户选择重新输入，显示文本输入框
            this.showTextInput();
            resolve({ confirm: false });
          }
        }
      });
    });
  },
  // 🎤 处理语音错误
  handleVoiceError(message) {
    console.error('🚨 语音错误:', message);
    // 重置语音状态
    this.setData({
      'voiceStatus.isRecording': false,
      'voiceStatus.isProcessing': false,
      'voiceStatus.currentText': '识别失败',
      'voiceStatus.source': 'error'
    });
    // 显示错误提示
    wx.showToast({
      title: message,
      icon: 'error',
      duration: 2000
    });
    // 3秒后重置状态
    setTimeout(() => {
      this.setData({
        'voiceStatus.currentText': '',
        'voiceStatus.source': 'idle'
      });
    }, 3000);
  },
  // 🎤 处理语音备用方案
  handleVoiceFallback(data) {
    // 重置语音状态
    this.setData({
      'voiceStatus.isRecording': false,
      'voiceStatus.isProcessing': false,
      'voiceStatus.currentText': '',
      'voiceStatus.source': 'idle'
    });
    // 显示手动输入选项
    wx.showModal({
      title: '语音识别失败',
      content: data.message || '语音识别失败，是否手动输入文本？',
      confirmText: '手动输入',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.showTextInput();
        }
      }
    });
  },
  // 🎤 更新语音状态
  updateVoiceStatus() {
    const state = voiceService.getState();
    this.setData({
      'voiceStatus.isRecording': state === 'recording',
      'voiceStatus.isProcessing': state === 'processing'
    });
  },
  // 🎯 显示语音输入
  showVoiceInput() {
    console.log('🎤 显示语音输入界面');
    const isRecording = this.data.voiceStatus.isRecording;
    const isProcessing = this.data.voiceStatus.isProcessing;
    if (isRecording) {
      // 正在录音，显示停止选项
      wx.showModal({
        title: '正在录音中',
        content: '点击确定停止录音并开始识别',
        confirmText: '停止录音',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.stopVoiceInput();
          }
        }
      });
    } else if (isProcessing) {
      // 正在处理，显示状态
      wx.showToast({
        title: '正在识别中，请稍候',
        icon: 'loading',
        duration: 2000
      });
    } else {
      // 空闲状态，开始录音
      this.startVoiceInput();
    }
  },
  // 更新AI服务状态
  updateAIStatus() {
    const status = aiService.getStatus();
    this.setData({
      aiStatus: {
        configured: status.configured,
        enabled: status.enabled,
        source: status.configured ? 'deepseek' : 'local'
      }
    });
  },
  // 显示当前用户信息
  showCurrentUser() {
    const app = getApp();
    // 更新界面显示
    let displayText = '未登录';
    if (app.globalData.isLoggedIn) {
      if (app.globalData.isGuest) {
        displayText = '访客模式';
      } else if (app.globalData.userInfo && app.globalData.userInfo.nick) {
        displayText = `欢迎，${app.globalData.userInfo.nick}`;
      } else {
        displayText = `用户 ${app.globalData.uid.slice(-8)}`;
      }
    }
    this.setData({
      userDisplayText: displayText
    });
  },
  // 加载最近输入记录
  async loadRecentInputs() {
    if (this.data.loading) return;
    this.setData({ loading: true });
    try {
      console.log('📋 开始加载最近输入记录...');
      
      // 🔧 首先尝试从持久化存储中恢复
      const persistentInputs = wx.getStorageSync('persistent_recent_inputs');
      if (persistentInputs && persistentInputs.length > 0) {
        console.log('✅ 从持久化存储恢复最近输入记录:', persistentInputs.length);
        this.setData({
          recentInputs: persistentInputs,
          loading: false
        });
        // 异步重新构建，确保数据最新
        setTimeout(() => {
          this.rebuildRecentInputs();
        }, 500);
        return;
      }
      
      // 🔧 如果没有持久化数据，从数据API获取
      let recentRecords = [];
      try {
        recentRecords = await dataApi.getRecentRecords(10);
        console.log('📊 从dataApi获取到记录:', recentRecords.length);
      } catch (error) {
        console.warn('⚠️ dataApi获取失败，使用本地存储:', error);
        recentRecords = [];
      }
      
      // 🔧 如果dataApi也没有数据，直接从本地存储构建
      if (recentRecords.length === 0) {
        console.log('📋 从本地存储重新构建最近输入记录...');
        await this.rebuildRecentInputs();
        this.setData({ loading: false });
        return;
      }
      
      // 🔧 处理从dataApi获取的记录
      const recentInputs = recentRecords.map((record) => ({
        id: record.id,
        content: this.getRecordDisplayText(record),
        time: this.formatTime(record.createdAt),
        type: record.type,
        typeText: this.getTypeText(record.type),
        icon: this.getTypeIcon(record.type),
        originalData: record
      }));
      
      this.setData({
        recentInputs,
        loading: false
      });
      
      // 🔒 保存到持久化存储
      wx.setStorageSync('persistent_recent_inputs', recentInputs);
      console.log('✅ 最近输入记录加载完成:', recentInputs.length);
      
    } catch (error) {
      console.error('❌ 加载最近输入记录失败:', error);
      this.setData({ loading: false });
      
      // 🔧 出错时也尝试重新构建
      try {
        await this.rebuildRecentInputs();
      } catch (rebuildError) {
        console.error('❌ 重新构建也失败:', rebuildError);
      }
    }
  },
  // 刷新最近输入记录
  async refreshRecentInputs() {
    try {
      console.log('🔄 刷新最近输入记录...');
      
      // 重新加载最近输入记录
      await this.loadRecentInputs();
      
      console.log('✅ 最近输入记录刷新完成');
    } catch (error) {
      console.error('❌ 刷新最近输入记录失败:', error);
    }
  },
  // 更新同步状态
  updateSyncStatus() {
    const syncStatus = dataApi.getSyncStatus();
    this.setData({ syncStatus });
  },
  // 获取记录显示文本
  getRecordDisplayText(record) {
    switch (record.type) {
      case 'schedule':
        return `${record.title} - ${record.time}`;
      case 'expense':
        return `${record.description} - ¥${record.amount}`;
      case 'todo':
        return record.title;
      default:
        return record.title || record.description || '记录';
    }
  },
  // 获取类型文本
  getTypeText(type) {
    const typeMap = {
      'schedule': '日程',
      'expense': '支出',
      'income': '收入',
      'todo': '待办'
    };
    return typeMap[type] || '记录';
  },
  // 🔧 获取类型图标
  getTypeIcon(type) {
    const iconMap = {
      'schedule': '📅',
      'expense': '💰',
      'income': '💵',
      'todo': '✅',
      'finance': '💰'
    };
    return iconMap[type] || '📝';
  },
  // 格式化时间
  formatTime(timeString) {
    try {
      const date = new Date(timeString);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      if (diffMins < 1) return '刚刚';
      if (diffMins < 60) return `${diffMins}分钟前`;
      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
      return `${Math.floor(diffMins / 1440)}天前`;
    } catch (error) {
      return '未知时间';
    }
  },
  // 显示文本输入
  async showTextInput() {
    console.log('📝 显示文本输入');
    wx.showModal({
      title: '文本输入',
      content: '请描述您要添加的内容\n\n💡提示：输入"测试"可进行数据创建测试',
      editable: true,
      placeholderText: '例如：明天下午2点开会 或 测试',
      success: async (res) => {
        if (res.confirm && res.content) {
          await this.processTextInputWithAI(res.content);
        }
      }
    });
  },
  // 使用AI处理文本输入
  async processTextInputWithAI(content) {
    try {
      console.log('🤖 开始处理文本:', content);
      if (!content || content.trim().length === 0) {
        wx.showToast({
          title: '请输入有效内容',
          icon: 'error'
        });
        return;
      }
      // 显示处理中
      wx.showLoading({
        title: '处理中...',
        mask: true
      });
      // 🎯 优先使用AI服务进行智能分析
      console.log('🤖 优先使用AI服务进行智能分析');
      await this.processTextWithAI(content.trim());
      wx.hideLoading();
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 文本处理失败:', error);
      wx.showToast({
        title: '处理失败，请重试',
        icon: 'error'
      });
    }
  },
  // 🎯 使用AI服务处理文本
  async processTextWithAI(content) {
    console.log('🤖 === 开始AI分类流程 ===');
    console.log('📝 输入内容:', content);
    
    try {
      // 🧪 简单测试模式
      if (content === '测试' || content === 'test') {
        console.log('🧪 检测到测试指令，开始简单测试...');
        await this.createSimpleTestData();
        return;
      }

      // 🎯 核心AI分类 - 这是最重要的部分
      console.log('🤖 开始调用AI分类服务...');
      const aiResult = await aiService.intelligentClassify(content);
      console.log('✅ AI分类完成:', aiResult);

      if (!aiResult || !aiResult.items || aiResult.items.length === 0) {
        throw new Error('AI分类返回空结果');
      }

      // 🎯 处理AI分类结果
      console.log('🎯 开始处理AI分类结果...');
      const createdItems = await this.handleAIClassificationResult(aiResult);
      
      if (createdItems && createdItems.length > 0) {
        console.log('✅ 数据创建成功:', createdItems.length, '条');
        
        // 显示成功提示
        const aiItem = aiResult.items[0];
        const typeText = aiItem.primaryType === 'finance' ? '财务记录' : 
                        aiItem.primaryType === 'schedule' ? '日程' : '待办';
        
        wx.showToast({
          title: `${typeText}创建成功`,
          icon: 'success'
        });

        // 如果是未来事件，显示特殊提示
        if (aiItem.isFuture) {
          setTimeout(() => {
            wx.showToast({
              title: '已同时创建待办提醒',
              icon: 'success'
            });
          }, 1500);
        }
      } else {
        console.log('⚠️ 没有创建任何数据项');
        wx.showToast({
          title: '处理完成',
          icon: 'success'
        });
      }

      // 刷新界面数据
      await this.refreshRecentInputs();

      // 通知其他页面刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshSchedules = true;
        app.globalData.shouldRefreshTodos = true;
        app.globalData.shouldRefreshFinances = true;
      }

      console.log('🎉 AI分类流程完成');

    } catch (error) {
      console.error('❌ AI分类流程失败:', error);
      
      // 降级到本地处理
      console.log('🔄 开始降级处理...');
      try {
        await this.processTextInputLocally(content);
        console.log('✅ 降级处理成功');
      } catch (fallbackError) {
        console.error('❌ 降级处理也失败:', fallbackError);
        wx.showModal({
          title: '处理失败',
          content: '文本处理失败，请检查网络连接或稍后重试',
          showCancel: false
        });
      }
    }
  },

  // 🎯 创建简单测试数据
  async createSimpleTestData() {
    try {
      console.log('🧪 创建简单测试数据...');
      
      const testTexts = [
        '明天下午2点开会',
        '买咖啡花了25元', 
        '完成工作报告'
      ];

      wx.showLoading({ title: '创建测试数据...', mask: true });

      let successCount = 0;
      for (const text of testTexts) {
        try {
          console.log(`📝 处理: ${text}`);
          await this.processTextWithAI(text);
          successCount++;
          await this.delay(500);
        } catch (error) {
          console.error(`❌ 处理失败: ${text}`, error);
        }
      }

      wx.hideLoading();
      wx.showModal({
        title: '测试完成',
        content: `成功创建 ${successCount} 条测试数据`,
        showCancel: false,
        success: () => {
          this.refreshRecentInputs();
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 测试数据创建失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },
  // 🎯 显示云函数部署指南
  showCloudFunctionGuide() {
    wx.showModal({
      title: '☁️ 云函数部署指南',
      content: `如需启用云端同步功能：
1️⃣ 打开微信开发者工具
2️⃣ 找到 cloudfunctions/userLogin 文件夹
3️⃣ 右键选择"创建并部署：云端安装依赖"
4️⃣ 等待部署完成后重新登录
⚡ 当前本地模式优势：
• 响应速度快
• 无网络依赖  
• 数据安全私密
• 功能完全可用`,
      confirmText: '我知道了',
      showCancel: false
    });
  },
  // 📅 添加日程（快速操作）
  addSchedule() {
    console.log('📅 快速添加日程');
    wx.showModal({
      title: '📅 添加日程',
      content: '选择添加方式',
      confirmText: '测试数据',
      cancelText: '文本输入',
      success: (res) => {
        if (res.confirm) {
          // 直接测试数据创建
          this.testBasicDataCreation();
        } else {
          // 文本输入
          this.showTextInput();
        }
      }
    });
  },
  // ✅ 添加待办（快速操作）
  addTodo() {
    console.log('✅ 快速添加待办');
    this.processTextInputWithAI('完成工作报告');
  },
  // 📝 添加笔记（快速操作）
  addNote() {
    console.log('📝 快速添加笔记');
    this.showTextInput();
  },
  // 处理最近记录点击
  onRecentClick(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击最近记录:', item);
    // 可以跳转到对应的详情页面
  },
  // 显示最近操作
  showRecentActions(e) {
    e.stopPropagation();
    const item = e.currentTarget.dataset.item;
    console.log('显示最近操作:', item);
  },
  // 清空最近输入
  clearRecentInputs() {
    this.setData({
      recentInputs: []
    });
    wx.showToast({
      title: '已清空',
      icon: 'success'
    });
  },
  // 显示用户详情
  showUserDetail() {
    const app = getApp();
    wx.showModal({
      title: '用户信息',
      content: `用户ID: ${app.globalData.uid}\n登录状态: ${app.globalData.isLoggedIn ? '已登录' : '未登录'}\n模式: ${app.globalData.isGuest ? '访客' : '正常'}`,
      showCancel: false
    });
  },
  // 处理退出登录
  handleLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp();
          app.logout();
          wx.redirectTo({
            url: '/pages/index/index'
          });
        }
      }
    });
  },
  // 🎯 计算下周具体星期几的日期
  calculateNextWeekDate(inputText, today) {
    console.log('🗓️ 计算下周日期，输入文本:', inputText);
    // 星期映射表
    const weekdayMap = {
      '周一': 1, '星期一': 1, '下周一': 1,
      '周二': 2, '星期二': 2, '下周二': 2,
      '周三': 3, '星期三': 3, '下周三': 3,
      '周四': 4, '星期四': 4, '下周四': 4,
      '周五': 5, '星期五': 5, '下周五': 5,
      '周六': 6, '星期六': 6, '下周六': 6,
      '周日': 0, '星期日': 0, '下周日': 0, '周天': 0, '下周天': 0
    };
    let targetWeekday = null;
    // 检查文本中包含的星期几
    Object.keys(weekdayMap).forEach(dayName => {
      if (inputText.includes(dayName)) {
        targetWeekday = weekdayMap[dayName];
        console.log(`🎯 检测到星期几: ${dayName} (${targetWeekday})`);
      }
    });
    // 如果没有检测到具体星期几，默认使用下周一
    if (targetWeekday === null) {
      console.log('⚠️ 未检测到具体星期几，默认使用下周一');
      targetWeekday = 1; // 周一
    }
    // 获取当前星期几 (0=周日, 1=周一, ..., 6=周六)
    const currentWeekday = today.getDay();
    console.log(`📅 今天是星期${currentWeekday}，目标是下周星期${targetWeekday}`);
    // 🎯 修复：正确计算下周指定星期几的日期
    let daysToAdd;
    // 🎯 新的简单计算方法：
    // 1. 先计算到下周同一天需要的天数：7天
    // 2. 然后调整到目标星期几：7 + (目标星期 - 当前星期)
    daysToAdd = 7 + (targetWeekday - currentWeekday);
    // 特殊处理：如果结果是负数或0，说明目标日期在本周或今天，需要调整到下周
    if (daysToAdd <= 0) {
      daysToAdd += 7;
    }
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + daysToAdd);
    console.log(`✅ 计算结果: 从今天星期${currentWeekday}(${today.toDateString()}) + ${daysToAdd}天 = 星期${targetWeekday}(${targetDate.toDateString()})`);
    return targetDate;
  },
  // 🧪 显示系统调试菜单
  showSystemDebugMenu() {
    console.log('🔧 === 显示系统调试菜单 ===');
    wx.showActionSheet({
      itemList: [
        '🔄 重新创建测试数据',
        '🗓️ 验证日程页面显示',
        '📊 检查数据保存格式',
        '🕵️ 追踪"五天后去上海"',
        '🧹 清空所有数据'
      ],
      success: (res) => {
        console.log('🎯 用户选择了调试菜单选项:', res.tapIndex);
        const actions = [
          () => this.recreateTestDataWithCorrectFormat(),
          () => this.verifySchedulePageDisplay(),
          () => this.checkDataSaveFormat(),
          () => this.debugSpecificInput(),
          () => this.confirmClearAllData()
        ];
        if (res.tapIndex < actions.length) {
          console.log('✅ 正在执行选中的调试功能...');
          actions[res.tapIndex]();
        }
      },
      fail: (res) => {
        console.log('用户取消了调试菜单');
      }
    });
  },
  // 🤖 测试AI分类功能
  async testAIClassification() {
    try {
      wx.showLoading({
        title: '测试AI分类...',
        mask: true
      });

      const testTexts = [
        '明天下午2点开会',
        '买咖啡花了25元',
        '完成工作报告'
      ];

      const results = [];
      
      for (const text of testTexts) {
        try {
          console.log(`🧪 测试分类: ${text}`);
          const result = await aiService.intelligentClassify(text);
          results.push({
            text,
            success: true,
            result: result.items[0],
            source: result.source
          });
        } catch (error) {
          results.push({
            text,
            success: false,
            error: error.message
          });
        }
      }

      wx.hideLoading();

      // 生成测试报告
      let report = '🤖 AI分类测试结果:\n\n';
      results.forEach((test, index) => {
        const status = test.success ? '✅' : '❌';
        report += `${index + 1}. "${test.text}": ${status}\n`;
        if (test.success) {
          report += `   类型: ${test.result.primaryType}\n`;
          report += `   置信度: ${Math.round(test.result.confidence * 100)}%\n`;
          report += `   来源: ${test.source}\n`;
        } else {
          report += `   错误: ${test.error}\n`;
        }
        report += '\n';
      });

      const successCount = results.filter(r => r.success).length;
      
      wx.showModal({
        title: '🧪 AI分类测试',
        content: report,
        confirmText: successCount > 0 ? '创建测试数据' : '确定',
        showCancel: false,
        success: (res) => {
          if (res.confirm && successCount > 0) {
            this.createSimpleTestData();
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ AI分类测试失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },
  // 🔍 检查大模型状态
  async checkAIServiceStatus() {
    try {
      wx.showLoading({ title: '检查AI状态...', mask: true });

      const aiStatus = aiService.getStatus();
      console.log('🔍 AI服务状态:', aiStatus);

      let report = '🔍 大模型服务状态:\n\n';
      report += `🤖 AI服务: ${aiStatus.enabled ? '✅ 启用' : '❌ 禁用'}\n`;
      report += `📡 提供商数量: ${Object.keys(aiService.providers).length}\n`;
      
      const activeProviders = Object.values(aiService.providers).filter(p => p.enabled);
      report += `⚡ 激活提供商: ${activeProviders.length}\n\n`;

      if (activeProviders.length > 0) {
        report += '📋 提供商详情:\n';
        activeProviders.forEach((provider, index) => {
          report += `${index + 1}. ${provider.name}\n`;
          report += `   模型: ${provider.model}\n`;
          report += `   API: ${provider.apiKey ? '已配置' : '未配置'}\n`;
        });
      } else {
        report += '⚠️ 没有激活的AI提供商\n';
        report += '💡 建议检查网络连接或API配置';
      }

      wx.hideLoading();

      wx.showModal({
        title: '🔍 AI服务状态',
        content: report,
        showCancel: false
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 检查AI状态失败:', error);
      wx.showToast({
        title: '检查失败',
        icon: 'error'
      });
    }
  },
  // 📊 显示保存的数据
  showSavedData() {
    const schedules = wx.getStorageSync('local_schedules') || [];
    const todos = wx.getStorageSync('local_todos') || [];
    const expenses = wx.getStorageSync('local_expenses') || [];

    let report = '📊 已保存的数据:\n\n';
    report += `📅 日程: ${schedules.length} 条\n`;
    report += `✅ 待办: ${todos.length} 条\n`;
    report += `💰 财务: ${expenses.length} 条\n\n`;

    const totalData = schedules.length + todos.length + expenses.length;
    
    if (totalData === 0) {
      report += '❌ 暂无数据\n';
      report += '💡 可以先创建一些测试数据';
    } else {
      report += '✅ 数据保存正常\n';
      report += '💡 可以到相应页面查看详情';
    }

    wx.showModal({
      title: '📊 数据概览',
      content: report,
      confirmText: totalData === 0 ? '创建测试数据' : '确定',
      showCancel: false,
      success: (res) => {
        if (res.confirm && totalData === 0) {
          this.createSimpleTestData();
        }
      }
    });
  },
  // 🔄 批量重新分类历史数据（新功能）
  async batchReclassifyHistoricalData() {
    try {
      console.log('🔄 === 开始批量重新分类历史数据 ===');
      
      // 🔧 添加详细的初始状态检查
      console.log('🔧 检查系统状态...');
      const aiStatus = aiService.getStatus();
      console.log('🤖 AI服务状态:', aiStatus);
      
      if (!aiStatus.enabled) {
        wx.showModal({
          title: '⚠️ AI服务未启用',
          content: 'AI服务当前未启用，无法进行智能重新分类。\n\n您可以：\n1. 检查网络连接\n2. 重启小程序\n3. 联系技术支持',
          showCancel: false
        });
        return;
      }

      // 1️⃣ 首先确认用户意图
      const confirmResult = await this.confirmBatchReclassification();
      if (!confirmResult) {
        console.log('❌ 用户取消了批量重新分类');
        return;
      }

      wx.showLoading({
        title: '正在扫描历史数据...',
        mask: true
      });

      // 2️⃣ 获取所有历史数据 - 添加详细日志
      console.log('📊 开始扫描历史数据...');
      const allData = await this.getAllHistoricalData();
      
      // 🔧 详细的数据统计日志
      console.log('📊 === 数据扫描结果 ===');
      console.log('📅 日程数据:', allData.schedules.length);
      console.log('✅ 待办数据:', allData.todos.length);
      console.log('💰 财务数据:', allData.expenses.length);
      console.log('📝 可重新分类文本:', allData.originalTexts.length);
      console.log('📋 数据详情:', allData.byType);

      if (allData.total === 0) {
        wx.hideLoading();
        wx.showModal({
          title: '📊 没有历史数据',
          content: `当前没有找到可以重新分类的历史数据。
          
📋 扫描结果：
• 日程记录：${allData.byType.schedule || 0} 条
• 待办事项：${allData.byType.todo || 0} 条  
• 财务记录：${allData.byType.expense || 0} 条

💡 建议：
• 先创建一些数据记录
• 或检查数据是否正确保存`,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }

      // 3️⃣ 显示数据统计，让用户确认
      const proceedResult = await this.showDataSummaryAndConfirm(allData);
      if (!proceedResult) {
        wx.hideLoading();
        return;
      }

      wx.showLoading({
        title: '开始AI重新分类...',
        mask: true
      });

      // 🔧 添加处理前的详细状态
      console.log('🤖 === 开始AI重新分类处理 ===');
      console.log('📝 待处理文本列表:');
      allData.originalTexts.forEach((item, index) => {
        console.log(`${index + 1}. [${item.type}] ${item.originalText}`);
      });

      // 4️⃣ 执行批量重新分类
      const reclassificationResults = await this.performBatchReclassification(allData);
      
      // 🔧 添加处理结果的详细日志
      console.log('🎉 === 批量重新分类处理完成 ===');
      console.log('📊 处理统计:', {
        total: reclassificationResults.total,
        success: reclassificationResults.success,
        failed: reclassificationResults.failed,
        improved: reclassificationResults.improved,
        unchanged: reclassificationResults.unchanged
      });
      console.log('✅ 成功处理的记录:', reclassificationResults.details.filter(r => r.success));
      console.log('❌ 失败的记录:', reclassificationResults.errors);

      wx.hideLoading();

      // 5️⃣ 显示处理结果
      await this.showReclassificationResults(reclassificationResults);

      // 6️⃣ 刷新界面数据
      console.log('🔄 刷新界面数据...');
      await this.refreshAllData();
      
      console.log('✅ 批量重新分类完成');
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 批量重新分类失败:', error);
      console.error('❌ 错误堆栈:', error.stack);
      
      wx.showModal({
        title: '❌ 重新分类失败',
        content: `批量重新分类过程中出现错误:

🚨 错误信息：${error.message}

🔧 可能原因：
• 网络连接不稳定
• AI服务临时不可用
• 数据格式异常
• 内存不足

💡 建议解决方案：
• 检查网络连接
• 重启小程序后重试
• 减少处理批次大小
• 联系技术支持

详细错误信息已记录在控制台，请截图保存。`,
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  },
  // 🔍 确认批量重新分类
  confirmBatchReclassification() {
    return new Promise((resolve) => {
      wx.showModal({
        title: '🔄 批量重新分类确认',
        content: `即将对所有历史数据进行AI重新分类，这将：
✅ 使用最新的AI分类逻辑
✅ 提高数据分类准确性  
✅ 自动优化数据归类
✅ 保留原始数据内容
⚠️ 注意事项：
• 此过程需要较长时间
• 需要稳定的网络连接
• 建议在WIFI环境下进行
是否继续？`,
        confirmText: '开始重新分类',
        cancelText: '暂时不用',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },
  // 📊 获取所有历史数据
  async getAllHistoricalData() {
    try {
      console.log('📊 扫描所有历史数据...');
      const allData = {
        schedules: [],
        todos: [],
        expenses: [],
        total: 0,
        originalTexts: [], // 存储原始输入文本，用于重新分类
        byType: {}
      };
      // 🎯 从多个存储位置获取数据
      // 方式1: 从新的dataSync格式获取
      const localSchedules = wx.getStorageSync('local_schedules') || [];
      const localTodos = wx.getStorageSync('local_todos') || [];
      const localExpenses = wx.getStorageSync('local_expenses') || [];
      // 方式2: 从旧的列表格式获取
      const schedulesList = wx.getStorageSync('local_schedules_list') || [];
      const todosList = wx.getStorageSync('local_todos_list') || [];
      const expensesList = wx.getStorageSync('local_expenses_list') || [];
      // 方式3: 从按日期分组的格式获取
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      const allTodos = wx.getStorageSync('allTodos') || {};
      const allFinances = wx.getStorageSync('allFinances') || {};
      console.log('📊 原始数据统计:', {
        local_schedules: localSchedules.length,
        local_todos: localTodos.length,
        local_expenses: localExpenses.length,
        schedules_list: schedulesList.length,
        todos_list: todosList.length,
        expenses_list: expensesList.length,
        grouped_schedules: Object.keys(allSchedules).length,
        grouped_todos: Object.keys(allTodos).length,
        grouped_finances: Object.keys(allFinances).length
      });
      // 🔄 合并和去重数据
      const processedIds = new Set();
      // 处理日程数据
      [...localSchedules, ...schedulesList].forEach(item => {
        if (item && item.id && !processedIds.has(item.id) && item.status !== 'deleted') {
          allData.schedules.push(this.normalizeScheduleData(item));
          processedIds.add(item.id);
        }
      });
      // 从分组数据中提取日程
      Object.values(allSchedules).forEach(daySchedules => {
        daySchedules.forEach(item => {
          if (item && item.id && !processedIds.has(item.id)) {
            allData.schedules.push(this.normalizeScheduleData(item));
            processedIds.add(item.id);
          }
        });
      });
      // 处理待办数据
      processedIds.clear();
      [...localTodos, ...todosList].forEach(item => {
        if (item && item.id && !processedIds.has(item.id) && item.status !== 'deleted') {
          allData.todos.push(this.normalizeTodoData(item));
          processedIds.add(item.id);
        }
      });
      // 从分组数据中提取待办
      Object.values(allTodos).forEach(dayTodos => {
        dayTodos.forEach(item => {
          if (item && item.id && !processedIds.has(item.id)) {
            allData.todos.push(this.normalizeTodoData(item));
            processedIds.add(item.id);
          }
        });
      });
      // 处理财务数据
      processedIds.clear();
      [...localExpenses, ...expensesList].forEach(item => {
        if (item && item.id && !processedIds.has(item.id) && item.status !== 'deleted') {
          allData.expenses.push(this.normalizeExpenseData(item));
          processedIds.add(item.id);
        }
      });
      // 从分组数据中提取财务
      Object.values(allFinances).forEach(dayFinances => {
        dayFinances.forEach(item => {
          if (item && item.id && !processedIds.has(item.id)) {
            allData.expenses.push(this.normalizeExpenseData(item));
            processedIds.add(item.id);
          }
        });
      });
      // 🎯 计算总数和构建重新分类列表
      allData.total = allData.schedules.length + allData.todos.length + allData.expenses.length;
      allData.byType = {
        schedule: allData.schedules.length,
        todo: allData.todos.length,
        expense: allData.expenses.length
      };
      // 🔍 为每个数据项生成重新分类文本
      allData.schedules.forEach(item => {
        allData.originalTexts.push({
          id: item.id,
          type: 'schedule',
          originalText: this.extractOriginalText(item, 'schedule'),
          currentData: item
        });
      });
      allData.todos.forEach(item => {
        allData.originalTexts.push({
          id: item.id,
          type: 'todo',
          originalText: this.extractOriginalText(item, 'todo'),
          currentData: item
        });
      });
      allData.expenses.forEach(item => {
        allData.originalTexts.push({
          id: item.id,
          type: 'expense',
          originalText: this.extractOriginalText(item, 'expense'),
          currentData: item
        });
      });
      console.log('✅ 历史数据扫描完成:', allData.byType);
      console.log('📝 可重新分类的文本数量:', allData.originalTexts.length);
      return allData;
    } catch (error) {
      console.error('❌ 获取历史数据失败:', error);
      throw new Error(`获取历史数据失败: ${error.message}`);
    }
  },
  // 🔧 标准化日程数据
  normalizeScheduleData(item) {
    return {
      id: item.id,
      title: item.title || '',
      description: item.description || '',
      startTime: item.startTime || item.start_time || '',
      endTime: item.endTime || item.end_time || '',
      location: item.location || '',
      time: item.time || '',
      type: 'schedule',
      originalType: 'schedule',
      source: item.source || 'unknown'
    };
  },
  // 🔧 标准化待办数据
  normalizeTodoData(item) {
    return {
      id: item.id,
      title: item.title || '',
      description: item.description || '',
      priority: item.priority || 'medium',
      completed: item.completed || false,
      dueDate: item.dueDate || item.due_date || '',
      category: item.category || '',
      type: 'todo',
      originalType: 'todo',
      source: item.source || 'unknown'
    };
  },
  // 🔧 标准化财务数据
  normalizeExpenseData(item) {
    return {
      id: item.id,
      amount: item.amount || 0,
      description: item.description || '',
      category: item.category || '',
      date: item.date || '',
      type: item.type || 'expense',
      originalType: 'expense',
      source: item.source || 'unknown'
    };
  },
  // 📝 提取原始输入文本（用于重新分类）
  extractOriginalText(item, type) {
    switch (type) {
      case 'schedule':
        // 尝试重构原始输入文本
        const timeInfo = item.time || this.formatTimeFromISO(item.startTime) || '';
        const locationInfo = item.location ? `在${item.location}` : '';
        return `${item.title} ${timeInfo} ${locationInfo}`.trim();
      case 'todo':
        const priorityInfo = item.priority && item.priority !== 'medium' ? `(${item.priority})` : '';
        const dueDateInfo = item.dueDate ? `截止${this.formatDate(item.dueDate)}` : '';
        return `${item.title} ${priorityInfo} ${dueDateInfo}`.trim();
      case 'expense':
        const amountInfo = item.amount ? `${item.amount}元` : '';
        const categoryInfo = item.category ? `${item.category}类` : '';
        return `${item.description} ${amountInfo} ${categoryInfo}`.trim();
      default:
        return item.title || item.description || '未知';
    }
  },
  // 📋 显示数据统计并确认处理
  showDataSummaryAndConfirm(allData) {
    return new Promise((resolve) => {
      const summary = `发现以下历史数据：
📅 日程记录: ${allData.byType.schedule} 条
✅ 待办事项: ${allData.byType.todo} 条  
💰 财务记录: ${allData.byType.expense} 条
📊 总计: ${allData.total} 条记录
🤖 AI重新分类将：
• 分析每条记录的内容
• 使用最新分类算法
• 自动优化归类结果
• 提高分类准确性
⏱️ 预计处理时间: ${Math.ceil(allData.total / 10)} 分钟
确认开始处理？`;
      wx.showModal({
        title: '📊 数据统计确认',
        content: summary,
        confirmText: '确认处理',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },
  // 🤖 执行批量重新分类
  async performBatchReclassification(allData) {
    console.log('🤖 开始执行批量重新分类...');
    const results = {
      total: allData.originalTexts.length,
      processed: 0,
      success: 0,
      failed: 0,
      improved: 0, // 分类改进的数量
      unchanged: 0, // 分类未变的数量
      details: [],
      errors: []
    };
    // 分批处理，避免一次性处理太多数据
    const batchSize = 5; // 每批处理5条
    const batches = [];
    for (let i = 0; i < allData.originalTexts.length; i += batchSize) {
      batches.push(allData.originalTexts.slice(i, i + batchSize));
    }
    console.log(`📦 将分${batches.length}批处理，每批${batchSize}条`);
    // 逐批处理
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      // 更新进度
      wx.showLoading({
        title: `处理中 ${results.processed}/${results.total}`,
        mask: true
      });
      console.log(`🔄 处理第${batchIndex + 1}批，共${batch.length}条记录`);
      // 并行处理当前批次的所有记录
      const batchPromises = batch.map(async (dataItem) => {
        try {
          return await this.reclassifySingleItem(dataItem);
        } catch (error) {
          console.error(`❌ 重新分类失败 ${dataItem.id}:`, error);
          return {
            id: dataItem.id,
            success: false,
            error: error.message,
            originalType: dataItem.type,
            originalText: dataItem.originalText
          };
        }
      });
      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);
      // 统计批次结果
      batchResults.forEach(result => {
        results.processed++;
        results.details.push(result);
        if (result.success) {
          results.success++;
          if (result.improved) {
            results.improved++;
          } else {
            results.unchanged++;
          }
        } else {
          results.failed++;
          results.errors.push(result);
        }
      });
      console.log(`✅ 第${batchIndex + 1}批处理完成，当前进度: ${results.processed}/${results.total}`);
      // 批次间短暂延迟，避免API频率限制
      if (batchIndex < batches.length - 1) {
        await this.delay(1000); // 1秒延迟
      }
    }
    console.log('🎉 所有批次处理完成:', results);
    return results;
  },
  // 🔍 重新分类单个数据项
  async reclassifySingleItem(dataItem) {
    try {
      console.log(`🔍 重新分类: ${dataItem.originalText} (当前类型: ${dataItem.type})`);
      // 使用AI服务重新分类
      const aiResult = await aiService.intelligentClassify(dataItem.originalText);
      if (!aiResult || !aiResult.items || aiResult.items.length === 0) {
        throw new Error('AI分类返回空结果');
      }
      const newClassification = aiResult.items[0];
      const newPrimaryType = newClassification.primaryType;
      const originalType = dataItem.type;
      // 判断是否需要更新分类
      const needsUpdate = this.shouldUpdateClassification(originalType, newClassification);
      if (needsUpdate) {
        // 更新数据分类
        await this.updateItemClassification(dataItem, newClassification, aiResult);
        console.log(`✅ 分类已更新: ${originalType} → ${newPrimaryType}`);
        return {
          id: dataItem.id,
          success: true,
          improved: true,
          originalType: originalType,
          newType: newPrimaryType,
          originalText: dataItem.originalText,
          confidence: newClassification.confidence,
          source: aiResult.source,
          reason: newClassification.reason
        };
      } else {
        console.log(`⚪ 分类未变: ${originalType} (置信度: ${newClassification.confidence})`);
        return {
          id: dataItem.id,
          success: true,
          improved: false,
          originalType: originalType,
          newType: newPrimaryType,
          originalText: dataItem.originalText,
          confidence: newClassification.confidence,
          source: aiResult.source,
          reason: '分类无需更改'
        };
      }
    } catch (error) {
      console.error(`❌ 重新分类失败 ${dataItem.id}:`, error);
      throw error;
    }
  },
  // 🔧 判断是否需要更新分类
  shouldUpdateClassification(originalType, newClassification) {
    const newType = newClassification.primaryType;
    // 类型映射表
    const typeMapping = {
      'schedule': ['schedule'],
      'todo': ['todo', 'schedule'], // 待办可能被分类为日程
      'expense': ['finance', 'expense'], // 支出可能被分类为财务
      'income': ['finance', 'income'], // 收入可能被分类为财务
      'finance': ['finance', 'expense', 'income'] // 财务可能是支出或收入
    };
    // 如果新分类类型在原类型的合理范围内，且置信度较高，则更新
    const validTypes = typeMapping[originalType] || [originalType];
    const isValidTransition = validTypes.includes(newType);
    const hasGoodConfidence = newClassification.confidence > 0.7;
    // 特殊规则：只有在分类明显改善时才更新
    if (originalType !== newType && isValidTransition && hasGoodConfidence) {
      return true;
    }
    // 如果是相同类型但置信度很高，也可以更新其他属性
    if (originalType === newType && hasGoodConfidence) {
      return true; // 可能需要更新其他属性如优先级、分类等
    }
    return false;
  },
  // 🔧 更新数据项分类
  async updateItemClassification(dataItem, newClassification, aiResult) {
    try {
      const newType = newClassification.primaryType;
      const updatedData = { ...dataItem.currentData };
      // 添加AI分类信息
      updatedData.aiClassification = {
        originalType: dataItem.type,
        newType: newType,
        confidence: newClassification.confidence,
        source: aiResult.source,
        reclassifiedAt: new Date().toISOString(),
        reason: newClassification.reason
      };
      // 根据新分类类型更新特定字段
      if (newClassification.extractedInfo) {
        const info = newClassification.extractedInfo;
        // 更新通用字段
        if (info.title && info.title !== dataItem.currentData.title) {
          updatedData.title = info.title;
        }
        if (info.category) {
          updatedData.category = info.category;
        }
        if (info.priority) {
          updatedData.priority = info.priority;
        }
        // 根据类型更新特定字段
        if (newType === 'finance' && info.amount) {
          updatedData.amount = info.amount;
        }
      }
      // 更新标记
      updatedData.reclassified = true;
      updatedData.updatedAt = new Date().toISOString();
      // 保存更新后的数据
      await this.saveUpdatedClassification(updatedData, dataItem.type, newType);
      console.log(`✅ 数据项 ${dataItem.id} 分类已更新`);
    } catch (error) {
      console.error(`❌ 更新数据项分类失败:`, error);
      throw error;
    }
  },
  // 💾 保存更新后的分类
  async saveUpdatedClassification(updatedData, originalType, newType) {
    try {
      // 1️⃣ 更新对应存储位置的数据
      await this.updateInAllStorageLocations(updatedData, originalType);
      // 2️⃣ 如果类型发生了变化，需要移动数据到新类型
      if (originalType !== newType) {
        await this.moveDataToNewType(updatedData, originalType, newType);
      }
    } catch (error) {
      console.error('保存更新分类失败:', error);
      throw error;
    }
  },
  // 🔄 在所有存储位置更新数据
  async updateInAllStorageLocations(updatedData, dataType) {
    try {
      const typeMapping = {
        'schedule': 'local_schedules',
        'todo': 'local_todos', 
        'expense': 'local_expenses',
        'finance': 'local_expenses' // 财务归类到支出
      };
      const storageKey = typeMapping[dataType];
      if (!storageKey) return;
      // 更新主存储
      const mainData = wx.getStorageSync(storageKey) || [];
      const mainIndex = mainData.findIndex(item => item.id === updatedData.id);
      if (mainIndex !== -1) {
        mainData[mainIndex] = { ...mainData[mainIndex], ...updatedData };
        wx.setStorageSync(storageKey, mainData);
      }
      // 更新列表存储
      const listKey = storageKey + '_list';
      const listData = wx.getStorageSync(listKey) || [];
      const listIndex = listData.findIndex(item => item.id === updatedData.id);
      if (listIndex !== -1) {
        listData[listIndex] = { ...listData[listIndex], ...updatedData };
        wx.setStorageSync(listKey, listData);
      }
      // 更新分组存储
      const groupMapping = {
        'schedule': 'allSchedules',
        'todo': 'allTodos',
        'expense': 'allFinances',
        'finance': 'allFinances'
      };
      const groupKey = groupMapping[dataType];
      if (groupKey) {
        const groupData = wx.getStorageSync(groupKey) || {};
        // 在所有日期组中查找并更新
        Object.keys(groupData).forEach(dateKey => {
          const items = groupData[dateKey] || [];
          const itemIndex = items.findIndex(item => item.id === updatedData.id);
          if (itemIndex !== -1) {
            items[itemIndex] = { ...items[itemIndex], ...updatedData };
          }
        });
        wx.setStorageSync(groupKey, groupData);
      }
    } catch (error) {
      console.error('更新存储位置失败:', error);
      throw error;
    }
  },
  // 🚚 移动数据到新类型
  async moveDataToNewType(updatedData, originalType, newType) {
    try {
      console.log(`🚚 移动数据: ${originalType} → ${newType}, ID: ${updatedData.id}`);
      // 这里可以实现类型转换逻辑
      // 目前只记录分类变化，不实际移动数据结构
      // 因为移动可能会导致数据结构不兼容
      console.log(`✅ 数据类型变更已记录: ${originalType} → ${newType}`);
    } catch (error) {
      console.error('移动数据类型失败:', error);
      throw error;
    }
  },
  // ⏱️ 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  // 📊 显示重新分类结果
  async showReclassificationResults(results) {
    const successRate = results.total > 0 ? Math.round((results.success / results.total) * 100) : 0;
    const improvementRate = results.total > 0 ? Math.round((results.improved / results.total) * 100) : 0;
    const resultSummary = `🎉 批量重新分类完成！
📊 处理统计:
• 总计处理: ${results.total} 条
• 成功处理: ${results.success} 条 (${successRate}%)
• 处理失败: ${results.failed} 条
🎯 分类改进:
• 分类优化: ${results.improved} 条 (${improvementRate}%)
• 分类无变: ${results.unchanged} 条
• 改进率: ${improvementRate}%
${results.failed > 0 ? `❌ 失败记录: ${results.failed} 条\n主要原因: 网络连接或AI服务问题` : ''}
✅ 数据分类准确性已得到提升！`;
    // 显示总结
    wx.showModal({
      title: '🎉 重新分类完成',
      content: resultSummary,
      confirmText: '查看详细结果',
      cancelText: '确定',
      success: (res) => {
        if (res.confirm) {
          this.showDetailedResults(results);
        }
      }
    });
  },
  // 📋 显示详细结果
  showDetailedResults(results) {
    console.log('📋 === 详细重新分类结果 ===');
    console.log('✅ 成功记录:', results.details.filter(r => r.success));
    console.log('❌ 失败记录:', results.errors);
    console.log('🔄 改进记录:', results.details.filter(r => r.success && r.improved));
    // 生成详细报告
    const improvedItems = results.details.filter(r => r.success && r.improved);
    let detailReport = `📈 分类改进详情 (${improvedItems.length}条):\n\n`;
    improvedItems.slice(0, 10).forEach((item, index) => {
      detailReport += `${index + 1}. "${item.originalText}"\n`;
      detailReport += `   ${item.originalType} → ${item.newType}\n`;
      detailReport += `   置信度: ${Math.round(item.confidence * 100)}%\n\n`;
    });
    if (improvedItems.length > 10) {
      detailReport += `... 还有 ${improvedItems.length - 10} 条改进记录\n`;
    }
    if (results.errors.length > 0) {
      detailReport += `\n❌ 处理失败 (${results.errors.length}条):\n`;
      results.errors.slice(0, 5).forEach((error, index) => {
        detailReport += `${index + 1}. ${error.originalText}: ${error.error}\n`;
      });
    }
    wx.showModal({
      title: '📋 详细分类结果',
      content: detailReport,
      showCancel: false,
      confirmText: '关闭'
    });
  },
  // 🔄 刷新所有数据
  async refreshAllData() {
    try {
      console.log('🔄 开始刷新所有数据...');
      
      // 刷新最近输入记录
      await this.refreshRecentInputs();
      
      // 通知其他页面刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshSchedules = true;
        app.globalData.shouldRefreshTodos = true;
        app.globalData.shouldRefreshFinances = true;
        console.log('✅ 已设置所有页面刷新标志');
      }
      
      console.log('✅ 所有数据刷新完成');
    } catch (error) {
      console.error('❌ 刷新数据失败:', error);
    }
  },
  // 🔄 刷新最近输入记录
  async refreshRecentInputs() {
    try {
      console.log('🔄 刷新最近输入记录...');
      
      // 重新加载最近输入记录
      await this.loadRecentInputs();
      
      console.log('✅ 最近输入记录刷新完成');
    } catch (error) {
      console.error('❌ 刷新最近输入记录失败:', error);
    }
  },
  // 🔒 确保最近输入记录持久化（修复跨页面消失问题）
  async ensureRecentInputsPersistence() {
    try {
      console.log('🔒 确保最近输入记录持久化...');
      
      // 保存当前的最近输入记录到存储
      if (this.data.recentInputs && this.data.recentInputs.length > 0) {
        wx.setStorageSync('persistent_recent_inputs', this.data.recentInputs);
        console.log('✅ 最近输入记录已持久化保存');
      }
      
      // 同时从所有数据源重新构建最近输入记录
      await this.rebuildRecentInputs();
      
    } catch (error) {
      console.error('❌ 持久化最近输入记录失败:', error);
    }
  },
  // 🔄 重新构建最近输入记录
  async rebuildRecentInputs() {
    try {
      console.log('🔄 重新构建最近输入记录...');
      
      const allRecentItems = [];
      
      // 从各个数据源收集最近记录
      const schedules = wx.getStorageSync('local_schedules') || [];
      const todos = wx.getStorageSync('local_todos') || [];
      const expenses = wx.getStorageSync('local_expenses') || [];
      
      // 收集所有记录并按时间排序
      [...schedules, ...todos, ...expenses]
        .filter(item => item && item.createdAt)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 10) // 只取最近10条
        .forEach(record => {
          try {
            allRecentItems.push({
              id: record.id,
              content: this.getRecordDisplayText(record),
              time: this.formatTime(record.createdAt),
              type: record.type,
              typeText: this.getTypeText(record.type),
              icon: this.getTypeIcon(record.type),
              originalData: record
            });
          } catch (error) {
            console.error('❌ 处理单个记录失败:', error, record);
            // 添加基本记录，避免完全失败
            allRecentItems.push({
              id: record.id || this.generateId(),
              content: record.title || record.description || '未知记录',
              time: '未知时间',
              type: record.type || 'unknown',
              typeText: '记录',
              icon: '📝',
              originalData: record
            });
          }
        });
      
      // 更新界面显示
      this.setData({
        recentInputs: allRecentItems
      });
      
      // 持久化保存
      wx.setStorageSync('persistent_recent_inputs', allRecentItems);
      
      console.log('✅ 最近输入记录重新构建完成:', allRecentItems.length);
      
    } catch (error) {
      console.error('❌ 重新构建最近输入记录失败:', error);
    }
  },
  // 🔧 辅助方法：从ISO时间格式化
  formatTimeFromISO(isoString) {
    if (!isoString) return '';
    try {
      const date = new Date(isoString);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error) {
      return '';
    }
  },
  // 🔧 辅助方法：格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    } catch (error) {
      return '';
    }
  },
  // 🧪 AI分类测试
  async testAIClassification() {
    try {
      wx.showLoading({
        title: '测试AI分类...',
        mask: true
      });
      const testTexts = [
        '明天下午2点开会',
        '买咖啡花了25元',
        '下周还信用卡2000元',
        '完成工作报告',
        '晚上看电影'
      ];
      const results = [];
      for (const text of testTexts) {
        try {
          const result = await aiService.intelligentClassify(text);
          results.push({
            text,
            success: true,
            result: result.items[0],
            source: result.source
          });
        } catch (error) {
          results.push({
            text,
            success: false,
            error: error.message
          });
        }
      }
      wx.hideLoading();
      // 生成测试报告
      let report = '🧪 AI分类测试结果:\n\n';
      results.forEach((test, index) => {
        const status = test.success ? '✅' : '❌';
        report += `${index + 1}. "${test.text}": ${status}\n`;
        if (test.success) {
          report += `   类型: ${test.result.primaryType}\n`;
          report += `   置信度: ${Math.round(test.result.confidence * 100)}%\n`;
          report += `   来源: ${test.source}\n`;
        } else {
          report += `   错误: ${test.error}\n`;
        }
        report += '\n';
      });
      wx.showModal({
        title: '🧪 AI分类测试',
        content: report,
        showCancel: false,
        confirmText: '关闭'
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: 'AI分类测试失败',
        icon: 'error'
      });
    }
  },
  // 🌐 网络连接测试
  async testNetworkConnection() {
    try {
      wx.showLoading({
        title: '测试网络连接...',
        mask: true
      });
      const testResults = await aiService.diagnoseNetworkConnectivity();
      wx.hideLoading();
      let report = '🌐 网络连接测试结果:\n\n';
      testResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration ? ` (${result.duration}ms)` : '';
        report += `${result.url.replace('https://', '')}: ${status}${duration}\n`;
      });
      const successCount = testResults.filter(r => r.success).length;
      report += `\n网络状态: ${successCount}/${testResults.length} 可连接`;
      wx.showModal({
        title: '🌐 网络连接测试',
        content: report,
        showCancel: false,
        confirmText: '关闭'
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '网络测试失败',
        icon: 'error'
      });
    }
  },
  // 📊 显示AI提供商状态
  showAIProvidersStatus() {
    try {
      const providers = aiService.providers;
      let report = '📊 AI提供商状态:\n\n';
      Object.entries(providers).forEach(([id, provider]) => {
        const status = provider.enabled ? '✅ 启用' : '❌ 禁用';
        const hasKey = provider.apiKey ? '🔑 有密钥' : '❌ 无密钥';
        report += `${provider.name}:\n`;
        report += `• 状态: ${status}\n`;
        report += `• 密钥: ${hasKey}\n`;
        report += `• 优先级: ${provider.priority}\n`;
        report += `• 模型: ${provider.model}\n`;
        report += `• 端点: ${provider.apiBase}\n\n`;
      });
      const currentProvider = aiService.getAvailableProvider();
      report += `🎯 当前主要提供商: ${currentProvider ? currentProvider.name : '无可用'}`;
      wx.showModal({
        title: '📊 AI提供商状态',
        content: report,
        showCancel: false,
        confirmText: '关闭'
      });
    } catch (error) {
      wx.showToast({
        title: '获取提供商状态失败',
        icon: 'error'
      });
    }
  },
  // 🔧 AI配置管理
  showAIConfiguration() {
    try {
      const status = aiService.getStatus();
      let config = `🔧 AI服务配置:\n\n`;
      config += `• 服务状态: ${status.enabled ? '✅ 启用' : '❌ 禁用'}\n`;
      config += `• 配置状态: ${status.configured ? '✅ 已配置' : '❌ 未配置'}\n`;
      config += `• API密钥: ${status.hasApiKey ? '✅ 已设置' : '❌ 未设置'}\n`;
      config += `• API端点: ${status.apiBase}\n`;
      config += `• 使用模型: ${status.model}\n\n`;
      config += `🔧 配置说明:\n`;
      config += `• 系统已预配置DeepSeek和硅基流动API\n`;
      config += `• 支持多提供商自动切换\n`;
      config += `• 具备本地智能分析降级能力\n`;
      config += `• AI分类算法持续优化中`;
      wx.showModal({
        title: "🔧 AI配置信息", 
        content: config,
        showCancel: false,
        confirmText: '关闭'
      });
    } catch (error) {
      wx.showToast({
        title: '获取AI配置失败',
        icon: 'error'
      });
    }
  },
  // 📈 数据流程调试
  async debugDataFlow() {
    try {
      // 获取数据统计
      const schedules = wx.getStorageSync('local_schedules') || [];
      const todos = wx.getStorageSync('local_todos') || [];
      const expenses = wx.getStorageSync('local_expenses') || [];
      const schedulesList = wx.getStorageSync('local_schedules_list') || [];
      const todosList = wx.getStorageSync('local_todos_list') || [];
      const expensesList = wx.getStorageSync('local_expenses_list') || [];
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      const allTodos = wx.getStorageSync('allTodos') || {};
      const allFinances = wx.getStorageSync('allFinances') || {};
      let report = `📈 数据流程调试信息:\n\n`;
      report += `📊 存储格式1 (DataSync):\n`;
      report += `• 日程: ${schedules.length} 条\n`;
      report += `• 待办: ${todos.length} 条\n`;
      report += `• 财务: ${expenses.length} 条\n\n`;
      report += `📊 存储格式2 (列表):\n`;
      report += `• 日程: ${schedulesList.length} 条\n`;
      report += `• 待办: ${todosList.length} 条\n`;
      report += `• 财务: ${expensesList.length} 条\n\n`;
      report += `📊 存储格式3 (分组):\n`;
      report += `• 日程日期: ${Object.keys(allSchedules).length} 个\n`;
      report += `• 待办日期: ${Object.keys(allTodos).length} 个\n`;
      report += `• 财务日期: ${Object.keys(allFinances).length} 个\n\n`;
      const totalRecords = schedules.length + todos.length + expenses.length;
      report += `🎯 数据流程状态:\n`;
      report += `• 总记录数: ${totalRecords} 条\n`;
      report += `• AI服务: ${aiService.enabled ? '正常' : '离线'}\n`;
      report += `• 数据同步: 本地模式\n`;
      report += `• 最后更新: ${new Date().toLocaleString()}`;
      wx.showModal({
        title: '📈 数据流程调试',
        content: report,
        confirmText: '清空所有数据',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm) {
            this.confirmClearAllData();
          }
        }
      });
    } catch (error) {
      wx.showToast({
        title: '数据流程调试失败',
        icon: 'error'
      });
    }
  },
  // 🗑️ 确认清空所有数据
  confirmClearAllData() {
    wx.showModal({
      title: '⚠️ 危险操作确认',
      content: '确定要清空所有历史数据吗？\n\n此操作不可撤销，建议先进行数据备份。',
      confirmText: '确认清空',
      cancelText: '取消',
      confirmColor: '#FF6B6B',
      success: (res) => {
        if (res.confirm) {
          this.clearAllHistoricalData();
        }
      }
    });
  },
  // 🗑️ 清空所有历史数据
  clearAllHistoricalData() {
    try {
      // 清空所有存储键
      const storageKeys = [
        'local_schedules',
        'local_todos', 
        'local_expenses',
        'local_schedules_list',
        'local_todos_list',
        'local_expenses_list',
        'allSchedules',
        'allTodos',
        'allFinances'
      ];
      storageKeys.forEach(key => {
        wx.removeStorageSync(key);
      });
      wx.showToast({
        title: '所有数据已清空',
        icon: 'success'
      });
      // 刷新界面
      this.refreshAllData();
    } catch (error) {
      wx.showToast({
        title: '清空数据失败',
        icon: 'error'
      });
    }
  },
  // 显示添加菜单
  showAddMenu() {
    console.log('🔍 === showAddMenu 调用开始 ===');
    console.log('📱 当前页面状态检查...');
    // 添加详细的调试信息
    try {
      console.log('✅ showAddMenu 方法正常执行');
      console.log('🎯 准备显示操作菜单...');
      wx.showActionSheet({
        itemList: [
          '📝 文本输入',
          '🎤 语音输入', 
          '📅 添加日程',
          '✅ 添加待办',
          '💰 添加财务记录',
          '🔧 系统调试'
        ],
        success: (res) => {
          console.log('📋 用户选择了选项:', res.tapIndex);
          // 根据用户选择执行对应操作
          switch(res.tapIndex) {
            case 0:
              console.log('📝 执行文本输入');
              this.showTextInput();
              break;
            case 1:
              console.log('🎤 执行语音输入');
              this.showVoiceInput();
              break;
            case 2:
              console.log('📅 执行添加日程');
              this.addSchedule();
              break;
            case 3:
              console.log('✅ 执行添加待办');
              this.addTodo();
              break;
            case 4:
              console.log('💰 执行添加财务记录');
              this.showTextInput();
              break;
            case 5:
              console.log('🔧 执行系统调试');
              this.showSystemDebugMenu();
              break;
            default:
              console.log('❓ 未知选项');
          }
        },
        fail: (error) => {
          console.error('❌ 显示菜单失败:', error);
          // 降级方案：显示基本的文本输入
          wx.showModal({
            title: '菜单显示失败',
            content: '是否直接进行文本输入？',
            success: (res) => {
              if (res.confirm) {
                this.showTextInput();
              }
            }
          });
        }
      });
    } catch (error) {
      console.error('❌ showAddMenu 执行出错:', error);
      // 显示错误提示和基本功能
      wx.showModal({
        title: '功能异常',
        content: `菜单功能遇到问题: ${error.message}\n\n是否使用文本输入作为替代？`,
        success: (res) => {
          if (res.confirm) {
            this.showTextInput();
          }
        }
      });
    }
    console.log('🔍 === showAddMenu 调用结束 ===');
  },
  // 🎯 处理AI分类结果
  async handleAIClassificationResult(aiResult) {
    try {
      console.log('🎯 处理AI分类结果:', aiResult);
      if (!aiResult || !aiResult.items || aiResult.items.length === 0) {
        throw new Error('AI结果为空');
      }
      const aiItem = aiResult.items[0];
      const createdItems = [];

      // 🎯 重点：正确处理日程数据，优先使用AI计算的日期和地点
      if (aiItem.primaryType === 'schedule' || aiItem.originalText.includes('去') || aiItem.originalText.includes('开会')) {
        console.log('📅 创建日程数据...');
        
        // 🗓️ 优先使用AI计算的日期，如果没有则降级计算
        let targetDate;
        if (aiItem.dateTime) {
          targetDate = new Date(aiItem.dateTime);
          console.log('✅ 使用AI计算的日期:', targetDate);
        } else if (aiItem.timeInfo && aiItem.timeInfo.dateTime) {
          targetDate = new Date(aiItem.timeInfo.dateTime);
          console.log('✅ 使用AI timeInfo的日期:', targetDate);
        } else {
          // 降级：手动计算日期
          targetDate = this.calculateDateFromText(aiItem.originalText);
          console.log('⚠️ 降级使用手动计算的日期:', targetDate);
        }
        
        // 🏢 优先使用AI提取的地点
        let location = '';
        if (aiItem.location) {
          location = aiItem.location;
          console.log('✅ 使用AI提取的地点:', location);
        } else {
          // 降级：手动提取地点
          location = this.extractLocationFromText(aiItem.originalText);
          console.log('⚠️ 降级使用手动提取的地点:', location);
        }
        
        const scheduleData = {
          id: this.generateId(),
          title: aiItem.title || aiItem.originalText,
          description: aiItem.description || aiItem.originalText,
          startTime: targetDate.toISOString(),
          endTime: new Date(targetDate.getTime() + 60 * 60 * 1000).toISOString(), // 默认1小时
          location: location,
          category: aiItem.category || aiItem.extractedInfo?.category || '会议',
          source: `AI分析 (${aiResult.source})`,
          createdAt: new Date().toISOString(),
          type: 'schedule'
        };
        
        console.log('📅 最终日程数据:', scheduleData);
        
        try {
          // 🎯 重要：保存到所有日程页面需要的位置
          const result = await this.saveScheduleToAllLocations(scheduleData);
          if (result && result.success) {
            createdItems.push(result);
            console.log('✅ 日程创建成功，已保存到所有位置');
          }
        } catch (error) {
          console.error('❌ 日程创建失败:', error);
        }
      }

      if (aiItem.primaryType === 'finance' || aiItem.originalText.includes('元') || aiItem.originalText.includes('花了')) {
        console.log('💰 创建财务数据...');
        
        // 优先使用AI提取的金额
        let amount = 0;
        if (aiItem.financialInfo && aiItem.financialInfo.amount) {
          amount = aiItem.financialInfo.amount;
        } else {
          amount = this.extractAmountFromText(aiItem.originalText);
        }
        
        const financeData = {
          id: this.generateId(),
          amount: amount,
          description: aiItem.title || aiItem.originalText,
          category: aiItem.category || aiItem.financialInfo?.category || '其他',
          type: 'expense',
          date: new Date().toISOString(),
          source: `AI分析 (${aiResult.source})`,
          createdAt: new Date().toISOString()
        };
        
        try {
          const result = await this.saveFinanceToAllLocations(financeData);
          if (result && result.success) {
            createdItems.push(result);
            console.log('✅ 财务创建成功');
          }
        } catch (error) {
          console.error('❌ 财务创建失败:', error);
        }
      }

      // 🎯 对于未来事件，同时创建待办（但优先确保日程正确保存）
      if (aiItem.isFuture || this.isFutureEvent(aiItem.originalText)) {
        console.log('✅ 创建待办数据...');
        
        // 使用与日程相同的日期计算逻辑
        let targetDate;
        if (aiItem.dateTime) {
          targetDate = new Date(aiItem.dateTime);
        } else if (aiItem.timeInfo && aiItem.timeInfo.dateTime) {
          targetDate = new Date(aiItem.timeInfo.dateTime);
        } else {
          targetDate = this.calculateDateFromText(aiItem.originalText);
        }
        
        const todoData = {
          id: this.generateId(),
          title: aiItem.title || aiItem.originalText,
          description: aiItem.description || aiItem.originalText,
          priority: aiItem.priority || 'medium',
          category: aiItem.category || '一般',
          dueDate: targetDate.toISOString(),
          source: `AI分析 (${aiResult.source})`,
          createdAt: new Date().toISOString(),
          completed: false,
          type: 'todo'
        };
        
        try {
          const result = await this.saveTodoToAllLocations(todoData);
          if (result && result.success) {
            createdItems.push(result);
            console.log('✅ 待办创建成功');
          }
        } catch (error) {
          console.error('❌ 待办创建失败:', error);
        }
      }

      console.log('✅ AI分类结果处理完成，创建项目:', createdItems);
      return createdItems;
    } catch (error) {
      console.error('❌ 处理AI分类结果失败:', error);
      throw error;
    }
  },

  // 🗓️ 从文本中计算正确的日期
  calculateDateFromText(text) {
    console.log('🗓️ 计算日期，输入文本:', text);
    const today = new Date();
    let targetDate = new Date(today);

    // 🎯 处理相对日期
    if (text.includes('明天')) {
      targetDate.setDate(today.getDate() + 1);
      console.log('📅 明天:', targetDate.toDateString());
    } else if (text.includes('后天')) {
      targetDate.setDate(today.getDate() + 2);
      console.log('📅 后天:', targetDate.toDateString());
    } else if (text.includes('三天后') || text.includes('3天后')) {
      targetDate.setDate(today.getDate() + 3);
      console.log('📅 三天后:', targetDate.toDateString());
    } else if (text.includes('四天后') || text.includes('4天后')) {
      targetDate.setDate(today.getDate() + 4);
      console.log('📅 四天后:', targetDate.toDateString());
    } else if (text.includes('五天后') || text.includes('5天后')) {
      targetDate.setDate(today.getDate() + 5);
      console.log('📅 五天后:', targetDate.toDateString());
    } else if (text.includes('六天后') || text.includes('6天后')) {
      targetDate.setDate(today.getDate() + 6);
      console.log('📅 六天后:', targetDate.toDateString());
    } else if (text.includes('七天后') || text.includes('7天后')) {
      targetDate.setDate(today.getDate() + 7);
      console.log('📅 七天后:', targetDate.toDateString());
    } else if (text.includes('八天后') || text.includes('8天后')) {
      targetDate.setDate(today.getDate() + 8);
      console.log('📅 八天后:', targetDate.toDateString());
    } else if (text.includes('九天后') || text.includes('9天后')) {
      targetDate.setDate(today.getDate() + 9);
      console.log('📅 九天后:', targetDate.toDateString());
    } else if (text.includes('十天后') || text.includes('10天后')) {
      targetDate.setDate(today.getDate() + 10);
      console.log('📅 十天后:', targetDate.toDateString());
    } else if (text.includes('下周')) {
      // 下周的具体处理
      targetDate = this.calculateNextWeekDate(text, today);
    } else {
      // 🔧 尝试提取数字+天后的格式
      const dayMatch = text.match(/(\d+)天后/);
      if (dayMatch) {
        const days = parseInt(dayMatch[1]);
        targetDate.setDate(today.getDate() + days);
        console.log(`📅 ${days}天后:`, targetDate.toDateString());
      } else {
        // 默认今天
        targetDate = new Date(today);
        targetDate.setHours(14, 0, 0, 0); // 默认下午2点
        console.log('📅 默认今天下午2点:', targetDate.toDateString());
      }
    }

    // 🕐 设置默认时间为下午2点（如果没有指定时间）
    if (!text.includes('上午') && !text.includes('下午') && !text.includes('点')) {
      targetDate.setHours(14, 0, 0, 0);
    }

    console.log('✅ 最终计算日期:', targetDate.toISOString());
    return targetDate;
  },

  // 🏢 从文本中提取地点
  extractLocationFromText(text) {
    const locationKeywords = ['北京', '上海', '杭州', '深圳', '广州', '南京', '成都', '西安', '武汉', '重庆'];
    for (const location of locationKeywords) {
      if (text.includes(location)) {
        console.log('🏢 提取到地点:', location);
        return location;
      }
    }
    return '';
  },

  // 💰 从文本中提取金额
  extractAmountFromText(text) {
    const match = text.match(/(\d+(?:\.\d+)?)元?/);
    return match ? parseFloat(match[1]) : 0;
  },

  // 🔮 判断是否为未来事件
  isFutureEvent(text) {
    const futureKeywords = ['明天', '后天', '天后', '下周', '下月', '下年'];
    return futureKeywords.some(keyword => text.includes(keyword));
  },

  // 🆔 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 📅 保存日程到所有位置
  async saveScheduleToAllLocations(scheduleData) {
    try {
      console.log('📅 保存日程到所有位置:', scheduleData);
      
      // 🔧 修复：确保字段名与日程页面期望的格式一致
      const formattedScheduleData = {
        id: scheduleData.id,
        title: scheduleData.title || '',
        description: scheduleData.description || '',
        start_time: scheduleData.startTime, // 日程页面期望的字段名
        end_time: scheduleData.endTime,     // 日程页面期望的字段名
        location: scheduleData.location || '',
        category: scheduleData.category || '',
        source: scheduleData.source || 'AI分析',
        created_at: scheduleData.createdAt || new Date().toISOString(),
        type: 'schedule',
        status: 'active'
      };
      
      console.log('🔧 格式化后的日程数据:', formattedScheduleData);
      
      // 1️⃣ 保存到主要存储位置
      const localSchedules = wx.getStorageSync('local_schedules') || [];
      localSchedules.push(formattedScheduleData);
      wx.setStorageSync('local_schedules', localSchedules);
      console.log('✅ 已保存到 local_schedules');

      // 2️⃣ 保存到列表格式
      const schedulesList = wx.getStorageSync('local_schedules_list') || [];
      schedulesList.push(formattedScheduleData);
      wx.setStorageSync('local_schedules_list', schedulesList);
      console.log('✅ 已保存到 local_schedules_list');

      // 3️⃣ 保存到按日期分组的格式（日程页面主要使用）
      const dateKey = this.formatDateKey(formattedScheduleData.start_time);
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      if (!allSchedules[dateKey]) {
        allSchedules[dateKey] = [];
      }
      
      // 🔧 确保保存的格式与日程页面期望的一致
      const displaySchedule = {
        id: formattedScheduleData.id,
        title: formattedScheduleData.title,
        time: this.formatTimeFromISO(formattedScheduleData.start_time),
        startTime: formattedScheduleData.start_time,
        endTime: formattedScheduleData.end_time,
        description: formattedScheduleData.description,
        location: formattedScheduleData.location,
        icon: 'time',
        type: 'meeting',
        dataType: 'schedule',
        displayIcon: '📅'
      };
      
      allSchedules[dateKey].push(displaySchedule);
      wx.setStorageSync('allSchedules', allSchedules);
      console.log(`✅ 已保存到 allSchedules[${dateKey}]`);

      // 4️⃣ 保存到日程页面数据格式
      const schedulePageData = wx.getStorageSync('schedulePageData') || [];
      schedulePageData.push(formattedScheduleData);
      wx.setStorageSync('schedulePageData', schedulePageData);
      console.log('✅ 已保存到 schedulePageData');

      // 5️⃣ 通知应用刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshSchedules = true;
      }

      return { success: true, data: formattedScheduleData };
    } catch (error) {
      console.error('❌ 保存日程失败:', error);
      throw error;
    }
  },

  // ✅ 保存待办到所有位置
  async saveTodoToAllLocations(todoData) {
    try {
      console.log('✅ 保存待办到所有位置:', todoData);
      
      // 1️⃣ 保存到主要存储位置
      const localTodos = wx.getStorageSync('local_todos') || [];
      localTodos.push(todoData);
      wx.setStorageSync('local_todos', localTodos);
      console.log('✅ 已保存到 local_todos');

      // 2️⃣ 保存到列表格式
      const todosList = wx.getStorageSync('local_todos_list') || [];
      todosList.push(todoData);
      wx.setStorageSync('local_todos_list', todosList);
      console.log('✅ 已保存到 local_todos_list');

      // 3️⃣ 保存到按日期分组的格式
      const dateKey = todoData.dueDate ? this.formatDateKey(todoData.dueDate) : this.formatDateKey(new Date().toISOString());
      const allTodos = wx.getStorageSync('allTodos') || {};
      if (!allTodos[dateKey]) {
        allTodos[dateKey] = [];
      }
      allTodos[dateKey].push(todoData);
      wx.setStorageSync('allTodos', allTodos);
      console.log(`✅ 已保存到 allTodos[${dateKey}]`);

      // 4️⃣ 保存到待办页面数据格式
      const todoPageData = wx.getStorageSync('todoPageData') || [];
      todoPageData.push(todoData);
      wx.setStorageSync('todoPageData', todoPageData);
      console.log('✅ 已保存到 todoPageData');

      // 5️⃣ 通知应用刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshTodos = true;
      }

      return { success: true, data: todoData };
    } catch (error) {
      console.error('❌ 保存待办失败:', error);
      throw error;
    }
  },

  // 💰 保存财务到所有位置
  async saveFinanceToAllLocations(financeData) {
    try {
      console.log('💰 保存财务到所有位置:', financeData);
      
      // 1️⃣ 保存到主要存储位置
      const localExpenses = wx.getStorageSync('local_expenses') || [];
      localExpenses.push(financeData);
      wx.setStorageSync('local_expenses', localExpenses);
      console.log('✅ 已保存到 local_expenses');

      // 2️⃣ 保存到列表格式
      const expensesList = wx.getStorageSync('local_expenses_list') || [];
      expensesList.push(financeData);
      wx.setStorageSync('local_expenses_list', expensesList);
      console.log('✅ 已保存到 local_expenses_list');

      // 3️⃣ 保存到按日期分组的格式
      const dateKey = this.formatDateKey(financeData.date);
      const allFinances = wx.getStorageSync('allFinances') || {};
      if (!allFinances[dateKey]) {
        allFinances[dateKey] = [];
      }
      allFinances[dateKey].push(financeData);
      wx.setStorageSync('allFinances', allFinances);
      console.log(`✅ 已保存到 allFinances[${dateKey}]`);

      // 4️⃣ 保存到财务页面数据格式
      const financePageData = wx.getStorageSync('financePageData') || [];
      financePageData.push(financeData);
      wx.setStorageSync('financePageData', financePageData);
      console.log('✅ 已保存到 financePageData');

      // 5️⃣ 通知应用刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshFinances = true;
      }

      return { success: true, data: financeData };
    } catch (error) {
      console.error('❌ 保存财务失败:', error);
      throw error;
    }
  },

  // 🗓️ 格式化日期键（用于按日期分组）
  formatDateKey(dateString) {
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const dateKey = `${year}-${month}-${day}`;
      console.log(`🗓️ 格式化日期键: ${dateString} → ${dateKey}`);
      return dateKey;
    } catch (error) {
      console.error('❌ 格式化日期键失败:', error);
      return new Date().toISOString().split('T')[0];
    }
  },

  // 🕐 从ISO时间格式化为显示时间
  formatTimeFromISO(isoTime) {
    try {
      const date = new Date(isoTime);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch (error) {
      console.error('❌ 格式化时间失败:', error);
      return '00:00';
    }
  },

  // 🔄 本地文本处理（降级方案 - 保持简单）
  async processTextInputLocally(content) {
    try {
      console.log('🔄 开始本地文本处理:', content);
      // 简单的本地分类逻辑
      let type = 'schedule'; // 默认为日程
      let title = content;
      
      // 基本关键词检测
      if (content.includes('元') || content.includes('钱') || content.includes('花') || content.includes('买')) {
        type = 'finance';
      } else if (content.includes('完成') || content.includes('处理') || content.includes('任务')) {
        type = 'todo';
      }

      // 🎯 直接使用dataApi创建数据
      try {
        if (type === 'schedule') {
          const result = await dataApi.createSchedule({
            title: title,
            description: content,
            startTime: new Date().toISOString(),
            source: '本地处理'
          });
          if (result && result.success) {
            wx.showToast({ title: '日程创建成功', icon: 'success' });
          }
        } else if (type === 'finance') {
          const amount = content.match(/(\d+)元?/) ? parseInt(content.match(/(\d+)元?/)[1]) : 0;
          const result = await dataApi.createExpense({
            amount: amount,
            description: content,
            category: '其他',
            source: '本地处理'
          });
          if (result && result.success) {
            wx.showToast({ title: '财务记录创建成功', icon: 'success' });
          }
        } else if (type === 'todo') {
          const result = await dataApi.createTodo({
            title: title,
            description: content,
            priority: 'medium',
            source: '本地处理'
          });
          if (result && result.success) {
            wx.showToast({ title: '待办创建成功', icon: 'success' });
          }
        }
        
        // 刷新界面
        await this.refreshRecentInputs();
        
      } catch (error) {
        console.error('❌ 本地数据创建失败:', error);
        throw error;
      }
      
      console.log('✅ 本地文本处理完成');
    } catch (error) {
      console.error('❌ 本地文本处理失败:', error);
      // 最终兜底：显示基本创建选项
      wx.showModal({
        title: '处理失败',
        content: '文本处理失败，是否手动创建记录？',
        confirmText: '手动创建',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.showManualCreateOptions(content);
          }
        }
      });
    }
  },

  // 💾 删除复杂的测试数据保存方法，恢复简单逻辑
  // 🧪 测试基本数据创建 - 简化版
  async testBasicDataCreation() {
    try {
      console.log('🧪 === 开始基本数据创建测试 ===');
      
      wx.showLoading({
        title: '创建测试数据...',
        mask: true
      });

      // 📊 简单测试数据
      const testTexts = [
        '明天下午2点开会',
        '买咖啡花了25元', 
        '完成工作报告'
      ];

      const results = [];
      
      for (let i = 0; i < testTexts.length; i++) {
        const text = testTexts[i];
        
        try {
          console.log(`📝 测试 ${i + 1}: ${text}`);
          
          // 🎯 直接使用AI分类处理
          await this.processTextWithAI(text);
          
          results.push({
            success: true,
            text: text
          });
          
          // 短暂延迟
          await this.delay(500);
          
        } catch (error) {
          console.error(`❌ 测试失败 ${i + 1}:`, error);
          results.push({
            success: false,
            text: text,
            error: error.message
          });
        }
      }
      
      wx.hideLoading();
      
      const successCount = results.filter(r => r.success).length;
      
      wx.showModal({
        title: '🧪 测试完成',
        content: `成功创建 ${successCount}/${results.length} 条数据\n\n现在可以检查各个页面是否显示数据！`,
        confirmText: '确定',
        showCancel: false,
        success: () => {
          // 刷新界面数据
          this.refreshAllData();
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 测试数据创建失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },

  // 📋 显示手动创建选项
  showManualCreateOptions(content) {
    wx.showActionSheet({
      itemList: [
        '📅 创建为日程',
        '✅ 创建为待办',
        '💰 创建为财务记录'
      ],
      success: (res) => {
        const types = ['schedule', 'todo', 'finance'];
        const selectedType = types[res.tapIndex];
        wx.showToast({
          title: `已创建${this.getTypeDisplayName(selectedType)}`,
          icon: 'success'
        });
        console.log(`🔧 手动创建${selectedType}:`, content);
      }
    });
  },

  // 🔧 获取类型显示名称
  getTypeDisplayName(type) {
    const typeMap = {
      'schedule': '日程',
      'todo': '待办',
      'finance': '财务记录'
    };
    return typeMap[type] || '记录';
  },

  // 🔧 测试快速操作按钮
  testQuickActions() {
    console.log('🔧 === 测试快速操作按钮 ===');
    wx.showModal({
      title: '🔧 快速操作按钮测试',
      content: '点击确定测试所有快速操作按钮的绑定情况',
      success: (res) => {
        if (res.confirm) {
          setTimeout(() => {
            console.log('📅 测试addSchedule方法...');
            this.addSchedule();
          }, 500);
          
          setTimeout(() => {
            console.log('✅ 测试addTodo方法...');
            this.addTodo();
          }, 1500);
          
          setTimeout(() => {
            console.log('📝 测试addNote方法...');  
            this.addNote();
          }, 2500);
          
          setTimeout(() => {
            console.log('🔧 测试showSystemDebugMenu方法...');
            this.showSystemDebugMenu();
          }, 3500);
        }
      }
    });
  },

  // 🔍 批量重新分类诊断检查（新功能）
  async diagnoseBatchReclassification() {
    try {
      console.log('🔍 === 开始批量重新分类诊断 ===');
      
      wx.showLoading({
        title: '正在诊断系统状态...',
        mask: true
      });

      let diagnosisReport = '🔍 批量重新分类诊断报告\n\n';
      
      // 1️⃣ 检查AI服务状态
      console.log('1️⃣ 检查AI服务状态...');
      const aiStatus = aiService.getStatus();
      diagnosisReport += `🤖 AI服务检查:\n`;
      diagnosisReport += `• 服务启用: ${aiStatus.enabled ? '✅ 是' : '❌ 否'}\n`;
      diagnosisReport += `• 服务配置: ${aiStatus.configured ? '✅ 是' : '❌ 否'}\n`;
      diagnosisReport += `• API密钥: ${aiStatus.hasApiKey ? '✅ 有' : '❌ 无'}\n\n`;

      // 2️⃣ 检查历史数据
      console.log('2️⃣ 检查历史数据...');
      const allData = await this.getAllHistoricalData();
      diagnosisReport += `📊 历史数据检查:\n`;
      diagnosisReport += `• 日程数据: ${allData.schedules.length} 条\n`;
      diagnosisReport += `• 待办数据: ${allData.todos.length} 条\n`;
      diagnosisReport += `• 财务数据: ${allData.expenses.length} 条\n`;
      diagnosisReport += `• 可处理文本: ${allData.originalTexts.length} 条\n`;
      diagnosisReport += `• 总计数据: ${allData.total} 条\n\n`;

      // 3️⃣ 检查存储状态
      console.log('3️⃣ 检查存储状态...');
      const storageKeys = [
        'local_schedules', 'local_todos', 'local_expenses',
        'local_schedules_list', 'local_todos_list', 'local_expenses_list',
        'allSchedules', 'allTodos', 'allFinances'
      ];
      
      diagnosisReport += `💾 存储检查:\n`;
      storageKeys.forEach(key => {
        const data = wx.getStorageSync(key) || [];
        const count = Array.isArray(data) ? data.length : Object.keys(data).length;
        diagnosisReport += `• ${key}: ${count} 项\n`;
      });
      diagnosisReport += '\n';

      // 4️⃣ 测试AI分类功能
      console.log('4️⃣ 测试AI分类功能...');
      let aiTestResult = '❌ 测试失败';
      try {
        const testResult = await aiService.intelligentClassify('测试文本');
        aiTestResult = testResult ? '✅ 测试成功' : '❌ 返回空结果';
      } catch (error) {
        aiTestResult = `❌ 测试失败: ${error.message}`;
      }
      diagnosisReport += `🧪 AI分类测试:\n• 测试结果: ${aiTestResult}\n\n`;

      // 5️⃣ 生成诊断结论
      let conclusion = '';
      if (!aiStatus.enabled) {
        conclusion = '❌ 问题：AI服务未启用';
      } else if (allData.total === 0) {
        conclusion = '⚠️ 问题：没有历史数据可以重新分类';
      } else if (aiTestResult.includes('失败')) {
        conclusion = '❌ 问题：AI分类功能异常';
      } else {
        conclusion = '✅ 系统状态正常，可以执行批量重新分类';
      }

      diagnosisReport += `🎯 诊断结论:\n${conclusion}\n\n`;

      // 6️⃣ 提供解决建议
      diagnosisReport += `💡 解决建议:\n`;
      if (!aiStatus.enabled) {
        diagnosisReport += `• 检查网络连接\n• 重启小程序\n• 联系技术支持`;
      } else if (allData.total === 0) {
        diagnosisReport += `• 先创建一些数据记录\n• 尝试输入文本创建日程、待办或财务记录\n• 创建数据后再执行批量重新分类`;
      } else if (aiTestResult.includes('失败')) {
        diagnosisReport += `• 检查网络连接\n• 稍后重试\n• 查看控制台错误信息`;
      } else {
        diagnosisReport += `• 系统正常，可以直接使用批量重新分类功能\n• 建议先备份重要数据\n• 在稳定网络环境下操作`;
      }

      wx.hideLoading();

      // 显示诊断报告
      wx.showModal({
        title: '🔍 诊断报告',
        content: diagnosisReport,
        confirmText: allData.total > 0 && aiStatus.enabled ? '立即执行批量重新分类' : '我知道了',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm && allData.total > 0 && aiStatus.enabled) {
            // 如果诊断通过，直接执行批量重新分类
            this.batchReclassifyHistoricalData();
          }
        }
      });

      // 输出详细的调试信息到控制台
      console.log('🔍 === 详细诊断信息 ===');
      console.log('🤖 AI状态:', aiStatus);
      console.log('📊 数据统计:', allData.byType);
      console.log('💾 存储详情:', storageKeys.map(key => ({
        key,
        data: wx.getStorageSync(key)
      })));

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 诊断检查失败:', error);
      wx.showModal({
        title: '❌ 诊断失败',
        content: `诊断过程中出现错误:\n${error.message}\n\n请查看控制台获取详细信息`,
        showCancel: false
      });
    }
  },

  // 🧪 测试基本数据创建
  async testBasicDataCreation() {
    try {
      console.log('🧪 === 开始基本数据创建测试 ===');
      
      wx.showLoading({
        title: '创建测试数据...',
        mask: true
      });

      // 📊 简单测试数据
      const testTexts = [
        '明天下午2点开会',
        '买咖啡花了25元', 
        '完成工作报告'
      ];

      const results = [];
      
      for (let i = 0; i < testTexts.length; i++) {
        const text = testTexts[i];
        
        try {
          console.log(`📝 测试 ${i + 1}: ${text}`);
          
          // 🎯 直接使用AI分类处理
          await this.processTextWithAI(text);
          
          results.push({
            success: true,
            text: text
          });
          
          // 短暂延迟
          await this.delay(500);
          
        } catch (error) {
          console.error(`❌ 测试失败 ${i + 1}:`, error);
          results.push({
            success: false,
            text: text,
            error: error.message
          });
        }
      }
      
      wx.hideLoading();
      
      const successCount = results.filter(r => r.success).length;
      
      wx.showModal({
        title: '🧪 测试完成',
        content: `成功创建 ${successCount}/${results.length} 条数据\n\n现在可以检查各个页面是否显示数据！`,
        confirmText: '确定',
        showCancel: false,
        success: () => {
          // 刷新界面数据
          this.refreshAllData();
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 测试数据创建失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },

  // 🧪 显示系统调试菜单
  showSystemDebugMenu() {
    console.log('🔧 === 显示系统调试菜单 ===');
    wx.showActionSheet({
      itemList: [
        '🔄 重新创建测试数据',
        '🗓️ 验证日程页面显示',
        '📊 检查数据保存格式',
        '🕵️ 追踪"五天后去上海"',
        '🧹 清空所有数据'
      ],
      success: (res) => {
        console.log('🎯 用户选择了调试菜单选项:', res.tapIndex);
        const actions = [
          () => this.recreateTestDataWithCorrectFormat(),
          () => this.verifySchedulePageDisplay(),
          () => this.checkDataSaveFormat(),
          () => this.debugSpecificInput(),
          () => this.confirmClearAllData()
        ];
        if (res.tapIndex < actions.length) {
          console.log('✅ 正在执行选中的调试功能...');
          actions[res.tapIndex]();
        }
      },
      fail: (res) => {
        console.log('用户取消了调试菜单');
      }
    });
  },

  // 📊 验证数据保存位置
  verifyDataStorage() {
    try {
      console.log('📊 === 验证数据保存位置 ===');
      
      // 检查所有存储位置
      const storageReport = {
        schedules: {
          local_schedules: wx.getStorageSync('local_schedules') || [],
          local_schedules_list: wx.getStorageSync('local_schedules_list') || [],
          allSchedules: wx.getStorageSync('allSchedules') || {},
          schedulePageData: wx.getStorageSync('schedulePageData') || []
        },
        todos: {
          local_todos: wx.getStorageSync('local_todos') || [],
          local_todos_list: wx.getStorageSync('local_todos_list') || [],
          allTodos: wx.getStorageSync('allTodos') || {},
          todoPageData: wx.getStorageSync('todoPageData') || []
        },
        finances: {
          local_expenses: wx.getStorageSync('local_expenses') || [],
          local_expenses_list: wx.getStorageSync('local_expenses_list') || [],
          allFinances: wx.getStorageSync('allFinances') || {},
          financePageData: wx.getStorageSync('financePageData') || []
        }
      };

      // 生成详细报告
      let report = '📊 数据保存位置验证:\n\n';
      
      // 日程数据
      report += '📅 日程数据:\n';
      report += `• local_schedules: ${storageReport.schedules.local_schedules.length} 条\n`;
      report += `• local_schedules_list: ${storageReport.schedules.local_schedules_list.length} 条\n`;
      report += `• allSchedules: ${Object.keys(storageReport.schedules.allSchedules).length} 个日期\n`;
      report += `• schedulePageData: ${storageReport.schedules.schedulePageData.length} 条\n\n`;

      // 待办数据
      report += '✅ 待办数据:\n';
      report += `• local_todos: ${storageReport.todos.local_todos.length} 条\n`;
      report += `• local_todos_list: ${storageReport.todos.local_todos_list.length} 条\n`;
      report += `• allTodos: ${Object.keys(storageReport.todos.allTodos).length} 个日期\n`;
      report += `• todoPageData: ${storageReport.todos.todoPageData.length} 条\n\n`;

      // 财务数据
      report += '💰 财务数据:\n';
      report += `• local_expenses: ${storageReport.finances.local_expenses.length} 条\n`;
      report += `• local_expenses_list: ${storageReport.finances.local_expenses_list.length} 条\n`;
      report += `• allFinances: ${Object.keys(storageReport.finances.allFinances).length} 个日期\n`;
      report += `• financePageData: ${storageReport.finances.financePageData.length} 条\n\n`;

      // 分析问题
      const scheduleTotal = storageReport.schedules.local_schedules.length;
      const todoTotal = storageReport.todos.local_todos.length;
      const financeTotal = storageReport.finances.local_expenses.length;

      report += '🔍 问题分析:\n';
      if (scheduleTotal === 0 && todoTotal === 0 && financeTotal === 0) {
        report += '❌ 没有发现任何数据\n';
        report += '💡 建议：先创建一些测试数据\n';
      } else {
        if (scheduleTotal > 0) {
          const scheduleDates = Object.keys(storageReport.schedules.allSchedules);
          report += `✅ 日程数据正常，涉及日期: ${scheduleDates.join(', ')}\n`;
        }
        if (todoTotal > 0) {
          report += `✅ 待办数据正常\n`;
        }
        if (financeTotal > 0) {
          report += `✅ 财务数据正常\n`;
        }
      }

      console.log('📊 存储验证报告:', storageReport);

      wx.showModal({
        title: '📊 数据保存验证',
        content: report,
        confirmText: scheduleTotal === 0 ? '创建测试数据' : '查看详细日期',
        cancelText: '确定',
        success: (res) => {
          if (res.confirm) {
            if (scheduleTotal === 0) {
              this.createSimpleTestData();
            } else {
              this.showDetailedDateInfo(storageReport);
            }
          }
        }
      });

    } catch (error) {
      console.error('❌ 验证数据保存失败:', error);
      wx.showToast({
        title: '验证失败',
        icon: 'error'
      });
    }
  },

  // 📅 显示详细日期信息
  showDetailedDateInfo(storageReport) {
    const scheduleDates = Object.keys(storageReport.schedules.allSchedules);
    const todoDates = Object.keys(storageReport.todos.allTodos);
    const financeDates = Object.keys(storageReport.finances.allFinances);

    let dateReport = '📅 详细日期分布:\n\n';
    
    if (scheduleDates.length > 0) {
      dateReport += '📅 日程日期:\n';
      scheduleDates.forEach(date => {
        const count = storageReport.schedules.allSchedules[date].length;
        dateReport += `• ${date}: ${count} 条日程\n`;
      });
      dateReport += '\n';
    }

    if (todoDates.length > 0) {
      dateReport += '✅ 待办日期:\n';
      todoDates.forEach(date => {
        const count = storageReport.todos.allTodos[date].length;
        dateReport += `• ${date}: ${count} 条待办\n`;
      });
      dateReport += '\n';
    }

    if (financeDates.length > 0) {
      dateReport += '💰 财务日期:\n';
      financeDates.forEach(date => {
        const count = storageReport.finances.allFinances[date].length;
        dateReport += `• ${date}: ${count} 条财务\n`;
      });
    }

    if (scheduleDates.length === 0 && todoDates.length === 0 && financeDates.length === 0) {
      dateReport = '❌ 没有按日期分组的数据\n这可能是数据保存格式问题';
    }

    wx.showModal({
      title: '📅 日期分布详情',
      content: dateReport,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 🗓️ 测试相对日期计算
  testDateCalculation() {
    const testTexts = [
      '明天开会',
      '后天去北京',
      '三天后去杭州',
      '四天后返回',
      '下周一会议'
    ];

    let report = '🗓️ 相对日期计算测试:\n\n';
    const today = new Date();
    
    testTexts.forEach((text, index) => {
      const calculatedDate = this.calculateDateFromText(text);
      const daysDiff = Math.ceil((calculatedDate - today) / (1000 * 60 * 60 * 24));
      report += `${index + 1}. "${text}"\n`;
      report += `   计算结果: ${calculatedDate.toLocaleDateString()}\n`;
      report += `   相差天数: ${daysDiff} 天\n\n`;
    });

    wx.showModal({
      title: '🗓️ 日期计算测试',
      content: report,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 🗓️ 调试特定输入"五天后去上海"
  async debugSpecificInput() {
    try {
      // 🔧 让用户选择要调试的输入
      wx.showModal({
        title: '🕵️ 调试相对日期输入',
        content: '请选择要调试的输入类型',
        confirmText: '7天后去杭州',
        cancelText: '5天后去上海',
        success: async (res) => {
          const testInput = res.confirm ? "7天后去杭州" : "5天后去上海";
          await this.debugSpecificInputText(testInput);
        }
      });
    } catch (error) {
      console.error('❌ 启动调试失败:', error);
    }
  },

  // 🕵️ 调试特定输入文本
  async debugSpecificInputText(testInput) {
    try {
      wx.showLoading({
        title: `调试"${testInput}"...`,
        mask: true
      });

      console.log('🕵️ === 开始调试特定输入 ===');
      console.log('📝 测试输入:', testInput);

      // 1️⃣ 测试日期计算
      const calculatedDate = this.calculateDateFromText(testInput);
      const dateKey = this.formatDateKey(calculatedDate.toISOString());
      
      console.log('🗓️ 日期计算结果:', {
        originalText: testInput,
        calculatedDate: calculatedDate,
        dateString: calculatedDate.toDateString(),
        isoString: calculatedDate.toISOString(),
        dateKey: dateKey
      });

      // 2️⃣ 测试AI分类
      let aiResult = null;
      try {
        console.log('🤖 开始AI分类测试...');
        aiResult = await aiService.intelligentClassify(testInput);
        console.log('✅ AI分类结果:', aiResult);
      } catch (aiError) {
        console.error('❌ AI分类失败:', aiError);
      }

      // 3️⃣ 检查当前存储的数据
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      const localSchedules = wx.getStorageSync('local_schedules') || [];
      
      console.log('📊 当前存储检查:', {
        allSchedulesKeys: Object.keys(allSchedules),
        targetDateKey: dateKey,
        hasTargetDate: !!allSchedules[dateKey],
        targetDateData: allSchedules[dateKey] || [],
        localSchedulesCount: localSchedules.length
      });

      // 4️⃣ 检查是否有包含目标城市的记录
      const targetCity = testInput.includes('杭州') ? '杭州' : '上海';
      const cityRecords = localSchedules.filter(item => 
        item.title?.includes(targetCity) || 
        item.description?.includes(targetCity) || 
        item.location?.includes(targetCity)
      );
      
      console.log(`🏢 ${targetCity}相关记录:`, cityRecords);

      wx.hideLoading();

      // 生成调试报告
      let report = `🗓️ "${testInput}" 调试报告:\n\n`;
      
      // 日期计算部分
      report += `📅 日期计算:\n`;
      report += `• 计算结果: ${calculatedDate.toLocaleDateString()}\n`;
      report += `• 距今天数: ${Math.ceil((calculatedDate - new Date()) / (1000 * 60 * 60 * 24))} 天\n`;
      report += `• 存储键值: ${dateKey}\n\n`;

      // AI分类部分
      report += `🤖 AI分类:\n`;
      if (aiResult) {
        const aiItem = aiResult.items[0];
        report += `• 识别类型: ${aiItem.primaryType}\n`;
        report += `• 置信度: ${Math.round(aiItem.confidence * 100)}%\n`;
        report += `• 提取标题: ${aiItem.title || '无'}\n`;
        report += `• 提取地点: ${aiItem.extractedInfo?.location || '无'}\n`;
      } else {
        report += `• AI分类: ❌ 失败或离线\n`;
      }
      report += '\n';

      // 存储检查部分
      report += `💾 存储检查:\n`;
      report += `• 日程总数: ${localSchedules.length} 条\n`;
      report += `• 目标日期(${dateKey})数据: ${allSchedules[dateKey]?.length || 0} 条\n`;
      report += `• ${targetCity}相关记录: ${cityRecords.length} 条\n\n`;

      // 问题诊断
      report += `🔍 问题诊断:\n`;
      if (!aiResult) {
        report += `❌ AI服务离线，无法正确分类\n`;
      } else if (aiResult.items[0].primaryType !== 'schedule') {
        report += `❌ AI未识别为日程类型(${aiResult.items[0].primaryType})\n`;
      } else if (!allSchedules[dateKey] || allSchedules[dateKey].length === 0) {
        report += `❌ 目标日期(${dateKey})没有数据\n`;
      } else if (cityRecords.length === 0) {
        report += `❌ 没有找到包含"${targetCity}"的记录\n`;
      } else {
        report += `✅ 数据正常，请检查日程页面是否查看了正确日期\n`;
      }

      wx.showModal({
        title: '🗓️ 调试报告',
        content: report,
        confirmText: cityRecords.length === 0 ? '立即重新创建' : '查看详细数据',
        cancelText: '手动创建此数据',
        success: (res) => {
          if (res.confirm) {
            if (cityRecords.length === 0) {
              // 重新创建数据
              this.recreateSpecificSchedule(testInput, calculatedDate, dateKey, targetCity);
            } else {
              // 显示详细数据
              this.showDetailedScheduleData(cityRecords, allSchedules[dateKey]);
            }
          } else {
            // 手动创建这条特定数据
            this.createSpecificScheduleData(testInput, calculatedDate, targetCity);
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 调试失败:', error);
      wx.showToast({
        title: '调试失败',
        icon: 'error'
      });
    }
  },

  // 🔧 创建特定的日程数据
  async createSpecificScheduleData(inputText, calculatedDate, targetCity) {
    try {
      wx.showLoading({
        title: `创建"${inputText}"日程...`,
        mask: true
      });

      console.log('🔧 手动创建特定日程数据...');
      
      const scheduleData = {
        id: this.generateId(),
        title: inputText,
        description: inputText,
        startTime: calculatedDate.toISOString(),
        endTime: new Date(calculatedDate.getTime() + 60 * 60 * 1000).toISOString(),
        location: targetCity,
        category: '出差',
        source: '手动创建',
        createdAt: new Date().toISOString(),
        type: 'schedule'
      };

      // 保存到所有位置
      await this.saveScheduleToAllLocations(scheduleData);
      
      wx.hideLoading();
      
      wx.showModal({
        title: '✅ 创建成功',
        content: `日程已创建并保存:\n• 日期: ${calculatedDate.toLocaleDateString()}\n• 标题: ${inputText}\n• 地点: ${targetCity}\n\n请到日程页面的 ${calculatedDate.toLocaleDateString()} 查看！`,
        confirmText: '去日程页面',
        cancelText: '确定',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
          // 刷新数据
          this.refreshAllData();
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 创建特定日程失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  // 🕵️ 完整流程追踪调试
  async fullProcessDebug() {
    try {
      const testInput = "五天后去上海";
      
      wx.showModal({
        title: '🕵️ 完整流程追踪',
        content: `即将完整追踪"${testInput}"的处理过程:\n\n1. AI分类识别\n2. 日期计算\n3. 数据创建\n4. 多位置保存\n5. 验证结果\n\n确认开始？`,
        confirmText: '开始追踪',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.executeFullProcessTrace(testInput);
          }
        }
      });
    } catch (error) {
      console.error('❌ 启动完整追踪失败:', error);
    }
  },

  // 🔍 执行完整流程追踪
  async executeFullProcessTrace(inputText) {
    const traceLog = [];
    
    try {
      wx.showLoading({
        title: '步骤1/5: AI分析中...',
        mask: true
      });

      // 步骤1: AI分类
      traceLog.push(`📝 输入文本: "${inputText}"`);
      traceLog.push(`⏰ 开始时间: ${new Date().toLocaleString()}`);
      traceLog.push('');
      traceLog.push('🤖 步骤1: AI分类分析');
      
      let aiResult = null;
      try {
        aiResult = await aiService.intelligentClassify(inputText);
        if (aiResult && aiResult.items && aiResult.items.length > 0) {
          const aiItem = aiResult.items[0];
          traceLog.push(`✅ AI分类成功`);
          traceLog.push(`   识别类型: ${aiItem.primaryType}`);
          traceLog.push(`   置信度: ${Math.round(aiItem.confidence * 100)}%`);
          traceLog.push(`   提取标题: ${aiItem.title || inputText}`);
          traceLog.push(`   来源: ${aiResult.source}`);
        } else {
          traceLog.push(`❌ AI分类返回空结果`);
        }
      } catch (error) {
        traceLog.push(`❌ AI分类失败: ${error.message}`);
      }

      // 步骤2: 日期计算
      wx.showLoading({ title: '步骤2/5: 日期计算中...', mask: true });
      traceLog.push('');
      traceLog.push('🗓️ 步骤2: 日期计算');
      
      const calculatedDate = this.calculateDateFromText(inputText);
      const dateKey = this.formatDateKey(calculatedDate.toISOString());
      const today = new Date();
      const daysDiff = Math.ceil((calculatedDate - today) / (1000 * 60 * 60 * 24));
      
      traceLog.push(`✅ 日期计算完成`);
      traceLog.push(`   计算结果: ${calculatedDate.toLocaleDateString()}`);
      traceLog.push(`   距今天数: ${daysDiff} 天`);
      traceLog.push(`   存储键值: ${dateKey}`);
      traceLog.push(`   ISO格式: ${calculatedDate.toISOString()}`);

      // 步骤3: 数据创建
      wx.showLoading({ title: '步骤3/5: 创建数据中...', mask: true });
      traceLog.push('');
      traceLog.push('📅 步骤3: 数据创建');
      
      const scheduleData = {
        id: this.generateId(),
        title: inputText,
        description: inputText,
        startTime: calculatedDate.toISOString(),
        endTime: new Date(calculatedDate.getTime() + 60 * 60 * 1000).toISOString(),
        location: '上海',
        category: '会议',
        source: '流程追踪调试',
        createdAt: new Date().toISOString(),
        type: 'schedule'
      };
      
      traceLog.push(`✅ 数据结构创建完成`);
      traceLog.push(`   ID: ${scheduleData.id}`);
      traceLog.push(`   标题: ${scheduleData.title}`);
      traceLog.push(`   地点: ${scheduleData.location}`);
      traceLog.push(`   开始时间: ${new Date(scheduleData.startTime).toLocaleString()}`);

      // 步骤4: 多位置保存
      wx.showLoading({ title: '步骤4/5: 保存数据中...', mask: true });
      traceLog.push('');
      traceLog.push('💾 步骤4: 多位置保存');
      
      try {
        await this.saveScheduleToAllLocations(scheduleData);
        traceLog.push(`✅ 数据保存成功`);
      } catch (saveError) {
        traceLog.push(`❌ 数据保存失败: ${saveError.message}`);
      }

      // 步骤5: 验证结果
      wx.showLoading({ title: '步骤5/5: 验证结果中...', mask: true });
      traceLog.push('');
      traceLog.push('🔍 步骤5: 验证保存结果');
      
      const verificationResults = this.verifyScheduleSaveResults(dateKey, scheduleData.id, inputText);
      traceLog.push(`✅ 验证完成`);
      traceLog.push(`   local_schedules: ${verificationResults.localSchedules ? '✅' : '❌'}`);
      traceLog.push(`   allSchedules[${dateKey}]: ${verificationResults.allSchedules ? '✅' : '❌'}`);
      traceLog.push(`   schedulePageData: ${verificationResults.schedulePageData ? '✅' : '❌'}`);

      wx.hideLoading();

      // 显示完整追踪报告
      this.showFullTraceReport(traceLog, verificationResults, calculatedDate, dateKey);

    } catch (error) {
      wx.hideLoading();
      traceLog.push('');
      traceLog.push(`❌ 流程追踪异常: ${error.message}`);
      console.error('❌ 完整流程追踪失败:', error);
      
      wx.showModal({
        title: '❌ 追踪失败',
        content: traceLog.join('\n'),
        showCancel: false
      });
    }
  },

  // 🔍 验证日程保存结果
  verifyScheduleSaveResults(dateKey, scheduleId, inputText) {
    const results = {
      localSchedules: false,
      allSchedules: false,
      schedulePageData: false,
      details: {}
    };

    try {
      // 检查 local_schedules
      const localSchedules = wx.getStorageSync('local_schedules') || [];
      results.localSchedules = localSchedules.some(item => 
        item.id === scheduleId || item.title?.includes('上海')
      );
      results.details.localSchedulesCount = localSchedules.length;

      // 检查 allSchedules
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      results.allSchedules = !!(allSchedules[dateKey] && allSchedules[dateKey].some(item => 
        item.id === scheduleId || item.title?.includes('上海')
      ));
      results.details.allSchedulesDateKeys = Object.keys(allSchedules);
      results.details.targetDateCount = allSchedules[dateKey]?.length || 0;

      // 检查 schedulePageData
      const schedulePageData = wx.getStorageSync('schedulePageData') || [];
      results.schedulePageData = schedulePageData.some(item => 
        item.id === scheduleId || item.title?.includes('上海')
      );
      results.details.schedulePageDataCount = schedulePageData.length;

    } catch (error) {
      console.error('❌ 验证保存结果失败:', error);
    }

    return results;
  },

  // 📋 显示完整追踪报告
  showFullTraceReport(traceLog, verificationResults, calculatedDate, dateKey) {
    const logText = traceLog.join('\n');
    console.log('🕵️ === 完整流程追踪报告 ===');
    console.log(logText);
    console.log('🔍 验证结果:', verificationResults);

    let conclusion = '';
    const allPassed = verificationResults.localSchedules && 
                     verificationResults.allSchedules && 
                     verificationResults.schedulePageData;

    if (allPassed) {
      conclusion = `✅ 所有步骤成功！\n\n📍 数据已保存到:\n• 日期: ${calculatedDate.toLocaleDateString()}\n• 键值: ${dateKey}\n\n请到日程页面查看 ${calculatedDate.toLocaleDateString()} 的安排！`;
    } else {
      conclusion = `⚠️ 部分步骤失败\n\n失败的保存位置:\n${!verificationResults.localSchedules ? '• local_schedules\n' : ''}${!verificationResults.allSchedules ? '• allSchedules\n' : ''}${!verificationResults.schedulePageData ? '• schedulePageData\n' : ''}`;
    }

    wx.showModal({
      title: '🕵️ 流程追踪完成',
      content: conclusion,
      confirmText: '查看详细日志',
      cancelText: allPassed ? '去日程页面' : '确定',
      success: (res) => {
        if (res.confirm) {
          // 显示详细日志
          wx.showModal({
            title: '📋 详细追踪日志',
            content: logText.substring(0, 1500) + (logText.length > 1500 ? '\n...(查看控制台获取完整日志)' : ''),
            showCancel: false
          });
        } else if (allPassed) {
          // 跳转到日程页面
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 🔄 重新创建测试数据（使用正确格式）
  async recreateTestDataWithCorrectFormat() {
    try {
      wx.showLoading({
        title: '重新创建测试数据...',
        mask: true
      });

      console.log('🔄 === 开始重新创建测试数据 ===');
      
      // 🧹 先清空现有数据
      const storageKeys = [
        'local_schedules', 'local_todos', 'local_expenses',
        'allSchedules', 'allTodos', 'allFinances'
      ];
      storageKeys.forEach(key => {
        wx.removeStorageSync(key);
      });
      console.log('🧹 已清空现有数据');

      // 🧪 创建标准测试数据集
      const testDataList = [
        {
          text: '明天下午2点开会',
          expectedType: 'schedule',
          targetDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // 明天
        },
        {
          text: '后天去上海出差',
          expectedType: 'schedule', 
          targetDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // 后天
        },
        {
          text: '五天后去上海',
          expectedType: 'schedule',
          targetDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 五天后
        },
        {
          text: '买咖啡花了25元',
          expectedType: 'finance',
          targetDate: new Date() // 今天
        },
        {
          text: '完成工作报告',
          expectedType: 'todo',
          targetDate: new Date() // 今天
        }
      ];

      const results = [];
      
      for (let i = 0; i < testDataList.length; i++) {
        const testItem = testDataList[i];
        
        try {
          console.log(`📝 创建测试数据 ${i + 1}: ${testItem.text}`);
          
          if (testItem.expectedType === 'schedule') {
            const scheduleData = {
              id: this.generateId(),
              title: testItem.text,
              description: testItem.text,
              startTime: testItem.targetDate.toISOString(),
              endTime: new Date(testItem.targetDate.getTime() + 60 * 60 * 1000).toISOString(),
              location: testItem.text.includes('上海') ? '上海' : '',
              category: '会议',
              source: '测试数据',
              createdAt: new Date().toISOString(),
              type: 'schedule'
            };
            
            await this.saveScheduleToAllLocations(scheduleData);
            results.push({ success: true, type: 'schedule', text: testItem.text });
          } else if (testItem.expectedType === 'finance') {
            const financeData = {
              id: this.generateId(),
              amount: 25,
              description: testItem.text,
              category: '其他',
              type: 'expense',
              date: testItem.targetDate.toISOString(),
              source: '测试数据',
              createdAt: new Date().toISOString()
            };
            
            await this.saveFinanceToAllLocations(financeData);
            results.push({ success: true, type: 'finance', text: testItem.text });
          } else if (testItem.expectedType === 'todo') {
            const todoData = {
              id: this.generateId(),
              title: testItem.text,
              description: testItem.text,
              priority: 'medium',
              completed: false,
              dueDate: testItem.targetDate.toISOString(),
              category: '工作',
              source: '测试数据',
              createdAt: new Date().toISOString(),
              type: 'todo'
            };
            
            await this.saveTodoToAllLocations(todoData);
            results.push({ success: true, type: 'todo', text: testItem.text });
          }
          
          // 短暂延迟
          await this.delay(300);
          
        } catch (error) {
          console.error(`❌ 创建失败 ${i + 1}:`, error);
          results.push({ success: false, type: testItem.expectedType, text: testItem.text, error: error.message });
        }
      }

      wx.hideLoading();

      const successCount = results.filter(r => r.success).length;
      
      wx.showModal({
        title: '🔄 测试数据重新创建完成',
        content: `成功创建 ${successCount}/${results.length} 条数据\n\n✅ 数据已使用正确格式保存\n📅 日程页面应该可以正常显示\n\n现在请检查日程页面！`,
        confirmText: '去日程页面',
        cancelText: '验证格式',
        success: (res) => {
          if (res.confirm) {
            // 跳转到日程页面
            wx.switchTab({
              url: '/pages/index/index'
            });
          } else {
            // 验证数据格式
            this.checkDataSaveFormat();
          }
        }
      });

      // 通知其他页面刷新
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.shouldRefreshSchedules = true;
        app.globalData.shouldRefreshTodos = true;
        app.globalData.shouldRefreshFinances = true;
      }

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 重新创建测试数据失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  // 📊 检查数据保存格式
  checkDataSaveFormat() {
    try {
      console.log('📊 === 检查数据保存格式 ===');
      
      const localSchedules = wx.getStorageSync('local_schedules') || [];
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      
      let report = '📊 数据格式检查报告:\n\n';
      
      // 检查local_schedules格式
      report += `📅 local_schedules (${localSchedules.length} 条):\n`;
      if (localSchedules.length > 0) {
        const sample = localSchedules[0];
        report += `• ID: ${sample.id ? '✅' : '❌'}\n`;
        report += `• 标题: ${sample.title ? '✅' : '❌'}\n`;
        report += `• start_time: ${sample.start_time ? '✅' : '❌'}\n`;
        report += `• end_time: ${sample.end_time ? '✅' : '❌'}\n`;
        report += `• 示例时间: ${sample.start_time || '无'}\n`;
      } else {
        report += `• 无数据\n`;
      }
      report += '\n';
      
      // 检查allSchedules格式
      const scheduleDates = Object.keys(allSchedules);
      report += `📅 allSchedules (${scheduleDates.length} 个日期):\n`;
      if (scheduleDates.length > 0) {
        report += `• 日期键: ${scheduleDates.join(', ')}\n`;
        const firstDate = scheduleDates[0];
        const firstDateData = allSchedules[firstDate];
        if (firstDateData && firstDateData.length > 0) {
          const sample = firstDateData[0];
          report += `• 显示格式: ${sample.dataType ? '✅' : '❌'}\n`;
          report += `• 时间显示: ${sample.time ? '✅' : '❌'}\n`;
          report += `• 示例: ${sample.title} - ${sample.time || '无时间'}\n`;
        }
      } else {
        report += `• 无数据\n`;
      }
      
      // 验证日期键格式
      report += '\n🗓️ 日期键格式验证:\n';
      const today = new Date();
      const testDateKey = this.formatDateKey(today.toISOString());
      report += `• 今天的键: ${testDateKey}\n`;
      report += `• 格式正确: ${/^\d{4}-\d{2}-\d{2}$/.test(testDateKey) ? '✅' : '❌'}\n`;
      
      // 检查是否有"五天后去上海"的数据
      const fiveDaysLater = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000);
      const fiveDaysKey = this.formatDateKey(fiveDaysLater.toISOString());
      const shanghaiData = allSchedules[fiveDaysKey];
      report += `\n🏢 "五天后去上海"检查:\n`;
      report += `• 目标日期: ${fiveDaysLater.toLocaleDateString()}\n`;
      report += `• 日期键: ${fiveDaysKey}\n`;
      report += `• 是否有数据: ${shanghaiData && shanghaiData.length > 0 ? '✅' : '❌'}\n`;
      if (shanghaiData && shanghaiData.length > 0) {
        const shanghaiItem = shanghaiData.find(item => 
          item.title?.includes('上海') || 
          item.description?.includes('上海') ||
          item.location?.includes('上海')
        );
        report += `• 包含上海: ${shanghaiItem ? '✅' : '❌'}\n`;
      }

      console.log('📊 数据格式检查详细结果:', {
        localSchedules: localSchedules,
        allSchedules: allSchedules,
        sampleData: localSchedules[0],
        dateKeys: scheduleDates
      });

      wx.showModal({
        title: '📊 数据格式检查',
        content: report,
        confirmText: '去日程页面',
        cancelText: '确定',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      });

    } catch (error) {
      console.error('❌ 检查数据格式失败:', error);
      wx.showToast({
        title: '检查失败',
        icon: 'error'
      });
    }
  },

  // 🗓️ 验证日程页面显示
  verifySchedulePageDisplay() {
    try {
      console.log('🗓️ === 验证日程页面显示 ===');
      
      const allSchedules = wx.getStorageSync('allSchedules') || {};
      const scheduleDates = Object.keys(allSchedules);
      
      let verification = '🗓️ 日程页面显示验证:\n\n';
      
      if (scheduleDates.length === 0) {
        verification += '❌ 没有找到任何日程数据\n';
        verification += '💡 建议：先创建一些测试数据\n';
      } else {
        verification += `✅ 找到 ${scheduleDates.length} 个有数据的日期:\n\n`;
        
        // 显示每个日期的数据
        scheduleDates.forEach(dateKey => {
          const items = allSchedules[dateKey];
          const date = new Date(dateKey);
          verification += `📅 ${date.toLocaleDateString()} (${dateKey}):\n`;
          items.forEach((item, index) => {
            verification += `   ${index + 1}. ${item.title} - ${item.time}\n`;
          });
          verification += '\n';
        });
        
        verification += '📝 验证步骤:\n';
        verification += '1. 点击"去日程页面"\n';
        verification += '2. 查看月历中的日期是否有标记\n';
        verification += '3. 点击有数据的日期\n';
        verification += '4. 检查下方是否显示日程列表\n';
      }

      wx.showModal({
        title: '🗓️ 显示验证',
        content: verification,
        confirmText: scheduleDates.length > 0 ? '去日程页面' : '创建测试数据',
        cancelText: '确定',
        success: (res) => {
          if (res.confirm) {
            if (scheduleDates.length > 0) {
              wx.switchTab({
                url: '/pages/index/index'
              });
            } else {
              this.recreateTestDataWithCorrectFormat();
            }
          }
        }
      });

    } catch (error) {
      console.error('❌ 验证日程页面显示失败:', error);
      wx.showToast({
        title: '验证失败',
        icon: 'error'
      });
    }
  },

  // 📊 显示详细日程数据
  showDetailedScheduleData(cityRecords, allDateRecords) {
    const cityName = cityRecords.length > 0 && cityRecords[0].location ? cityRecords[0].location : '目标城市';
    let detail = `📊 ${cityName}相关日程详情:\n\n`;
    
    cityRecords.forEach((record, index) => {
      detail += `${index + 1}. ${record.title}\n`;
      detail += `   时间: ${new Date(record.start_time || record.startTime).toLocaleString()}\n`;
      detail += `   地点: ${record.location || '未指定'}\n`;
      detail += `   来源: ${record.source || '未知'}\n\n`;
    });

    if (allDateRecords && allDateRecords.length > cityRecords.length) {
      detail += `\n📅 同日期其他记录: ${allDateRecords.length - cityRecords.length} 条`;
    }

    wx.showModal({
      title: '📊 日程详情',
      content: detail,
      confirmText: '去日程页面',
      cancelText: '确定',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 🔧 重新创建特定日程数据
  async recreateSpecificSchedule(inputText, calculatedDate, dateKey, targetCity) {
    try {
      wx.showLoading({
        title: `重新创建"${inputText}"...`,
        mask: true
      });

      console.log('🔧 重新创建特定日程数据...');
      
      const scheduleData = {
        id: this.generateId(),
        title: inputText,
        description: inputText,
        startTime: calculatedDate.toISOString(),
        endTime: new Date(calculatedDate.getTime() + 60 * 60 * 1000).toISOString(),
        location: targetCity,
        category: '出差',
        source: '重新创建',
        createdAt: new Date().toISOString(),
        type: 'schedule'
      };

      // 保存到所有位置
      await this.saveScheduleToAllLocations(scheduleData);
      
      wx.hideLoading();
      
      wx.showModal({
        title: '✅ 重新创建成功',
        content: `日程已重新创建并保存:\n• 日期: ${calculatedDate.toLocaleDateString()}\n• 标题: ${inputText}\n• 地点: ${targetCity}\n\n请到日程页面的 ${calculatedDate.toLocaleDateString()} 查看！`,
        confirmText: '去日程页面',
        cancelText: '确定',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
          // 刷新数据
          this.refreshAllData();
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 重新创建特定日程失败:', error);
      wx.showToast({
        title: '重新创建失败',
        icon: 'error'
      });
    }
  }
});