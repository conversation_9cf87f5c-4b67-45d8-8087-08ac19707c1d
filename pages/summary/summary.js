Page({
  data: {
    financial: {
      income: '2,500',
      expense: '1,800',
      savings: '700',
      incomeChange: 10,
      expenseChange: -5,
      savingsChange: 15
    },
    scheduleHighlights: [
      {
        id: 1,
        title: '项目会议',
        time: '周一, 10:00',
        icon: 'calendar'
      },
      {
        id: 2,
        title: '客户演示',
        time: '周三, 14:00',
        icon: 'user-group'
      },
      {
        id: 3,
        title: '团队聚餐',
        time: '周五, 12:30',
        icon: 'heart'
      }
    ],
    weekStats: {
      completedTasks: 12,
      meetings: 5,
      expenses: 8,
      efficiency: 85
    }
  },

  onLoad() {
    this.loadSummaryData();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载总结数据
  loadSummaryData() {
    // 这里可以调用API获取真实的总结数据
    console.log('加载周度总结数据');
  },

  // 导出报告
  exportReport() {
    wx.showToast({
      title: '正在生成报告...',
      icon: 'loading'
    });
    
    // 模拟导出过程
    setTimeout(() => {
      wx.showToast({
        title: '报告已保存到相册',
        icon: 'success'
      });
    }, 2000);
  },

  // 分享报告
  shareReport() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '我的本周财务总结',
      path: '/pages/summary/summary',
      imageUrl: '/assets/images/share-summary.png'
    };
  }
})