/* pages/summary/summary.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 48rpx;
}

/* 财务概览 */
.financial-overview {
  margin: 24rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.overview-cards {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.overview-card {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
}

.overview-card.income {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.overview-card.expense {
  background: linear-gradient(135deg, #fff2e6 0%, #ffd591 100%);
}

.savings-card {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
}

.card-title {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.card-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-change {
  font-size: 24rpx;
  font-weight: 500;
}

.card-change.positive {
  color: #52c41a;
}

.card-change.negative {
  color: #ff4d4f;
}

/* 日程亮点 */
.schedule-highlights {
  margin: 24rpx 32rpx;
}

.highlight-list {
  background: white;
  border-radius: 16rpx;
  padding: 16rpx 0;
}

.highlight-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 24rpx;
}

.highlight-icon {
  width: 64rpx;
  height: 64rpx;
  background: #e6f7ff;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-content {
  flex: 1;
}

.highlight-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.highlight-time {
  font-size: 26rpx;
  color: #666;
}

/* 本周统计 */
.week-stats {
  margin: 24rpx 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.stat-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1890ff;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 趋势分析 */
.trend-analysis {
  margin: 24rpx 32rpx;
}

.trend-chart {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  font-size: 28rpx;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx 48rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 16rpx;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

.export-btn {
  flex: 1;
}

.share-btn {
  flex: 1;
}