<!--summary.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <t-icon name="chevron-left" size="24" color="#333" bind:tap="goBack" />
    <text class="title">周度总结</text>
    <view class="placeholder"></view>
  </view>

  <!-- 财务概览 -->
  <view class="financial-overview">
    <text class="section-title">财务概览</text>
    
    <view class="overview-cards">
      <view class="overview-card income">
        <text class="card-title">收入</text>
        <text class="card-amount">¥{{financial.income}}</text>
        <text class="card-change positive">+{{financial.incomeChange}}%</text>
      </view>
      
      <view class="overview-card expense">
        <text class="card-title">支出</text>
        <text class="card-amount">¥{{financial.expense}}</text>
        <text class="card-change negative">{{financial.expenseChange}}%</text>
      </view>
    </view>

    <view class="savings-card">
      <text class="card-title">储蓄</text>
      <text class="card-amount">¥{{financial.savings}}</text>
      <text class="card-change positive">+{{financial.savingsChange}}%</text>
    </view>
  </view>

  <!-- 日程亮点 -->
  <view class="schedule-highlights">
    <text class="section-title">日程亮点</text>
    
    <view class="highlight-list">
      <view 
        class="highlight-item" 
        wx:for="{{scheduleHighlights}}" 
        wx:key="id"
      >
        <view class="highlight-icon">
          <t-icon name="{{item.icon}}" size="20" color="#1890ff" />
        </view>
        <view class="highlight-content">
          <text class="highlight-title">{{item.title}}</text>
          <text class="highlight-time">{{item.time}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本周统计 -->
  <view class="week-stats">
    <text class="section-title">本周数据</text>
    
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{weekStats.completedTasks}}</text>
        <text class="stat-label">已完成任务</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-number">{{weekStats.meetings}}</text>
        <text class="stat-label">会议数量</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-number">{{weekStats.expenses}}</text>
        <text class="stat-label">支出笔数</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-number">{{weekStats.efficiency}}%</text>
        <text class="stat-label">完成效率</text>
      </view>
    </view>
  </view>

  <!-- 趋势分析 -->
  <view class="trend-analysis">
    <text class="section-title">趋势分析</text>
    
    <view class="trend-chart">
      <text class="chart-placeholder">收支趋势图表</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <t-button 
      theme="default" 
      size="large" 
      bind:tap="exportReport"
      class="export-btn"
    >
      导出报告
    </t-button>
    
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="shareReport"
      class="share-btn"
    >
      分享总结
    </t-button>
  </view>
</view>