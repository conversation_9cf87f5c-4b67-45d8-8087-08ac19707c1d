Page({
  data: {
    dateFilter: '',
    categoryFilter: '',
    accountFilter: '',
    dateOptions: [
      { label: '全部', value: '' },
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    categoryOptions: [
      { label: '全部', value: '' },
      { label: '餐饮', value: 'dining' },
      { label: '交通', value: 'transport' },
      { label: '购物', value: 'shopping' },
      { label: '娱乐', value: 'entertainment' },
      { label: '生活费用', value: 'utilities' }
    ],
    accountOptions: [
      { label: '全部', value: '' },
      { label: '支付宝', value: 'alipay' },
      { label: '微信', value: 'wechat' },
      { label: '银行卡', value: 'bank' }
    ],
    transactions: [
      {
        id: 1,
        merchant: '生鲜超市',
        category: '生活用品',
        amount: -45.20,
        icon: 'shop',
        date: '2024-05-14',
        account: 'alipay',
        cssClass: 'groceries'
      },
      {
        id: 2,
        merchant: '优步',
        category: '交通',
        amount: -12.50,
        icon: 'car',
        date: '2024-05-14',
        account: 'wechat',
        cssClass: 'transportation'
      },
      {
        id: 3,
        merchant: '意大利餐厅',
        category: '餐饮',
        amount: -68.75,
        icon: 'food',
        date: '2024-05-14',
        account: 'bank',
        cssClass: 'dining'
      },
      {
        id: 4,
        merchant: '电影院',
        category: '娱乐',
        amount: -25.00,
        icon: 'play-circle',
        date: '2024-05-14',
        account: 'alipay',
        cssClass: 'entertainment'
      },
      {
        id: 5,
        merchant: '在线商店',
        category: '购物',
        amount: -110.00,
        icon: 'shopping-bag',
        date: '2024-05-13',
        account: 'wechat',
        cssClass: 'shopping'
      },
      {
        id: 6,
        merchant: '电力公司',
        category: '生活费用',
        amount: -85.50,
        icon: 'lightning',
        date: '2024-05-13',
        account: 'bank',
        cssClass: 'utilities'
      },
      {
        id: 7,
        merchant: '加油站',
        category: '交通',
        amount: -35.75,
        icon: 'car',
        date: '2024-05-13',
        account: 'alipay',
        cssClass: 'transportation'
      },
      {
        id: 8,
        merchant: '咖啡店',
        category: '餐饮',
        amount: -8.50,
        icon: 'food',
        date: '2024-05-12',
        account: 'wechat',
        cssClass: 'dining'
      },
      {
        id: 9,
        merchant: '演唱会门票',
        category: '娱乐',
        amount: -75.00,
        icon: 'play-circle',
        date: '2024-05-12',
        account: 'bank',
        cssClass: 'entertainment'
      },
      {
        id: 10,
        merchant: '书店',
        category: '购物',
        amount: -22.99,
        icon: 'shopping-bag',
        date: '2024-05-12',
        account: 'alipay',
        cssClass: 'shopping'
      }
    ]
  },

  onLoad() {
    console.log('事务记录页面加载');
    this.checkUserAuth();
  },

  onShow() {
    // 每次显示都重新检查用户状态和加载数据
    this.checkUserAuth();
  },

  // 检查用户身份
  checkUserAuth() {
    const app = getApp();
    
    if (!app.globalData.uid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
      return;
    }
    
    // 加载用户事务数据
    this.loadTransactionData();
  },

  // 加载事务数据
  loadTransactionData() {
    const app = getApp();
    const isGuest = app.globalData.isGuest;
    
    console.log('加载事务数据，用户模式:', isGuest ? '访客' : '正式用户');
    
    if (isGuest) {
      console.log('访客模式，显示示例事务数据');
      this.loadDemoTransactionData();
    } else {
      console.log('用户模式，加载真实事务数据');
      this.loadUserTransactionData();
    }
  },

  // 加载示例事务数据（访客模式）
  loadDemoTransactionData() {
    console.log('显示示例事务数据');
    // 当前的data中已经包含示例数据，无需修改
  },

  // 加载用户真实事务数据
  loadUserTransactionData() {
    try {
      const app = getApp();
      const userQuery = app.getUserQuery();
      console.log('查询用户事务数据:', userQuery);
      
      wx.showLoading({ title: '加载中...' });
      
      // TODO: 实现真实的云端数据加载
      // 这里会在后续Sprint中实现具体的云函数调用
      setTimeout(() => {
        wx.hideLoading();
        console.log('用户事务数据加载完成');
        
        // 模拟空的用户数据（实际应从云端获取）
        this.setData({
          transactions: []
        });
      }, 1000);
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载用户事务数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 日期筛选改变
  onDateChange(e) {
    this.setData({
      dateFilter: e.detail.value
    });
    this.filterTransactions();
  },

  // 分类筛选改变
  onCategoryChange(e) {
    this.setData({
      categoryFilter: e.detail.value
    });
    this.filterTransactions();
  },

  // 账户筛选改变
  onAccountChange(e) {
    this.setData({
      accountFilter: e.detail.value
    });
    this.filterTransactions();
  },

  // 筛选交易记录
  filterTransactions() {
    // 这里可以根据筛选条件过滤数据
    console.log('筛选条件:', {
      date: this.data.dateFilter,
      category: this.data.categoryFilter,
      account: this.data.accountFilter
    });
  },

  // 点击交易记录
  onTransactionClick(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击交易:', item);
    // 可以跳转到交易详情页
  },

  // 添加交易
  onAddTransaction() {
    console.log('添加交易记录');
    // 跳转到交易添加页面
  }
});