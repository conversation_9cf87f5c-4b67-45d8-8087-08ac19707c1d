/* pages/transactions/transactions.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 48rpx;
}

/* 筛选区域 */
.filter-section {
  background: white;
  margin-bottom: 16rpx;
}

/* 交易列表 */
.transaction-list {
  background: white;
  margin: 0 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.transaction-item {
  border-bottom: 1rpx solid #f5f5f5;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  margin-right: 24rpx;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper.groceries {
  background-color: #52c41a;
}

.icon-wrapper.transportation {
  background-color: #1890ff;
}

.icon-wrapper.dining {
  background-color: #fa8c16;
}

.icon-wrapper.entertainment {
  background-color: #722ed1;
}

.icon-wrapper.shopping {
  background-color: #eb2f96;
}

.icon-wrapper.utilities {
  background-color: #faad14;
}

.transaction-content {
  flex: 1;
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.merchant {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.amount {
  font-size: 32rpx;
  font-weight: 600;
}

.amount.expense {
  color: #ff4d4f;
}

.amount.income {
  color: #52c41a;
}

.transaction-meta {
  display: flex;
  align-items: center;
}

.category {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin: 32rpx 0 48rpx;
}

/* 浮动按钮 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  z-index: 100;
}