<!--transactions.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <t-icon name="chevron-left" size="24" color="#333" bind:tap="goBack" />
    <text class="title">交易记录</text>
    <view class="placeholder"></view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-section">
    <t-dropdown-menu>
      <t-dropdown-item 
        value="{{dateFilter}}" 
        options="{{dateOptions}}" 
        bind:change="onDateChange"
      >
        日期
      </t-dropdown-item>
      <t-dropdown-item 
        value="{{categoryFilter}}" 
        options="{{categoryOptions}}" 
        bind:change="onCategoryChange"
      >
        分类
      </t-dropdown-item>
      <t-dropdown-item 
        value="{{accountFilter}}" 
        options="{{accountOptions}}" 
        bind:change="onAccountChange"
      >
        账户
      </t-dropdown-item>
    </t-dropdown-menu>
  </view>

  <!-- 交易列表 -->
  <view class="transaction-list">
    <t-cell-group>
      <t-cell 
        wx:for="{{transactions}}" 
        wx:key="id"
        hover
        bind:click="onTransactionClick"
        data-item="{{item}}"
        class="transaction-item"
      >
        <view slot="left-icon" class="transaction-icon">
          <view class="icon-wrapper {{item.cssClass}}">
            <t-icon name="{{item.icon}}" size="24" color="#fff" />
          </view>
        </view>
        
        <view slot="title" class="transaction-content">
          <view class="transaction-main">
            <text class="merchant">{{item.merchant}}</text>
            <text class="amount {{item.amount < 0 ? 'expense' : 'income'}}">{{item.amount}}</text>
          </view>
          <view class="transaction-meta">
            <text class="category">{{item.category}}</text>
          </view>
        </view>
      </t-cell>
    </t-cell-group>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{transactions.length === 0}}">
    <t-icon name="folder" size="64" color="#ccc" />
    <text class="empty-text">暂无交易记录</text>
    <t-button theme="primary" size="medium" bind:tap="onAddTransaction">
      添加第一笔记录
    </t-button>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab-container">
    <t-fab 
      icon="add" 
      bind:click="onAddTransaction"
      aria-label="添加交易"
    />
  </view>
</view>