/* pages/cloud-status/cloud-status.wxss */

.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 20rpx;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: -0.5rpx;
}

/* 状态卡片 */
.status-card, .data-card, .tips-card, .deploy-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.status-header, .data-header, .tips-header, .deploy-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.status-title, .data-title, .tips-title, .deploy-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.status-badge.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.status-badge.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1rpx solid #ffccc7;
}

.status-info {
  margin-bottom: 24rpx;
}

.last-check {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff4d4f;
  background: #fff2f0;
  padding: 12rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
}

.status-actions, .data-actions {
  display: flex;
  gap: 16rpx;
}

.check-btn, .sync-btn {
  flex: 1;
}

.guide-btn, .clear-btn {
  flex: 1;
}

/* 数据统计 */
.data-stats {
  text-align: center;
  margin-bottom: 24rpx;
}

.stat-item {
  display: inline-block;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 提示内容 */
.tips-content {
  line-height: 1.6;
}

.tip-item {
  display: block;
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 12rpx;
}

/* 部署步骤 */
.deploy-steps {
  margin-top: 20rpx;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.step-number {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
}

/* 按钮样式 */
.check-btn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: white !important;
  border: none !important;
}

.sync-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  color: white !important;
  border: none !important;
}

.guide-btn {
  background: #f8fafc !important;
  color: #6b7280 !important;
  border: 2rpx solid #e5e7eb !important;
}

.clear-btn {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  color: white !important;
  border: none !important;
}

/* 禁用状态 */
.sync-btn[disabled] {
  background: #f5f5f5 !important;
  color: #bfbfbf !important;
}

/* 响应式 */
@media (max-width: 375px) {
  .status-actions, .data-actions {
    flex-direction: column;
  }
  
  .status-actions .t-button, 
  .data-actions .t-button {
    flex: none;
  }
}