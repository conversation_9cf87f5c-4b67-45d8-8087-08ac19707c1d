<!--pages/cloud-status/cloud-status.wxml-->
<view class="container">
  <!-- 标题 -->
  <view class="header">
    <text class="title">云同步状态</text>
  </view>

  <!-- 云函数状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <t-icon name="cloud" size="24" color="{{cloudStatus.isEnabled ? '#52c41a' : '#ff4d4f'}}" />
      <text class="status-title">云函数状态</text>
      <view class="status-badge {{cloudStatus.isEnabled ? 'success' : 'error'}}">
        {{cloudStatus.isEnabled ? '正常' : '异常'}}
      </view>
    </view>
    
    <view class="status-info">
      <text class="last-check">最后检查: {{cloudStatus.lastCheck || '未检查'}}</text>
      <text class="error-message" wx:if="{{cloudStatus.errorMessage}}">
        错误: {{cloudStatus.errorMessage}}
      </text>
    </view>

    <view class="status-actions">
      <t-button size="small" bind:tap="checkCloudStatus" class="check-btn">
        重新检查
      </t-button>
      <t-button size="small" theme="light" bind:tap="showDeployGuide" class="guide-btn">
        部署指南
      </t-button>
    </view>
  </view>

  <!-- 本地数据统计 -->
  <view class="data-card">
    <view class="data-header">
      <t-icon name="folder" size="24" color="#1890ff" />
      <text class="data-title">本地数据</text>
    </view>
    
    <view class="data-stats">
      <view class="stat-item">
        <text class="stat-number">{{cloudStatus.localDataCount}}</text>
        <text class="stat-label">条记录</text>
      </view>
    </view>
    
    <view class="data-actions">
      <t-button 
        size="small" 
        theme="primary" 
        bind:tap="forceSync" 
        disabled="{{!cloudStatus.isEnabled}}"
        class="sync-btn"
      >
        强制同步
      </t-button>
      <t-button size="small" theme="danger" bind:tap="clearLocalData" class="clear-btn">
        清理数据
      </t-button>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-card">
    <view class="tips-header">
      <t-icon name="info-circle" size="24" color="#fa8c16" />
      <text class="tips-title">温馨提示</text>
    </view>
    
    <view class="tips-content">
      <text class="tip-item">• 云函数异常时，数据会自动保存到本地</text>
      <text class="tip-item">• 部署云函数后，本地数据将自动同步到云端</text>
      <text class="tip-item">• 建议定期检查云函数状态，确保数据同步正常</text>
    </view>
  </view>

  <!-- 部署步骤 -->
  <view class="deploy-card" wx:if="{{!cloudStatus.isEnabled}}">
    <view class="deploy-header">
      <t-icon name="tools" size="24" color="#722ed1" />
      <text class="deploy-title">快速部署云函数</text>
    </view>
    
    <view class="deploy-steps">
      <view class="step-item">
        <view class="step-number">1</view>
        <text class="step-text">在微信开发者工具中点击"云开发"</text>
      </view>
      <view class="step-item">
        <view class="step-number">2</view>
        <text class="step-text">右键 writeRecord 文件夹</text>
      </view>
      <view class="step-item">
        <view class="step-number">3</view>
        <text class="step-text">选择"上传并部署（云端安装依赖）"</text>
      </view>
      <view class="step-item">
        <view class="step-number">4</view>
        <text class="step-text">重复操作 userLogin 和 deleteAccount</text>
      </view>
    </view>
  </view>
</view>