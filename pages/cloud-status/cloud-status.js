// pages/cloud-status/cloud-status.js - 云函数状态页面

Page({
  data: {
    cloudStatus: {
      isEnabled: false,
      lastCheck: null,
      errorMessage: null,
      localDataCount: 0
    }
  },

  onLoad() {
    this.checkCloudStatus();
    this.getLocalDataCount();
  },

  // 检查云函数状态
  async checkCloudStatus() {
    wx.showLoading({ title: '检查云函数状态...' });

    try {
      // 尝试调用云函数测试
      const result = await wx.cloud.callFunction({
        name: 'writeRecord',
        data: {
          action: 'test',
          collection: 'test',
          recordId: 'test',
          data: { test: true }
        }
      });

      if (result.result && result.result.success) {
        this.setData({
          'cloudStatus.isEnabled': true,
          'cloudStatus.lastCheck': new Date().toLocaleString(),
          'cloudStatus.errorMessage': null
        });
        
        wx.showToast({
          title: '云函数工作正常',
          icon: 'success'
        });
      } else {
        throw new Error('云函数返回异常');
      }

    } catch (error) {
      console.error('云函数状态检查失败:', error);
      
      let errorMessage = '未知错误';
      if (error.errMsg && error.errMsg.includes('Cannot find module \'wx-server-sdk\'')) {
        errorMessage = 'wx-server-sdk 模块缺失，需要重新部署云函数';
      } else if (error.errMsg && error.errMsg.includes('INVALID_ENV')) {
        errorMessage = '云开发环境配置问题';
      }

      this.setData({
        'cloudStatus.isEnabled': false,
        'cloudStatus.lastCheck': new Date().toLocaleString(),
        'cloudStatus.errorMessage': errorMessage
      });

    } finally {
      wx.hideLoading();
    }
  },

  // 获取本地数据统计
  async getLocalDataCount() {
    try {
      const todos = wx.getStorageSync('local_todos_list') || [];
      const expenses = wx.getStorageSync('local_expenses_list') || [];
      const schedules = wx.getStorageSync('local_schedules_list') || [];

      this.setData({
        'cloudStatus.localDataCount': todos.length + expenses.length + schedules.length
      });

    } catch (error) {
      console.error('获取本地数据统计失败:', error);
    }
  },

  // 强制同步
  async forceSync() {
    if (!this.data.cloudStatus.isEnabled) {
      wx.showToast({
        title: '云函数未就绪，无法同步',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '强制同步中...' });

    try {
      const app = getApp();
      const dataSync = app.getDataSync();
      
      await dataSync.forceSync();
      
      wx.showToast({
        title: '同步完成',
        icon: 'success'
      });

    } catch (error) {
      console.error('强制同步失败:', error);
      wx.showToast({
        title: '同步失败: ' + error.message,
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 部署指南
  showDeployGuide() {
    wx.showModal({
      title: '云函数部署指南',
      content: '1. 在微信开发者工具中点击"云开发"\n2. 右键云函数文件夹\n3. 选择"上传并部署（云端安装依赖）"\n4. 等待部署完成',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 清理本地数据
  clearLocalData() {
    wx.showModal({
      title: '确认清理',
      content: '这将清除所有本地缓存数据，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync();
            wx.showToast({
              title: '本地数据已清理',
              icon: 'success'
            });
            this.getLocalDataCount();
          } catch (error) {
            wx.showToast({
              title: '清理失败',
              icon: 'error'
            });
          }
        }
      }
    });
  }
});