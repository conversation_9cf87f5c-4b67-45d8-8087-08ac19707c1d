/* pages/task-edit/task-edit.wxss */

.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(102, 126, 234, 0.3);
}

.title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
  letter-spacing: -0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-placeholder {
  width: 80rpx; /* 与返回按钮同宽 */
}

/* 底部操作按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: linear-gradient(to top, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

/* 统一底部按钮基础样式 */
.bottom-actions .t-button {
  flex: 1;
  border: none !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  color: white !important;
  transition: all 0.2s ease !important;
}

.bottom-actions .t-button:active {
  transform: scale(0.98) !important;
}

.cancel-btn {
  background: #f8fafc !important;
  color: #6b7280 !important;
  border: 2rpx solid #e5e7eb !important;
}

.cancel-btn:active {
  background: #f1f5f9 !important;
}

.save-btn-bottom {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4) !important;
}

.save-btn-bottom:active {
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4) !important;
}

.delete-btn-bottom {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.4) !important;
}

.delete-btn-bottom:active {
  box-shadow: 0 6rpx 20rpx rgba(255, 77, 79, 0.4) !important;
}

/* 表单区域 */
.form-section {
  margin: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

/* 任务描述区域特殊压缩样式 */
.description-section {
  margin: 12rpx 24rpx !important;
  padding: 16rpx !important;
}

.description-section .section-header {
  margin-bottom: 12rpx !important;
}

.description-section .section-title {
  font-size: 26rpx !important;
}

.description-section .description-input {
  min-height: 40rpx !important;
  max-height: 60rpx !important;
  padding: 8rpx 12rpx !important;
  font-size: 22rpx !important;
  line-height: 1.3 !important;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.3rpx;
}

/* 输入框样式 */
.title-input {
  font-size: 30rpx;
  color: #1f2937;
  line-height: 1.5;
}

.description-input {
  font-size: 24rpx;
  color: #374151;
  line-height: 1.4;
  min-height: 60rpx;
  max-height: 80rpx;
}

/* 优先级选项 */
.priority-options {
  display: flex;
  gap: 16rpx;
}

.priority-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f8fafc;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.priority-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.priority-item:active {
  transform: scale(0.98);
}

.priority-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.priority-color.high {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.priority-color.medium {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  box-shadow: 0 2rpx 8rpx rgba(250, 140, 22, 0.3);
}

.priority-color.low {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

.priority-text {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

/* 状态选项 */
.status-options {
  display: flex;
  gap: 16rpx;
}

.status-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f8fafc;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.status-item.selected {
  background: rgba(19, 194, 194, 0.1);
  border-color: #13c2c2;
}

.status-item:active {
  transform: scale(0.98);
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.pending {
  background: #fa8c16;
  box-shadow: 0 0 8rpx rgba(250, 140, 22, 0.4);
}

.status-indicator.completed {
  background: #52c41a;
  box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}

.status-text {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

/* 日期时间选择器 */
.datetime-picker-container {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 12rpx;
  overflow: hidden;
}

.current-time-display {
  background: white;
  border-radius: 8rpx;
  padding: 12rpx;
  margin-bottom: 12rpx;
  text-align: center;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.current-time-text {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
}

.datetime-picker-view {
  width: 100%;
  height: 240rpx;
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 选中指示线 */
.datetime-picker-view::before,
.datetime-picker-view::after {
  content: '';
  position: absolute;
  left: 20rpx;
  right: 20rpx;
  height: 1rpx;
  background: rgba(102, 126, 234, 0.3);
  z-index: 10;
  pointer-events: none;
}

.datetime-picker-view::before {
  top: 96rpx; /* (240 - 48) / 2 */
}

.datetime-picker-view::after {
  top: 144rpx; /* (240 - 48) / 2 + 48 */
}

.picker-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
}

.picker-item {
  height: 48rpx;
  line-height: 48rpx;
  font-size: 26rpx;
  color: #374151;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 100%;
}

/* picker-view 默认样式覆盖 */
picker-view {
  border-radius: 8rpx !important;
}

picker-view-column {
  text-align: center !important;
}

/* 选中指示器样式 */
.picker-indicator {
  background: rgba(102, 126, 234, 0.1);
  border-top: 1rpx solid rgba(102, 126, 234, 0.3);
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.3);
}





/* TDesign 组件样式覆盖 */
.t-input {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.t-textarea {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.t-input__inner {
  border: none !important;
  background: transparent !important;
}

.t-textarea__inner {
  border: none !important;
  background: transparent !important;
}

/* 选择器弹窗样式 */
.t-picker {
  border-radius: 20rpx 20rpx 0 0 !important;
}

.t-picker__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.t-picker__confirm {
  color: white !important;
  font-weight: 600 !important;
}

.t-picker__cancel {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .priority-options {
    flex-direction: column;
  }
  
  .status-options {
    flex-direction: column;
  }
  
  .priority-item,
  .status-item {
    flex: none;
  }
}

/* 顶部导航样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(102, 126, 234, 0.3);
}

.back-btn-area {
  display: flex;
  align-items: center;
  padding: 10rpx 15rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.back-btn-area:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.back-text {
  color: white;
  font-size: 28rpx;
  margin-left: 8rpx;
}

/* 隐藏picker */
.hidden-picker {
  position: absolute;
  left: -9999rpx;
  top: -9999rpx;
  opacity: 0;
}

 