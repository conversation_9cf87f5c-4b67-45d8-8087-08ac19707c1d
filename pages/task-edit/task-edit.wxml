<!--task-edit.wxml-->
<view class="container" bind:touchstart="touchStart" bind:touchend="touchEnd">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="back-btn-area" bind:tap="goBack">
      <text class="back-text">返回</text>
    </view>
    <text class="title">{{isEdit ? '编辑任务' : '新建任务'}}</text>
    <view class="header-placeholder"></view>
  </view>



  <!-- 任务标题 -->
  <view class="form-section">
    <view class="section-header">
      <text class="section-title">任务标题</text>
    </view>
    <t-input
      placeholder="请输入任务标题"
      value="{{taskData.title}}"
      bind:change="onTitleChange"
      bind:input="onTitleInput"
      class="title-input"
    />
  </view>

  <!-- 任务描述 -->
  <view class="form-section description-section">
    <view class="section-header">
      <text class="section-title">任务描述</text>
    </view>
    <t-textarea
      placeholder="添加更多详细信息（可选）"
      value="{{taskData.description}}"
      bind:change="onDescriptionChange"
      bind:input="onDescriptionInput"
      maxlength="500"
      class="description-input"
      indicator
    />
  </view>

  <!-- 优先级 -->
  <view class="form-section">
    <view class="section-header">
      <text class="section-title">优先级</text>
    </view>
    <view class="priority-options">
      <view 
        class="priority-item {{taskData.priority === 'high' ? 'selected' : ''}}"
        bind:tap="selectPriority"
        data-priority="high"
      >
        <view class="priority-color high"></view>
        <text class="priority-text">高优先级</text>
      </view>
      <view 
        class="priority-item {{taskData.priority === 'medium' ? 'selected' : ''}}"
        bind:tap="selectPriority"
        data-priority="medium"
      >
        <view class="priority-color medium"></view>
        <text class="priority-text">中优先级</text>
      </view>
      <view 
        class="priority-item {{taskData.priority === 'low' ? 'selected' : ''}}"
        bind:tap="selectPriority"
        data-priority="low"
      >
        <view class="priority-color low"></view>
        <text class="priority-text">低优先级</text>
      </view>
    </view>
  </view>

  <!-- 截止时间 -->
  <view class="form-section">
    <view class="section-header">
      <text class="section-title">截止时间</text>
    </view>
    <view class="datetime-picker-container">
      <!-- 显示当前选中时间 -->
      <view class="current-time-display">
        <text class="current-time-text">当前选中: {{displayDateTime || '未设置'}}</text>
      </view>
      
      <picker-view
        class="datetime-picker-view"
        value="{{dateTimeValue}}"
        bindchange="onDateTimeChange"
        indicator-style="height: 48rpx; border-top: 1px solid #e2e8f0; border-bottom: 1px solid #e2e8f0;"
      >
        <picker-view-column wx:if="{{dateTimeRange[0]}}">
          <view
            class="picker-item"
            wx:for="{{dateTimeRange[0]}}"
            wx:key="value"
          >
            {{item.label}}
          </view>
        </picker-view-column>
        <picker-view-column wx:if="{{dateTimeRange[1]}}">
          <view
            class="picker-item"
            wx:for="{{dateTimeRange[1]}}"
            wx:key="value"
          >
            {{item.label}}
          </view>
        </picker-view-column>
        <picker-view-column wx:if="{{dateTimeRange[2]}}">
          <view
            class="picker-item"
            wx:for="{{dateTimeRange[2]}}"
            wx:key="value"
          >
            {{item.label}}
          </view>
        </picker-view-column>
        <picker-view-column wx:if="{{dateTimeRange[3]}}">
          <view
            class="picker-item"
            wx:for="{{dateTimeRange[3]}}"
            wx:key="value"
          >
            {{item.label}}
          </view>
        </picker-view-column>
        <picker-view-column wx:if="{{dateTimeRange[4]}}">
          <view
            class="picker-item"
            wx:for="{{dateTimeRange[4]}}"
            wx:key="value"
          >
            {{item.label}}
          </view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>

  <!-- 任务状态 -->
  <view class="form-section" wx:if="{{isEdit}}">
    <view class="section-header">
      <text class="section-title">任务状态</text>
    </view>
    <view class="status-options">
      <view 
        class="status-item {{taskData.status === 'pending' ? 'selected' : ''}}"
        bind:tap="selectStatus"
        data-status="pending"
      >
        <view class="status-indicator pending"></view>
        <text class="status-text">待完成</text>
      </view>
      <view 
        class="status-item {{taskData.status === 'completed' ? 'selected' : ''}}"
        bind:tap="selectStatus"
        data-status="completed"
      >
        <view class="status-indicator completed"></view>
        <text class="status-text">已完成</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <!-- 新建模式：取消 + 保存 -->
    <block wx:if="{{!isEdit}}">
      <t-button 
        size="large" 
        bind:tap="goBack"
        class="cancel-btn"
        icon=""
      >
        取消
      </t-button>
      <t-button 
        theme="primary" 
        size="large" 
        bind:tap="saveTask"
        class="save-btn-bottom"
      >
        创建任务
      </t-button>
    </block>
    
    <!-- 编辑模式：删除 + 保存 -->
    <block wx:if="{{isEdit}}">
      <t-button 
        theme="danger" 
        size="large" 
        bind:tap="deleteTask"
        class="delete-btn-bottom"
      >
        删除任务
      </t-button>
      <t-button 
        theme="primary" 
        size="large" 
        bind:tap="saveTask"
        class="save-btn-bottom"
      >
        保存修改
      </t-button>
    </block>
  </view>
</view>

 