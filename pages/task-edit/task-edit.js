import dataApi from '../../utils/dataApi.js';

Page({
  data: {
    isEdit: false, // 是否为编辑模式
    taskId: null, // 任务ID
    taskData: {
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending',
      dueDate: null
    },
    
    // 日期时间选择器
    showDateTimePicker: false,
    dateTimeRange: [],
    dateTimeValue: [0, 0, 0, 0, 0], // 年、月、日、时、分
    displayDateTime: '', // 显示的日期时间文本
    
    // 表单验证
    hasChanges: false
  },

  onLoad(options) {
    console.log('任务编辑页面 onLoad:', options);
    
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    
    // 初始化日期时间选择器选项
    this.initDateTimeRange();
    
    // 添加手势返回监听
    this.setupGestureBack();
    
    if (options.taskId) {
      // 编辑模式
      this.setData({
        isEdit: true,
        taskId: options.taskId
      });
      this.loadTask(options.taskId);
    } else {
      // 新建模式：设置默认截止时间为1小时后
      this.setData({ isEdit: false });
      this.setDefaultDueDate();
    }
    
    // 确保数据初始化完成后输出调试信息
    setTimeout(() => {
      console.log('🗺️ 初始化完成后的数据状态:');
      console.log('dateTimeRange:', this.data.dateTimeRange?.map(arr => arr?.length || 0));
      console.log('dateTimeValue:', this.data.dateTimeValue);
      console.log('taskData.dueDate:', this.data.taskData?.dueDate);
    }, 100);
  },
  
  // 设置默认截止时间（新建任务时）
  setDefaultDueDate() {
    const now = new Date();
    const defaultDueDate = new Date(now.getTime() + 60 * 60 * 1000); // 1小时后
    
    // 调整到最近的30分钟间隔
    const minutes = defaultDueDate.getMinutes();
    if (minutes < 30) {
      defaultDueDate.setMinutes(30, 0, 0);
    } else {
      defaultDueDate.setMinutes(0, 0, 0);
      defaultDueDate.setHours(defaultDueDate.getHours() + 1);
    }
    
    this.setData({
      'taskData.dueDate': defaultDueDate,
      displayDateTime: this.formatDateTime(defaultDueDate)
    });
    
    // 更新选择器索引
    this.updateDateTimePickerValue(defaultDueDate);
    
    console.log('📅 设置默认截止时间:', defaultDueDate.toLocaleString());
  },
  
  // 更新日期时间选择器的值
  updateDateTimePickerValue(date) {
    if (!date) return;
    
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    
    const dateTimeRange = this.data.dateTimeRange;
    
    // 找到对应的索引
    const yearIndex = dateTimeRange[0].findIndex(item => item.value === year);
    const monthIndex = dateTimeRange[1].findIndex(item => item.value === month);
    const dayIndex = dateTimeRange[2].findIndex(item => item.value === day);
    const hourIndex = dateTimeRange[3].findIndex(item => item.value === hour);
    const minuteIndex = dateTimeRange[4].findIndex(item => item.value === minute);
    
    this.setData({
      dateTimeValue: [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex]
    });
  },

  onShow() {
    console.log('=== 任务编辑页面 onShow ===');
    const pages = getCurrentPages();
    console.log('当前页面栈信息:');
    pages.forEach((page, index) => {
      console.log(`${index}: ${page.route}`);
    });
    
    // 确保显示数据正确
    const { taskData } = this.data;
    if (taskData.dueDate && !this.data.displayDateTime) {
      this.setData({
        displayDateTime: this.formatDateTime(taskData.dueDate)
      });
      console.log('🔄 修复 displayDateTime:', this.formatDateTime(taskData.dueDate));
    }
    
    // 调试: 检查picker-view所需的数据
    console.log('🚿 picker-view 数据检查:');
    console.log('dateTimeRange 是否存在:', !!this.data.dateTimeRange);
    console.log('dateTimeValue 是否存在:', !!this.data.dateTimeValue);
    if (this.data.dateTimeRange) {
      console.log('dateTimeRange 结构:', this.data.dateTimeRange.map(arr => `${arr?.length || 0} 个选项`));
    }
    if (this.data.dateTimeValue) {
      console.log('dateTimeValue:', this.data.dateTimeValue);
    }
  },

  // 设置手势返回
  setupGestureBack() {
    console.log('手势返回功能已启用');
  },

  // 触摸开始
  touchStart(e) {
    this.startX = e.touches[0].clientX;
    this.startY = e.touches[0].clientY;
  },

  // 触摸结束
  touchEnd(e) {
    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;
    
    const deltaX = endX - this.startX;
    const deltaY = Math.abs(endY - this.startY);
    
    // 右滑手势检测：从左边缘开始，向右滑动超过100px，且垂直位移不超过50px
    if (this.startX < 50 && deltaX > 100 && deltaY < 50) {
      console.log('检测到右滑返回手势');
      this.performNavigation();
    }
  },

  // 初始化日期时间选择器选项
  initDateTimeRange() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const currentDay = now.getDate();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    const yearOptions = [];
    const monthOptions = [];
    const dayOptions = [];
    const hourOptions = [];
    const minuteOptions = [];
    
    // 年份选项（当前年到未来2年）
    for (let i = 0; i < 3; i++) {
      yearOptions.push({
        label: `${currentYear + i}年`,
        value: currentYear + i
      });
    }
    
    // 月份选项
    for (let i = 1; i <= 12; i++) {
      monthOptions.push({
        label: `${i}月`,
        value: i
      });
    }
    
    // 日期选项
    for (let i = 1; i <= 31; i++) {
      dayOptions.push({
        label: `${i}日`,
        value: i
      });
    }
    
    // 小时选项
    for (let i = 0; i < 24; i++) {
      hourOptions.push({
        label: `${String(i).padStart(2, '0')}时`,
        value: i
      });
    }
    
    // 分钟选项（30分钟间隔）
    const minuteValues = [0, 30];
    minuteValues.forEach(minute => {
      minuteOptions.push({
        label: `${String(minute).padStart(2, '0')}分`,
        value: minute
      });
    });
    
    // 计算当前时间的默认选择索引
    const currentYearIndex = 0; // 当前年是第一个选项
    const currentMonthIndex = currentMonth - 1; // 月份从0开始索引
    const currentDayIndex = currentDay - 1; // 日期从0开始索引
    const currentHourIndex = currentHour; // 小时直接对应索引
    
    // 分钟选择最接近的30分钟间隔
    let currentMinuteIndex = 0;
    if (currentMinute >= 30) {
      currentMinuteIndex = 1; // 30分
    } else {
      currentMinuteIndex = 0; // 00分
    }
    
    this.setData({
      dateTimeRange: [yearOptions, monthOptions, dayOptions, hourOptions, minuteOptions],
      dateTimeValue: [currentYearIndex, currentMonthIndex, currentDayIndex, currentHourIndex, currentMinuteIndex]
    });
    
    console.log('📅 日期时间选择器初始化:', {
      当前时间: now.toLocaleString(),
      默认选择: [currentYearIndex, currentMonthIndex, currentDayIndex, currentHourIndex, currentMinuteIndex],
      dateTimeRange长度: [yearOptions.length, monthOptions.length, dayOptions.length, hourOptions.length, minuteOptions.length]
    });
    
    // 调试输出前几个选项
    console.log('第一列选项:', yearOptions.slice(0, 3));
    console.log('最后一列选项:', minuteOptions);
  },

  // 加载任务数据
  async loadTask(taskId) {
    try {
      console.log('📄 加载任务，任务ID:', taskId);
      
      if (!taskId) {
        throw new Error('任务ID不能为空');
      }
      
      wx.showLoading({ title: '加载中...' });
      
      // 优先尝试直接获取单个任务
      let task = null;
      try {
        task = await dataApi.getTodo(taskId);
      } catch (directError) {
        console.log('直接获取失败，尝试从列表中查找:', directError.message);
        // 降级方案：从列表中查找
        const todos = await dataApi.getTodos({ limit: 200 });
        task = todos.find(t => t.id === taskId);
      }
      
      wx.hideLoading();
      
      if (task) {
        console.log('✅ 加载到任务数据:', task);
        
        // 验证和转换数据
        const taskData = this.convertTodoToTaskData(task);
        
        this.setData({
          taskData,
          displayDateTime: this.formatDateTime(taskData.dueDate),
          hasChanges: false // 初始加载时没有更改
        });
        
        // 更新日期时间选择器
        if (taskData.dueDate) {
          this.updateDateTimePickerValue(taskData.dueDate);
        }
        
        console.log('✨ 任务数据加载完成');
        
      } else {
        console.error('❌ 未找到任务:', taskId);
        this.showTaskNotFoundError();
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 加载任务失败:', error);
      
      const errorMessage = this.getErrorMessage(error);
      
      wx.showModal({
        title: '加载失败',
        content: errorMessage,
        confirmText: '重试',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            setTimeout(() => this.loadTask(taskId), 500);
          } else {
            wx.navigateBack();
          }
        }
      });
    }
  },
  
  // 获取当前时间（调整到30分钟间隔）
  getCurrentTimeRounded() {
    const now = new Date();
    const minutes = now.getMinutes();
    
    // 调整到最近的30分钟间隔
    if (minutes < 30) {
      now.setMinutes(30, 0, 0);
    } else {
      now.setMinutes(0, 0, 0);
      now.setHours(now.getHours() + 1);
    }
    
    return now;
  },

  // 返回上一页
  goBack() {
    console.log('🔙 goBack方法被调用');
    console.log('是否有未保存的更改:', this.data.hasChanges);
    
    if (this.data.hasChanges) {
      console.log('有未保存的更改，显示确认对话框');
      wx.showModal({
        title: '确认离开？',
        content: '您有未保存的修改，确定要离开吗？',
        success: (res) => {
          console.log('对话框结果:', res);
          if (res.confirm) {
            console.log('用户确认离开，执行返回');
            this.performNavigation();
          }
        }
      });
    } else {
      console.log('无未保存的更改，直接返回');
      this.performNavigation();
    }
  },

  // 执行导航返回
  performNavigation() {
    console.log('执行导航返回...');
    
    // 获取当前页面栈信息
    const pages = getCurrentPages();
    console.log('当前页面栈长度:', pages.length);
    
    if (pages.length > 1) {
      // 有上一页，正常返回
      console.log('使用wx.navigateBack返回');
      wx.navigateBack({
        success: () => {
          console.log('✅ wx.navigateBack 成功');
        },
        fail: (error) => {
          console.error('❌ wx.navigateBack 失败:', error);
          // 降级方案：跳转到任务页面
          this.fallbackNavigation();
        }
      });
    } else {
      // 没有上一页，使用降级方案
      console.log('页面栈只有一页，使用降级方案');
      this.fallbackNavigation();
    }
  },

  // 降级导航方案
  fallbackNavigation() {
    console.log('执行降级导航方案');
    
    // 尝试跳转到任务页面
    wx.switchTab({
      url: '/pages/tasks/tasks',
      success: () => {
        console.log('✅ 降级导航成功：跳转到任务页面');
      },
      fail: (error) => {
        console.error('❌ 降级导航失败:', error);
        
        // 最后的降级方案：跳转到首页
        wx.switchTab({
          url: '/pages/index/index',
          success: () => {
            console.log('✅ 最终降级成功：跳转到首页');
          },
          fail: (finalError) => {
            console.error('❌ 所有导航方案都失败了:', finalError);
            wx.showToast({
              title: '导航失败，请重启小程序',
              icon: 'error'
            });
          }
        });
      }
    });
  },

  // 标题输入变化 (实时)
  onTitleInput(e) {
    const newTitle = e.detail.value;
    console.log('标题实时输入:', newTitle);
    this.setData({
      'taskData.title': newTitle,
      hasChanges: true
    });
  },
  
  // 标题输入完成
  onTitleChange(e) {
    const newTitle = e.detail.value;
    console.log('标题变化完成:', newTitle);
    this.setData({
      'taskData.title': newTitle,
      hasChanges: true
    });
  },

  // 描述输入变化 (实时)
  onDescriptionInput(e) {
    this.setData({
      'taskData.description': e.detail.value,
      hasChanges: true
    });
  },
  
  // 描述输入完成
  onDescriptionChange(e) {
    this.setData({
      'taskData.description': e.detail.value,
      hasChanges: true
    });
  },

  // 选择优先级
  selectPriority(e) {
    const priority = e.currentTarget.dataset.priority;
    this.setData({
      'taskData.priority': priority,
      hasChanges: true
    });
  },

  // 选择状态
  selectStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      'taskData.status': status,
      hasChanges: true
    });
  },



  // 日期时间选择器变化
  onDateTimeChange(e) {
    console.log('🔄 日期时间选择器变化:', e.detail.value);
    
    try {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;
      const dateTimeRange = this.data.dateTimeRange;
      
      // 强化索引验证
      const validation = this.validatePickerIndexes(dateTimeRange, e.detail.value);
      if (!validation.isValid) {
        console.error('⚠️ 选择器索引验证失败:', validation.error);
        this.resetToCurrentTime();
        return;
      }
      
      const year = dateTimeRange[0][yearIndex].value;
      const month = dateTimeRange[1][monthIndex].value;
      const day = dateTimeRange[2][dayIndex].value;
      const hour = dateTimeRange[3][hourIndex].value;
      const minute = dateTimeRange[4][minuteIndex].value;
      
      console.log('📅 解析的日期时间:', { year, month, day, hour, minute });
      
      // 创建日期对象并验证
      const dueDate = this.createValidDate(year, month, day, hour, minute);
      if (!dueDate) {
        console.error('⚠️ 无效的日期组合');
        this.resetToCurrentTime();
        return;
      }
      
      console.log('✅ 创建的日期对象:', dueDate.toLocaleString());
      
      this.setData({
        'taskData.dueDate': dueDate,
        dateTimeValue: e.detail.value,
        displayDateTime: this.formatDateTime(dueDate),
        hasChanges: true
      });
      
      console.log('✨ 日期时间更新完成');
      
    } catch (error) {
      console.error('❌ 日期选择器处理失败:', error);
      this.resetToCurrentTime();
    }
  },



  // 格式化日期时间显示（供WXML调用）
  formatDateTime(date) {
    if (!date) return '';
    
    const now = new Date();
    const target = new Date(date);
    
    const isToday = target.toDateString() === now.toDateString();
    const isTomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString() === target.toDateString();
    
    const timeStr = `${String(target.getHours()).padStart(2, '0')}:${String(target.getMinutes()).padStart(2, '0')}`;
    
    if (isToday) {
      return `今天 ${timeStr}`;
    } else if (isTomorrow) {
      return `明天 ${timeStr}`;
    } else {
      return `${target.getMonth() + 1}月${target.getDate()}日 ${timeStr}`;
    }
  },

  // 保存任务
  async saveTask() {
    const { taskData, isEdit, taskId } = this.data;
    
    console.log('💾 开始保存任务，当前数据:', taskData);
    
    // 强化验证逻辑
    const validation = this.validateTaskData(taskData);
    if (!validation.isValid) {
      console.log('❌ 验证失败:', validation.errors);
      wx.showToast({
        title: validation.errors[0], // 显示第一个错误
        icon: 'none',
        duration: 2500
      });
      return;
    }
    
    console.log('✅ 验证通过，开始保存...');
    
    try {
      wx.showLoading({ title: isEdit ? '保存中...' : '创建中...' });
      
      // 构建保存数据
      const todoData = this.buildTodoData(taskData, isEdit);
      console.log('📦 构建的数据:', todoData);
      
      let result;
      if (isEdit) {
        // 更新现有任务
        result = await dataApi.updateTodo(taskId, todoData);
      } else {
        // 创建新任务
        todoData.source = 'manual';
        result = await dataApi.createTodo(todoData);
      }
      
      wx.hideLoading();
      
      if (result.success) {
        console.log('✅ 保存成功:', result);
        
        // 标记数据已保存
        this.setData({ hasChanges: false });
        
        wx.showToast({
          title: isEdit ? '保存成功🎉' : '创建成功🎉',
          icon: 'success',
          duration: 2000
        });
        
        // 返回上一页并通知刷新
        setTimeout(() => {
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage && prevPage.refresh) {
            prevPage.refresh(); // 通知上一页刷新
          }
          wx.navigateBack();
        }, 1500);
        
      } else {
        throw new Error(result.error || '保存失败');
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 保存任务失败:', error);
      
      // 根据错误类型显示不同的错误信息
      const errorMessage = this.getErrorMessage(error);
      
      wx.showModal({
        title: '保存失败',
        content: errorMessage,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户选择重试
            setTimeout(() => this.saveTask(), 500);
          }
        }
      });
    }
  },

  // 删除任务
  async deleteTask() {
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个任务吗？',
      confirmColor: '#ff4d4f',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' });
            
            const result = await dataApi.deleteTodo(this.data.taskId);
            
            wx.hideLoading();
            
            if (result.success) {
              console.log('✅ 删除成功:', result);
              
              wx.showToast({
                title: '删除成功🗑️',
                icon: 'success',
                duration: 2000
              });
              
              // 通知上一页刷新
              setTimeout(() => {
                const pages = getCurrentPages();
                const prevPage = pages[pages.length - 2];
                if (prevPage && prevPage.refresh) {
                  prevPage.refresh();
                }
                wx.navigateBack();
              }, 1500);
              
            } else {
              throw new Error(result.error || '删除失败');
            }
            
          } catch (error) {
            wx.hideLoading();
            console.error('❌ 删除任务失败:', error);
            
            const errorMessage = this.getErrorMessage(error);
            
            wx.showModal({
              title: '删除失败',
              content: errorMessage,
              confirmText: '重试',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  setTimeout(() => this.deleteTask(), 500);
                }
              }
            });
          }
        }
      }
    });
  },

  // 验证任务数据
  validateTaskData(taskData) {
    const errors = [];
    
    // 标题验证
    if (!taskData.title || taskData.title.trim() === '') {
      errors.push('请输入任务标题');
    } else if (taskData.title.trim().length > 100) {
      errors.push('任务标题不能超过100个字符');
    }
    
    // 描述验证
    if (taskData.description && taskData.description.length > 500) {
      errors.push('任务描述不能超过500个字符');
    }
    
    // 截止时间验证
    if (taskData.dueDate) {
      const now = new Date();
      const dueDate = new Date(taskData.dueDate);
      
      if (isNaN(dueDate.getTime())) {
        errors.push('截止时间格式不正确');
      } else if (dueDate < new Date(now.getTime() - 60 * 1000)) {
        // 允许1分钟的容错范围
        errors.push('截止时间不能早于当前时间');
      }
    }
    
    // 优先级验证
    const validPriorities = ['low', 'medium', 'high'];
    if (!validPriorities.includes(taskData.priority)) {
      errors.push('请选择有效的优先级');
    }
    
    // 状态验证
    const validStatuses = ['pending', 'completed'];
    if (!validStatuses.includes(taskData.status)) {
      errors.push('请选择有效的任务状态');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // 构建保存数据
  buildTodoData(taskData, isEdit) {
    const now = new Date();
    
    const todoData = {
      title: taskData.title.trim(),
      description: taskData.description ? taskData.description.trim() : '',
      priority: taskData.priority,
      status: taskData.status,
      dueDate: taskData.dueDate ? taskData.dueDate.toISOString() : null,
      updatedAt: now.toISOString(),
      
      // 添加扩展字段
      urgent: this.isUrgent(taskData.dueDate),
      important: taskData.priority === 'high'
    };
    
    if (!isEdit) {
      // 新建任务的额外字段
      todoData.source = 'manual';
      todoData.createdAt = now.toISOString();
      todoData.completed = false;
    }
    
    return todoData;
  },

  // 判断任务是否紧急
  isUrgent(dueDate) {
    if (!dueDate) return false;
    
    const now = new Date();
    const due = new Date(dueDate);
    const diffHours = (due - now) / (1000 * 60 * 60);
    
    // 在24小时内的任务为紧急
    return diffHours <= 24 && diffHours > 0;
  },

  // 验证选择器索引
  validatePickerIndexes(dateTimeRange, indexes) {
    if (!dateTimeRange || dateTimeRange.length !== 5) {
      return { isValid: false, error: 'dateTimeRange 数据不完整' };
    }
    
    if (!Array.isArray(indexes) || indexes.length !== 5) {
      return { isValid: false, error: '索引数组格式不正确' };
    }
    
    for (let i = 0; i < 5; i++) {
      const index = indexes[i];
      const range = dateTimeRange[i];
      
      if (!range || !Array.isArray(range)) {
        return { isValid: false, error: `第${i + 1}个选择器选项为空` };
      }
      
      if (index < 0 || index >= range.length) {
        return { isValid: false, error: `第${i + 1}个索引(${index})超出范围(0-${range.length - 1})` };
      }
    }
    
    return { isValid: true };
  },

  // 创建有效日期
  createValidDate(year, month, day, hour, minute) {
    try {
      // 检查数值范围
      if (year < 2024 || year > 2030) return null;
      if (month < 1 || month > 12) return null;
      if (day < 1 || day > 31) return null;
      if (hour < 0 || hour > 23) return null;
      if (minute < 0 || minute > 59) return null;
      
      const date = new Date(year, month - 1, day, hour, minute);
      
      // 检查日期是否有效（防止如有2月31日这种情况）
      if (date.getFullYear() !== year || 
          date.getMonth() !== month - 1 || 
          date.getDate() !== day ||
          date.getHours() !== hour ||
          date.getMinutes() !== minute) {
        return null;
      }
      
      return date;
    } catch (error) {
      console.error('创建日期失败:', error);
      return null;
    }
  },

  // 重置为当前时间
  resetToCurrentTime() {
    console.log('🔄 重置为当前时间');
    
    const now = this.getCurrentTimeRounded();
    
    this.setData({
      'taskData.dueDate': now,
      displayDateTime: this.formatDateTime(now)
    });
    
    // 更新选择器索引
    this.updateDateTimePickerValue(now);
    
    wx.showToast({
      title: '已重置为当前时间',
      icon: 'none',
      duration: 1500
    });
  },

  // 获取错误信息
  getErrorMessage(error) {
    if (!error) return '未知错误';
    
    const message = error.message || error.toString();
    
    // 网络相关错误
    if (message.includes('network') || message.includes('网络')) {
      return '网络连接失败，请检查网络后重试';
    }
    
    // 超时错误
    if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请稍后重试';
    }
    
    // 登录相关错误
    if (message.includes('login') || message.includes('登录') || message.includes('未登录')) {
      return '登录状态失效，请重新登录';
    }
    
    // 权限相关错误
    if (message.includes('permission') || message.includes('权限')) {
      return '没有操作权限，请联系管理员';
    }
    
    // 数据验证错误
    if (message.includes('validation') || message.includes('验证')) {
      return '数据格式不正确，请检查输入内容';
    }
    
    // 云函数相关错误
    if (message.includes('cloud function') || message.includes('云函数')) {
      return '服务暂不可用，数据已保存在本地，稍后会自动同步';
    }
    
    // 默认错误信息
    if (message.length > 50) {
      return '操作失败，请稍后重试';
    }
    
    return message;
  },

  // 检查是否有未保存的更改
  checkUnsavedChanges() {
    return this.data.hasChanges;
  },

  // 页面卸载前的检查
  onUnload() {
    if (this.checkUnsavedChanges()) {
      console.log('⚠️ 检测到未保存的更改');
      // 注意：onUnload 中无法阻止页面卸载，只能记录日志
    }
  },

  // 转换任务数据格式
  convertTodoToTaskData(todo) {
    // 处理日期字段
    let dueDate = null;
    if (todo.dueDate) {
      dueDate = new Date(todo.dueDate);
      // 检查日期是否有效
      if (isNaN(dueDate.getTime())) {
        console.warn('无效的截止日期:', todo.dueDate);
        dueDate = this.getCurrentTimeRounded();
      }
    } else {
      // 如果没有截止时间，设置为当前时间
      dueDate = this.getCurrentTimeRounded();
    }
    
    return {
      title: todo.title || '',
      description: todo.description || '',
      priority: ['low', 'medium', 'high'].includes(todo.priority) ? todo.priority : 'medium',
      status: ['pending', 'completed'].includes(todo.status) ? todo.status : 'pending',
      dueDate
    };
  },

  // 显示任务未找到错误
  showTaskNotFoundError() {
    wx.showModal({
      title: '任务不存在',
      content: '未找到指定的任务，可能已被删除',
      showCancel: false,
      confirmText: '返回',
      success: () => {
        wx.navigateBack();
      }
    });
  }
}); 