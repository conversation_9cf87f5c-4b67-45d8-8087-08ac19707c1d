import dataApi from '../../utils/dataApi.js';

Page({
  data: {
    isNewSchedule: true,
    scheduleId: null,
    scheduleData: {
      title: '',
      description: '',
      location: '',
      startTime: null,
      endTime: null,
      reminderType: 'none',
      status: 'pending'
    },
    
    // 时间选择器数据
    startDateTimeValue: [0, 0, 0, 0, 0], // 年、月、日、时、分
    startDateTimeRange: [], // [年选项, 月选项, 日选项, 时选项, 分选项]
    endDateTimeValue: [0, 0, 0, 0, 0],
    endDateTimeRange: [],
    
    // 选项配置
    reminderOptions: [
      { value: 'none', label: '无提醒' },
      { value: '10min', label: '10分钟前' },
      { value: '30min', label: '30分钟前' },
      { value: '1hour', label: '1小时前' },
      { value: '1day', label: '1天前' }
    ],
    
    statusOptions: [
      { value: 'pending', label: '待进行' },
      { value: 'ongoing', label: '进行中' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' }
    ],
    
    // 状态标识
    saving: false,
    deleting: false,
    loading: true
  },

  async onLoad(options) {
    console.log('=== 日程编辑页面 onLoad ===', options);
    console.log('所有options属性:', Object.keys(options));
    console.log('scheduleId:', options.scheduleId);
    console.log('scheduleId类型:', typeof options.scheduleId);
    console.log('scheduleId长度:', options.scheduleId ? options.scheduleId.length : 'undefined');
    
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    
    // 初始化数据API
    await this.initDataApi();
    
    // 判断是新建还是编辑模式
    if (options.scheduleId) {
      this.setData({
        isNewSchedule: false,
        scheduleId: options.scheduleId
      });
      console.log('进入编辑模式，scheduleId:', options.scheduleId);
      await this.loadSchedule(options.scheduleId);
    } else {
      this.setData({
        isNewSchedule: true
      });
      console.log('进入新建模式');
      await this.initNewSchedule();
    }
    
    // 初始化时间选择器
    this.initDateTimeRange();
    
    this.setData({ loading: false });
  },

  // 初始化数据API
  async initDataApi() {
    try {
      const success = await dataApi.init();
      if (success) {
        console.log('数据API初始化成功');
      } else {
        console.warn('数据API初始化失败，将使用降级方案');
      }
    } catch (error) {
      console.error('数据API初始化出错:', error);
    }
  },

  // 加载现有日程
  async loadSchedule(scheduleId) {
    try {
      console.log('加载日程数据:', scheduleId);
      console.log('scheduleId类型:', typeof scheduleId);
      console.log('scheduleId是否为空:', !scheduleId);
      
      if (!scheduleId) {
        throw new Error('日程ID不能为空');
      }
      
      wx.showLoading({ title: '加载中...' });
      
      const schedule = await dataApi.getSchedule(scheduleId);
      console.log('获取到的日程数据:', schedule);
      console.log('schedule类型:', typeof schedule);
      
      if (schedule) {
        console.log('日程数据详情:', {
          title: schedule.title,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          description: schedule.description,
          location: schedule.location,
          status: schedule.status,
          reminderType: schedule.reminderType
        });
        
        const startTime = schedule.startTime ? new Date(schedule.startTime) : new Date();
        const endTime = schedule.endTime ? new Date(schedule.endTime) : null;
        
        console.log('转换后的时间:', {
          startTime: startTime,
          endTime: endTime
        });
        
        this.setData({
          scheduleData: {
            title: schedule.title || '',
            description: schedule.description || '',
            location: schedule.location || '',
            startTime: startTime,
            endTime: endTime,
            reminderType: schedule.reminderType || 'none',
            status: schedule.status || 'pending'
          }
        });
        
        console.log('设置后的页面数据:', this.data.scheduleData);
        
        // 更新时间选择器
        this.updateDateTimePickerValue();
      } else {
        console.warn('未找到日程数据，ID:', scheduleId);
        throw new Error('日程不存在或已被删除');
      }
      
      wx.hideLoading();
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载日程失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack
      });
      
      wx.showModal({
        title: '加载失败',
        content: `无法加载日程数据：${error.message}`,
        confirmText: '返回',
        showCancel: true,
        cancelText: '重试',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          } else if (res.cancel) {
            // 重试加载
            this.loadSchedule(scheduleId);
          }
        }
      });
    }
  },

  // 初始化新日程
  async initNewSchedule() {
    const now = new Date();
    
    // 设置默认开始时间为当前时间后1小时，调整到30分钟间隔
    const defaultStartTime = this.getCurrentTimeRounded();
    defaultStartTime.setHours(defaultStartTime.getHours() + 1);
    
    this.setData({
      scheduleData: {
        ...this.data.scheduleData,
        startTime: defaultStartTime,
        endTime: null
      }
    });
    
    console.log('初始化新日程，默认开始时间:', defaultStartTime);
  },

  // 获取调整到30分钟间隔的当前时间
  getCurrentTimeRounded() {
    const now = new Date();
    const minutes = now.getMinutes();
    const roundedMinutes = minutes >= 30 ? 30 : 0;
    
    now.setMinutes(roundedMinutes);
    now.setSeconds(0);
    now.setMilliseconds(0);
    
    return now;
  },

  // 初始化时间选择器范围
  initDateTimeRange() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const currentDay = now.getDate();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    const yearOptions = [];
    const monthOptions = [];
    const dayOptions = [];
    const hourOptions = [];
    const minuteOptions = [];
    
    // 年份选项（当前年到未来2年）
    for (let i = 0; i < 3; i++) {
      yearOptions.push({
        label: `${currentYear + i}年`,
        value: currentYear + i
      });
    }
    
    // 月份选项
    for (let i = 1; i <= 12; i++) {
      monthOptions.push({
        label: `${i}月`,
        value: i
      });
    }
    
    // 日期选项
    for (let i = 1; i <= 31; i++) {
      dayOptions.push({
        label: `${i}日`,
        value: i
      });
    }
    
    // 小时选项
    for (let i = 0; i < 24; i++) {
      hourOptions.push({
        label: `${String(i).padStart(2, '0')}时`,
        value: i
      });
    }
    
    // 分钟选项（30分钟间隔）
    const minuteValues = [0, 30];
    minuteValues.forEach(minute => {
      minuteOptions.push({
        label: `${String(minute).padStart(2, '0')}分`,
        value: minute
      });
    });
    
    // 计算当前时间的默认选择索引
    const currentYearIndex = 0; // 当前年是第一个选项
    const currentMonthIndex = currentMonth - 1; // 月份从0开始索引
    const currentDayIndex = currentDay - 1; // 日期从0开始索引
    const currentHourIndex = currentHour; // 小时直接对应索引
    
    // 分钟选择最接近的30分钟间隔
    let currentMinuteIndex = 0;
    if (currentMinute >= 30) {
      currentMinuteIndex = 1; // 30分
    } else {
      currentMinuteIndex = 0; // 00分
    }
    
    const dateTimeRange = [yearOptions, monthOptions, dayOptions, hourOptions, minuteOptions];
    const dateTimeValue = [currentYearIndex, currentMonthIndex, currentDayIndex, currentHourIndex, currentMinuteIndex];
    
    this.setData({
      startDateTimeRange: dateTimeRange,
      endDateTimeRange: dateTimeRange,
      startDateTimeValue: dateTimeValue,
      endDateTimeValue: dateTimeValue
    });
    
    console.log('📅 日期时间选择器初始化:', {
      当前时间: now.toLocaleString(),
      默认选择: dateTimeValue,
      dateTimeRange长度: [yearOptions.length, monthOptions.length, dayOptions.length, hourOptions.length, minuteOptions.length]
    });
    
    // 设置默认值
    this.updateDateTimePickerValue();
  },

  // 更新时间选择器的值
  updateDateTimePickerValue() {
    const { scheduleData } = this.data;
    
    if (scheduleData.startTime) {
      const startValue = this.dateToPickerValue(scheduleData.startTime);
      this.setData({ startDateTimeValue: startValue });
    }
    
    if (scheduleData.endTime) {
      const endValue = this.dateToPickerValue(scheduleData.endTime);
      this.setData({ endDateTimeValue: endValue });
    }
  },

  // 将日期转换为选择器索引值
  dateToPickerValue(date) {
    if (!date) return [0, 0, 0, 0, 0];
    
    const targetDate = new Date(date);
    const year = targetDate.getFullYear();
    const month = targetDate.getMonth() + 1;
    const day = targetDate.getDate();
    const hour = targetDate.getHours();
    const minute = targetDate.getMinutes();
    
    const dateTimeRange = this.data.startDateTimeRange;
    if (!dateTimeRange || dateTimeRange.length < 5) {
      console.warn('dateTimeRange 未初始化');
      return [0, 0, 0, 0, 0];
    }
    
    // 找到对应的索引
    const yearIndex = dateTimeRange[0].findIndex(item => item.value === year);
    const monthIndex = dateTimeRange[1].findIndex(item => item.value === month);
    const dayIndex = dateTimeRange[2].findIndex(item => item.value === day);
    const hourIndex = dateTimeRange[3].findIndex(item => item.value === hour);
    const minuteIndex = dateTimeRange[4].findIndex(item => item.value === minute);
    
    return [
      Math.max(0, yearIndex),
      Math.max(0, monthIndex),
      Math.max(0, dayIndex),
      Math.max(0, hourIndex),
      Math.max(0, minuteIndex)
    ];
  },

  // 将选择器值转换为日期
  pickerValueToDate(value, dateTimeRange) {
    if (!value || !dateTimeRange || dateTimeRange.length < 5) {
      console.warn('pickerValueToDate: 参数不完整');
      return new Date();
    }
    
    const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = value;
    
    // 验证索引有效性
    if (yearIndex >= dateTimeRange[0].length || 
        monthIndex >= dateTimeRange[1].length || 
        dayIndex >= dateTimeRange[2].length || 
        hourIndex >= dateTimeRange[3].length || 
        minuteIndex >= dateTimeRange[4].length) {
      console.warn('pickerValueToDate: 索引超出范围');
      return new Date();
    }
    
    const year = dateTimeRange[0][yearIndex].value;
    const month = dateTimeRange[1][monthIndex].value;
    const day = dateTimeRange[2][dayIndex].value;
    const hour = dateTimeRange[3][hourIndex].value;
    const minute = dateTimeRange[4][minuteIndex].value;
    
    const date = new Date(year, month - 1, day, hour, minute, 0, 0);
    
    // 验证日期有效性
    if (isNaN(date.getTime())) {
      console.warn('无效的日期，使用当前时间');
      return new Date();
    }
    
    return date;
  },

  // 输入事件处理
  onTitleInput(e) {
    this.setData({
      'scheduleData.title': e.detail.value
    });
    console.log('标题输入:', e.detail.value);
  },

  onTitleChange(e) {
    this.setData({
      'scheduleData.title': e.detail.value.trim()
    });
    console.log('标题变更:', e.detail.value.trim());
  },

  onLocationInput(e) {
    this.setData({
      'scheduleData.location': e.detail.value
    });
  },

  onLocationChange(e) {
    this.setData({
      'scheduleData.location': e.detail.value.trim()
    });
  },

  onDescriptionInput(e) {
    this.setData({
      'scheduleData.description': e.detail.value
    });
  },

  onDescriptionChange(e) {
    this.setData({
      'scheduleData.description': e.detail.value.trim()
    });
  },

  // 开始时间选择
  onStartDateTimeChange(e) {
    console.log('🔄 开始时间选择器变化:', e.detail.value);
    
    try {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;
      const dateTimeRange = this.data.startDateTimeRange;
      
      // 强化索引验证
      const validation = this.validatePickerIndexes(dateTimeRange, e.detail.value);
      if (!validation.isValid) {
        console.error('⚠️ 选择器索引验证失败:', validation.error);
        this.resetToCurrentTime();
        return;
      }
      
      const year = dateTimeRange[0][yearIndex].value;
      const month = dateTimeRange[1][monthIndex].value;
      const day = dateTimeRange[2][dayIndex].value;
      const hour = dateTimeRange[3][hourIndex].value;
      const minute = dateTimeRange[4][minuteIndex].value;
      
      console.log('📅 解析的开始时间:', { year, month, day, hour, minute });
      
      // 创建日期对象并验证
      const startTime = this.createValidDate(year, month, day, hour, minute);
      if (!startTime) {
        console.error('⚠️ 无效的日期组合');
        this.resetToCurrentTime();
        return;
      }
      
      console.log('✅ 创建的开始时间对象:', startTime.toLocaleString());
      
      this.setData({
        'scheduleData.startTime': startTime,
        startDateTimeValue: e.detail.value
      });
      
      // 如果结束时间早于开始时间，自动调整结束时间
      if (this.data.scheduleData.endTime && this.data.scheduleData.endTime <= startTime) {
        const newEndTime = new Date(startTime);
        newEndTime.setHours(newEndTime.getHours() + 1);
        
        this.setData({
          'scheduleData.endTime': newEndTime
        });
        
        this.updateDateTimePickerValue();
        
        wx.showToast({
          title: '已自动调整结束时间',
          icon: 'none',
          duration: 2000
        });
      }
      
      console.log('✨ 开始时间更新完成');
      
    } catch (error) {
      console.error('❌ 开始时间选择器处理失败:', error);
      this.resetToCurrentTime();
    }
  },

  // 结束时间选择
  onEndDateTimeChange(e) {
    console.log('🔄 结束时间选择器变化:', e.detail.value);
    
    try {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;
      const dateTimeRange = this.data.endDateTimeRange;
      
      // 强化索引验证
      const validation = this.validatePickerIndexes(dateTimeRange, e.detail.value);
      if (!validation.isValid) {
        console.error('⚠️ 选择器索引验证失败:', validation.error);
        this.resetToCurrentTime();
        return;
      }
      
      const year = dateTimeRange[0][yearIndex].value;
      const month = dateTimeRange[1][monthIndex].value;
      const day = dateTimeRange[2][dayIndex].value;
      const hour = dateTimeRange[3][hourIndex].value;
      const minute = dateTimeRange[4][minuteIndex].value;
      
      console.log('📅 解析的结束时间:', { year, month, day, hour, minute });
      
      // 创建日期对象并验证
      const endTime = this.createValidDate(year, month, day, hour, minute);
      if (!endTime) {
        console.error('⚠️ 无效的日期组合');
        this.resetToCurrentTime();
        return;
      }
      
      console.log('✅ 创建的结束时间对象:', endTime.toLocaleString());
      
      // 验证结束时间不能早于开始时间
      if (endTime <= this.data.scheduleData.startTime) {
        wx.showToast({
          title: '结束时间不能早于开始时间',
          icon: 'error'
        });
        return;
      }
      
      this.setData({
        'scheduleData.endTime': endTime,
        endDateTimeValue: e.detail.value
      });
      
      console.log('✨ 结束时间更新完成');
      
    } catch (error) {
      console.error('❌ 结束时间选择器处理失败:', error);
      this.resetToCurrentTime();
    }
  },

  // 清除结束时间
  clearEndTime() {
    this.setData({
      'scheduleData.endTime': null,
      endDateTimeValue: [0, 0, 0, 0, 0]
    });
    
    wx.showToast({
      title: '已清除结束时间',
      icon: 'success'
    });
  },

  // 提醒类型选择
  onReminderSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'scheduleData.reminderType': value
    });
    console.log('选择提醒类型:', value);
  },

  // 状态选择
  onStatusSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'scheduleData.status': value
    });
    console.log('选择状态:', value);
  },

  // 格式化日期时间显示
  formatDateTime(date) {
    if (!date) return '';
    
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    if (isNaN(date.getTime())) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dateStr = `${year}-${month}-${day}`;
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const tomorrowStr = `${tomorrow.getFullYear()}-${String(tomorrow.getMonth() + 1).padStart(2, '0')}-${String(tomorrow.getDate()).padStart(2, '0')}`;
    
    let displayDate;
    if (dateStr === todayStr) {
      displayDate = '今天';
    } else if (dateStr === tomorrowStr) {
      displayDate = '明天';
    } else {
      displayDate = `${month}月${day}日`;
    }
    
    return `${displayDate} ${hour}:${minute}`;
  },

  // 验证表单数据
  validateScheduleData() {
    const { scheduleData } = this.data;
    
    // 标题验证
    if (!scheduleData.title || scheduleData.title.trim() === '') {
      return { valid: false, message: '请输入日程标题' };
    }
    
    if (scheduleData.title.length > 100) {
      return { valid: false, message: '日程标题不能超过100个字符' };
    }
    
    // 开始时间验证
    if (!scheduleData.startTime) {
      return { valid: false, message: '请选择开始时间' };
    }
    
    // 如果设置了结束时间，验证时间逻辑
    if (scheduleData.endTime) {
      if (scheduleData.endTime <= scheduleData.startTime) {
        return { valid: false, message: '结束时间必须晚于开始时间' };
      }
      
      const timeDiff = scheduleData.endTime - scheduleData.startTime;
      const maxDuration = 24 * 60 * 60 * 1000; // 24小时
      
      if (timeDiff > maxDuration) {
        return { valid: false, message: '日程持续时间不能超过24小时' };
      }
    }
    
    // 描述长度验证
    if (scheduleData.description && scheduleData.description.length > 500) {
      return { valid: false, message: '描述不能超过500个字符' };
    }
    
    // 地点长度验证
    if (scheduleData.location && scheduleData.location.length > 100) {
      return { valid: false, message: '地点不能超过100个字符' };
    }
    
    return { valid: true };
  },

  // 构建保存数据
  buildScheduleData() {
    const { scheduleData } = this.data;
    
    return {
      title: scheduleData.title.trim(),
      description: scheduleData.description.trim(),
      location: scheduleData.location.trim(),
      startTime: scheduleData.startTime.toISOString(),
      endTime: scheduleData.endTime ? scheduleData.endTime.toISOString() : null,
      reminderType: scheduleData.reminderType,
      status: scheduleData.status,
      source: 'manual',
      updatedAt: new Date().toISOString()
    };
  },

  // 保存日程
  async onSave() {
    if (this.data.saving) return;
    
    console.log('=== 开始保存日程 ===');
    
    // 验证数据
    const validation = this.validateScheduleData();
    if (!validation.valid) {
      wx.showToast({
        title: validation.message,
        icon: 'error',
        duration: 3000
      });
      return;
    }
    
    this.setData({ saving: true });
    
    try {
      const scheduleData = this.buildScheduleData();
      console.log('保存数据:', scheduleData);
      
      let result;
      if (this.data.isNewSchedule) {
        // 创建新日程
        result = await dataApi.createSchedule(scheduleData);
      } else {
        // 更新现有日程
        result = await dataApi.updateSchedule(this.data.scheduleId, scheduleData);
      }
      
      if (result && result.success) {
        const actionText = this.data.isNewSchedule ? '创建' : '保存';
        
        wx.showToast({
          title: `🎉 ${actionText}成功`,
          icon: 'success',
          duration: 2000
        });
        
        console.log(`${actionText}日程成功:`, result);
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack({
            success: () => {
              // 通知前一个页面刷新数据
              const pages = getCurrentPages();
              if (pages.length > 1) {
                const prevPage = pages[pages.length - 2];
                if (prevPage.refreshData) {
                  prevPage.refreshData();
                }
                if (prevPage.loadScheduleData) {
                  prevPage.loadScheduleData();
                }
              }
            }
          });
        }, 1500);
        
      } else {
        throw new Error(result ? result.error : '保存失败');
      }
      
    } catch (error) {
      console.error('保存日程失败:', error);
      
      wx.showModal({
        title: '保存失败',
        content: `保存过程中出现错误：${error.message}`,
        showCancel: false,
        confirmText: '我知道了'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 删除日程
  async onDelete() {
    if (this.data.deleting || this.data.isNewSchedule) return;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除日程"${this.data.scheduleData.title}"吗？`,
      confirmColor: '#ff4d4f',
      success: async (res) => {
        if (res.confirm) {
          this.setData({ deleting: true });
          
          try {
            const result = await dataApi.deleteSchedule(this.data.scheduleId);
            
            if (result && result.success) {
              wx.showToast({
                title: '🗑️ 删除成功',
                icon: 'success',
                duration: 2000
              });
              
              console.log('删除日程成功');
              
              setTimeout(() => {
                wx.navigateBack({
                  success: () => {
                    // 通知前一个页面刷新数据
                    const pages = getCurrentPages();
                    if (pages.length > 1) {
                      const prevPage = pages[pages.length - 2];
                      if (prevPage.refreshData) {
                        prevPage.refreshData();
                      }
                      if (prevPage.loadScheduleData) {
                        prevPage.loadScheduleData();
                      }
                    }
                  }
                });
              }, 1500);
              
            } else {
              throw new Error(result ? result.error : '删除失败');
            }
            
          } catch (error) {
            console.error('删除日程失败:', error);
            
            wx.showModal({
              title: '删除失败',
              content: `删除过程中出现错误：${error.message}`,
              showCancel: false,
              confirmText: '我知道了'
            });
          } finally {
            this.setData({ deleting: false });
          }
        }
      }
    });
  },

  // 取消操作
  onCancel() {
    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges()) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的更改，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  // 返回操作
  onBack() {
    this.onCancel();
  },

  // 检查是否有未保存的更改
  hasUnsavedChanges() {
    const { scheduleData } = this.data;
    
    // 新建模式：检查是否有任何输入
    if (this.data.isNewSchedule) {
      return !!(
        scheduleData.title.trim() ||
        scheduleData.description.trim() ||
        scheduleData.location.trim()
      );
    }
    
    // 编辑模式：这里可以与原始数据比较
    // 为简化起见，假设用户做了更改
    return true;
  },

  // 验证选择器索引
  validatePickerIndexes(dateTimeRange, value) {
    if (!dateTimeRange || !value || value.length !== 5) {
      return { isValid: false, error: '参数不完整' };
    }
    
    const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = value;
    
    if (yearIndex >= dateTimeRange[0].length || 
        monthIndex >= dateTimeRange[1].length || 
        dayIndex >= dateTimeRange[2].length || 
        hourIndex >= dateTimeRange[3].length || 
        minuteIndex >= dateTimeRange[4].length) {
      return { isValid: false, error: '索引超出范围' };
    }
    
    return { isValid: true };
  },

  // 创建有效日期对象
  createValidDate(year, month, day, hour, minute) {
    if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(hour) || isNaN(minute)) {
      return null;
    }
    
    const date = new Date(year, month - 1, day, hour, minute, 0, 0);
    
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date;
  },

  // 重置到当前时间
  resetToCurrentTime() {
    const now = new Date();
    this.setData({
      'scheduleData.startTime': now,
      'scheduleData.endTime': null,
      startDateTimeValue: [0, 0, 0, 0, 0],
      endDateTimeValue: [0, 0, 0, 0, 0]
    });
    this.updateDateTimePickerValue();
  }
}); 