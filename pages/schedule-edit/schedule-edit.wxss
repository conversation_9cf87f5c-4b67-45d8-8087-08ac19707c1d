/* pages/schedule-edit/schedule-edit.wxss */

/* 整体容器 */
.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-bottom: 140rpx;
}

/* 顶部导航栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin: 16rpx 24rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}

.back-button:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.back-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.page-title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
  letter-spacing: -0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-right {
  display: flex;
  align-items: center;
}

.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 主要内容区域 */
.content {
  flex: 1;
  height: calc(100vh - 200rpx);
  padding: 24rpx 24rpx 0;
}

/* 表单区块 */
.section {
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-header {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.3rpx;
}

.section-desc {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 400;
}

/* 输入框样式 */
.input-wrapper, .textarea-wrapper {
  position: relative;
}

.title-input, .location-input {
  width: 100%;
  height: 88rpx;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1f2937;
  transition: all 0.3s ease;
}

.title-input:focus, .location-input:focus {
  background: white;
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.title-input::placeholder, .location-input::placeholder {
  color: #9ca3af;
  font-size: 26rpx;
}

/* 文本域样式 */
.description-input {
  width: 100%;
  min-height: 60rpx;
  max-height: 80rpx;
  padding: 16rpx;
  background: #f8fafc;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #1f2937;
  line-height: 1.4;
  transition: all 0.3s ease;
  resize: none;
}

.description-input:focus {
  background: white;
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.description-input::placeholder {
  color: #9ca3af;
  font-size: 22rpx;
}

.char-count {
  position: absolute;
  bottom: 8rpx;
  right: 12rpx;
  font-size: 20rpx;
  color: #9ca3af;
}

/* 特殊调整：描述区块 */
.description-section {
  margin: 12rpx 24rpx 32rpx;
  padding: 16rpx;
}

.description-section .section-header {
  margin-bottom: 12rpx;
}

.description-section .section-title {
  font-size: 26rpx;
}

/* 日期时间选择器 */
.datetime-picker-container {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 12rpx;
  overflow: hidden;
}

.current-time-display {
  background: white;
  border-radius: 8rpx;
  padding: 12rpx;
  margin-bottom: 12rpx;
  text-align: center;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.current-time-text {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
}

.datetime-picker-view {
  width: 100%;
  height: 240rpx;
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 选中指示线 */
.datetime-picker-view::before,
.datetime-picker-view::after {
  content: '';
  position: absolute;
  left: 20rpx;
  right: 20rpx;
  height: 1rpx;
  background: rgba(102, 126, 234, 0.3);
  z-index: 10;
  pointer-events: none;
}

.datetime-picker-view::before {
  top: 96rpx; /* (240 - 48) / 2 */
}

.datetime-picker-view::after {
  top: 144rpx; /* (240 - 48) / 2 + 48 */
}

.picker-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
}

.picker-item {
  height: 48rpx;
  line-height: 48rpx;
  font-size: 26rpx;
  color: #374151;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 100%;
}

/* picker-view 默认样式覆盖 */
picker-view {
  border-radius: 8rpx !important;
}

picker-view-column {
  text-align: center !important;
}

/* 选中指示器样式 */
.picker-indicator {
  background: rgba(102, 126, 234, 0.1);
  border-top: 1rpx solid rgba(102, 126, 234, 0.3);
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.3);
}

/* 清除按钮样式 */
.clear-end-time {
  text-align: center;
  margin-top: 12rpx;
  padding: 8rpx;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 6rpx;
  border: 1rpx solid rgba(255, 77, 79, 0.2);
}

.clear-text {
  font-size: 24rpx;
  color: #ff4d4f;
  font-weight: 500;
}

/* 选项组样式 */
.reminder-options, .status-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reminder-item, .status-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.reminder-item:active, .status-item:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

.reminder-radio, .status-radio {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid #d1d5db;
  transition: all 0.3s ease;
}

.reminder-radio.checked, .status-radio.checked {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.radio-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: bold;
}

.reminder-radio.checked .radio-text, .status-radio.checked .radio-text {
  color: #3b82f6;
}

.reminder-label, .status-label {
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 24rpx calc(24rpx + env(safe-area-inset-bottom));
  background: linear-gradient(to top, rgba(248, 250, 252, 0.95) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.button-row {
  display: flex;
  gap: 16rpx;
}

/* 底部按钮样式 */
.bottom-actions .t-button {
  flex: 1;
  height: 88rpx !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  border: none !important;
}

.bottom-actions .t-button:active {
  transform: scale(0.98) !important;
}

/* 取消按钮 */
.cancel-btn {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
  color: #6b7280 !important;
  box-shadow: 0 4rpx 12rpx rgba(107, 114, 128, 0.2) !important;
}

/* 保存按钮 */
.save-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
  box-shadow: 0 6rpx 20rpx rgba(59, 130, 246, 0.4) !important;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2) !important;
}

/* 删除按钮 */
.delete-btn-bottom {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  color: white !important;
  box-shadow: 0 6rpx 20rpx rgba(255, 77, 79, 0.4) !important;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2) !important;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .section {
    margin-bottom: 24rpx;
    padding: 24rpx;
  }
  
  .section-title {
    font-size: 24rpx;
  }
  
  .title-input, .location-input {
    height: 80rpx;
    font-size: 26rpx;
  }
  
  .bottom-actions .t-button {
    height: 80rpx !important;
    font-size: 26rpx !important;
  }
} 