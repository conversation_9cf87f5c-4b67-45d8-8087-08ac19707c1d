<!--schedule-edit.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-left">
      <view class="back-button" bind:tap="onBack">
        <text class="back-text">←</text>
      </view>
      <text class="page-title">{{isNewSchedule ? '新建日程' : '编辑日程'}}</text>
    </view>
    <view class="header-right">
      <text class="tips-text">* 必填</text>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <scroll-view class="content" scroll-y="true" enable-flex="true">
    
    <!-- 日程标题 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">日程标题 *</text>
      </view>
      <view class="input-wrapper">
        <input 
          class="title-input"
          type="text"
          placeholder="请输入日程标题"
          value="{{scheduleData.title}}"
          bind:input="onTitleInput"
          bind:change="onTitleChange"
          maxlength="100"
          confirm-type="next"
        />
      </view>
    </view>

    <!-- 开始时间 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">开始时间 *</text>
      </view>
      <view class="datetime-picker-container">
        <!-- 显示当前选中时间 -->
        <view class="current-time-display">
          <text class="current-time-text">当前选中: {{formatDateTime(scheduleData.startTime) || '未设置'}}</text>
        </view>
        
        <picker-view
          class="datetime-picker-view"
          value="{{startDateTimeValue}}"
          bindchange="onStartDateTimeChange"
          indicator-style="height: 48rpx; border-top: 1px solid #e2e8f0; border-bottom: 1px solid #e2e8f0;"
        >
          <picker-view-column wx:if="{{startDateTimeRange[0]}}">
            <view
              class="picker-item"
              wx:for="{{startDateTimeRange[0]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{startDateTimeRange[1]}}">
            <view
              class="picker-item"
              wx:for="{{startDateTimeRange[1]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{startDateTimeRange[2]}}">
            <view
              class="picker-item"
              wx:for="{{startDateTimeRange[2]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{startDateTimeRange[3]}}">
            <view
              class="picker-item"
              wx:for="{{startDateTimeRange[3]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{startDateTimeRange[4]}}">
            <view
              class="picker-item"
              wx:for="{{startDateTimeRange[4]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 结束时间 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">结束时间</text>
        <text class="section-desc">（可选，留空表示无明确结束时间）</text>
      </view>
      <view class="datetime-picker-container">
        <!-- 显示当前选中时间 -->
        <view class="current-time-display">
          <text class="current-time-text">当前选中: {{scheduleData.endTime ? formatDateTime(scheduleData.endTime) : '未设置'}}</text>
        </view>
        
        <picker-view
          class="datetime-picker-view"
          value="{{endDateTimeValue}}"
          bindchange="onEndDateTimeChange"
          indicator-style="height: 48rpx; border-top: 1px solid #e2e8f0; border-bottom: 1px solid #e2e8f0;"
        >
          <picker-view-column wx:if="{{endDateTimeRange[0]}}">
            <view
              class="picker-item"
              wx:for="{{endDateTimeRange[0]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{endDateTimeRange[1]}}">
            <view
              class="picker-item"
              wx:for="{{endDateTimeRange[1]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{endDateTimeRange[2]}}">
            <view
              class="picker-item"
              wx:for="{{endDateTimeRange[2]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{endDateTimeRange[3]}}">
            <view
              class="picker-item"
              wx:for="{{endDateTimeRange[3]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
          <picker-view-column wx:if="{{endDateTimeRange[4]}}">
            <view
              class="picker-item"
              wx:for="{{endDateTimeRange[4]}}"
              wx:key="value"
            >
              {{item.label}}
            </view>
          </picker-view-column>
        </picker-view>
        
        <view class="clear-end-time" wx:if="{{scheduleData.endTime}}" bind:tap="clearEndTime">
          <text class="clear-text">清除</text>
        </view>
      </view>
    </view>

    <!-- 地点 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">地点</text>
      </view>
      <view class="input-wrapper">
        <input 
          class="location-input"
          type="text"
          placeholder="请输入地点（可选）"
          value="{{scheduleData.location}}"
          bind:input="onLocationInput"
          bind:change="onLocationChange"
          maxlength="100"
          confirm-type="next"
        />
      </view>
    </view>

    <!-- 日程描述 -->
    <view class="section description-section">
      <view class="section-header">
        <text class="section-title">描述详情</text>
      </view>
      <view class="textarea-wrapper">
        <textarea 
          class="description-input"
          placeholder="请输入日程描述或备注（可选）"
          value="{{scheduleData.description}}"
          bind:input="onDescriptionInput"
          bind:change="onDescriptionChange"
          maxlength="500"
          auto-height="{{true}}"
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="char-count">{{scheduleData.description.length}}/500</view>
      </view>
    </view>

    <!-- 提醒设置 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">提醒设置</text>
      </view>
      <view class="reminder-options">
        <view class="reminder-item" wx:for="{{reminderOptions}}" wx:key="value">
          <view 
            class="reminder-radio {{scheduleData.reminderType === item.value ? 'checked' : ''}}"
            bind:tap="onReminderSelect"
            data-value="{{item.value}}"
          >
            <text class="radio-text">{{scheduleData.reminderType === item.value ? '●' : '○'}}</text>
          </view>
          <text class="reminder-label">{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- 状态 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">状态</text>
      </view>
      <view class="status-options">
        <view class="status-item" wx:for="{{statusOptions}}" wx:key="value">
          <view 
            class="status-radio {{scheduleData.status === item.value ? 'checked' : ''}}"
            bind:tap="onStatusSelect"
            data-value="{{item.value}}"
          >
            <text class="radio-text">{{scheduleData.status === item.value ? '●' : '○'}}</text>
          </view>
          <text class="status-label">{{item.label}}</text>
        </view>
      </view>
    </view>

  </scroll-view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="button-row">
      <!-- 新建模式：取消和创建 -->
      <block wx:if="{{isNewSchedule}}">
        <t-button 
          theme="default" 
          size="large"
          icon=""
          bind:tap="onCancel"
          class="cancel-btn"
        >
          取消
        </t-button>
        <t-button 
          theme="primary" 
          size="large"
          bind:tap="onSave"
          class="save-btn"
          loading="{{saving}}"
        >
          创建日程
        </t-button>
      </block>
      
      <!-- 编辑模式：删除和保存 -->
      <block wx:else>
        <t-button 
          theme="danger" 
          size="large"
          bind:tap="onDelete"
          class="delete-btn-bottom"
          loading="{{deleting}}"
        >
          删除日程
        </t-button>
        <t-button 
          theme="primary" 
          size="large"
          bind:tap="onSave"
          class="save-btn"
          loading="{{saving}}"
        >
          保存修改
        </t-button>
      </block>
    </view>
  </view>
</view> 