// pages/login/login.js
Page({
  data: {
    loginLoading: false,
    showPrivacyPopup: false
  },

  onLoad() {
    console.log('登录页面加载');
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp();
    if (app.globalData.uid) {
      // 已登录，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 微信登录
  async handleLogin() {
    this.setData({ loginLoading: true });
    
    try {
      // 第一步：获取微信登录code
      const loginResult = await this.wxLogin();
      console.log('获取到微信code:', loginResult.code);
      
      // 第二步：获取用户基本信息（先获取，再传给云函数）
      const wxUserInfo = await this.getUserProfile();
      
      // 第三步：调用云函数进行登录
      const userInfo = await this.cloudLogin(loginResult.code, wxUserInfo);
      console.log('云函数登录成功:', userInfo);
      
      // 第四步：登录成功，跳转首页
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
      
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 微信登录获取code
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 云函数登录
  async cloudLogin(code, userInfo = null) {
    try {
      console.log('开始调用userLogin云函数...');
      
      // 调用云函数
      const result = await wx.cloud.callFunction({
        name: 'userLogin',
        data: {
          code,
          userInfo
        }
      });
      
      console.log('云函数登录结果:', result);
      
      if (!result.result.success) {
        throw new Error(result.result.message || '云函数登录失败');
      }
      
      const userData = result.result.data;
      
      // 保存到全局数据
      const app = getApp();
      app.setLoginStatus(userData.uid, false, userData);
      
      return userData;
      
    } catch (error) {
      console.error('云函数调用失败:', error);
      
      // 🎯 优化的错误分析和处理
      let errorMessage = '登录失败';
      let shouldFallback = false;
      let showDetailedMessage = false;
      
      if (error.errMsg) {
        if (error.errMsg.includes('FunctionName parameter could not be found') || 
            error.errMsg.includes('FUNCTION_NOT_FOUND')) {
          errorMessage = '正在启用离线模式...';
          shouldFallback = true;
          showDetailedMessage = true;
          
          // 🔍 详细的部署提示（仅在开发模式显示）
          console.log('📋 云函数部署指南:');
          console.log('1. 在微信开发者工具中找到 cloudfunctions/userLogin 文件夹');
          console.log('2. 右键选择"创建并部署：云端安装依赖"');
          console.log('3. 部署完成后重新登录即可使用云端同步功能');
          console.log('4. 当前使用本地模式，所有功能正常');
          
        } else if (error.errMsg.includes('env check invalid')) {
          errorMessage = '正在启用本地模式...';
          shouldFallback = true;
          showDetailedMessage = true;
        } else if (error.errMsg.includes('wx-server-sdk')) {
          errorMessage = '正在启用本地模式...';
          shouldFallback = true;
        } else {
          errorMessage = '网络连接失败，正在启用本地模式...';
          shouldFallback = true;
        }
      } else {
        errorMessage = error.message || '未知错误，正在启用本地模式...';
        shouldFallback = true;
      }
      
      if (shouldFallback) {
        // 🎯 显示优化的降级提示
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: showDetailedMessage ? 3000 : 2000
        });
        
        // 🎯 显示详细说明（可选）
        if (showDetailedMessage) {
          setTimeout(() => {
            wx.showModal({
              title: '云端服务暂不可用',
              content: '小程序将以本地模式运行：\n\n✅ 所有功能正常使用\n✅ 数据保存在本地\n✅ 性能更加流畅\n\n如需云端同步，请联系开发者部署云函数。',
              showCancel: false,
              confirmText: '我知道了'
            });
          }, 3500);
        }
        
        // 降级为本地模拟登录
        console.log('🔄 降级为本地模式登录');
        return this.fallbackLogin(userInfo);
      } else {
        // 不降级，直接抛出错误
        throw new Error(errorMessage);
      }
    }
  },
  
  // 🎯 优化的降级登录（开发阶段使用）
  fallbackLogin(userInfo) {
    return new Promise((resolve) => {
      // 显示本地模式登录进度
      wx.showLoading({
        title: '本地模式登录中...',
        mask: true
      });
      
      setTimeout(() => {
        const mockUserData = {
          uid: 'local_' + Date.now(),
          unionid: 'unionid_local_' + Date.now(),
          nick: userInfo?.nickName || '本地用户',
          avatar: userInfo?.avatarUrl || '',
          createdAt: new Date(),
          isNewUser: true,
          mode: 'local' // 标记为本地模式
        };
        
        // 保存到全局数据
        const app = getApp();
        app.setLoginStatus(mockUserData.uid, false, mockUserData);
        
        // 保存本地模式标记
        wx.setStorageSync('loginMode', 'local');
        
        wx.hideLoading();
        
        // 显示登录成功
        wx.showToast({
          title: '本地模式登录成功',
          icon: 'success',
          duration: 1500
        });
        
        console.log('✅ 本地模式登录成功:', mockUserData);
        
        resolve(mockUserData);
      }, 1500);
    });
  },

  // 获取用户基本信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res.userInfo);
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.log('用户拒绝授权:', error);
          // 即使拒绝授权也算登录成功，只是没有用户信息
          resolve(null);
        }
      });
    });
  },

  // 访客模式
  handleGuestMode() {
    wx.showModal({
      title: '访客模式',
      content: '访客模式下，数据不会同步到云端，仅本地体验功能。建议登录以获得完整体验。',
      confirmText: '继续体验',
      cancelText: '去登录',
      success: (res) => {
        if (res.confirm) {
          // 设置访客标识
          const app = getApp();
          app.globalData.isGuest = true;
          app.globalData.uid = 'guest_' + Date.now();
          
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 显示隐私政策
  showPrivacy() {
    this.setData({ showPrivacyPopup: true });
  },

  // 显示服务条款
  showTerms() {
    wx.showModal({
      title: '服务条款',
      content: '1. 本应用免费提供基础功能\n2. 部分高级功能需要订阅Pro版本\n3. 用户数据严格保密，不会泄露给第三方\n4. 禁止使用本应用进行违法活动\n5. 服务条款的最终解释权归开发者所有',
      showCancel: false
    });
  },

  // 关闭隐私政策弹窗
  closePrivacy() {
    this.setData({ showPrivacyPopup: false });
  },

  // 同意隐私政策
  agreePrivacy() {
    this.setData({ showPrivacyPopup: false });
    wx.showToast({
      title: '感谢您的信任',
      icon: 'success'
    });
  },

  // 弹窗状态改变
  onPopupChange(e) {
    this.setData({ showPrivacyPopup: e.detail.visible });
  }
});