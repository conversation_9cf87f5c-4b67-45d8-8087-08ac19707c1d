/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9ff 0%, #ffffff 50%);
  display: flex;
  flex-direction: column;
  padding: 0 64rpx;
}

/* 应用标识区域 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 120rpx;
  margin-bottom: 80rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(18, 150, 219, 0.2);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #1296db;
  margin-bottom: 16rpx;
}

.app-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 功能特色展示 */
.features {
  display: flex;
  justify-content: space-around;
  margin-bottom: 80rpx;
  padding: 0 32rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.feature-icon {
  font-size: 48rpx;
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(18, 150, 219, 0.1);
  border-radius: 50%;
}

.feature-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 登录区域 */
.login-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-bottom: 32rpx;
}

/* 原生按钮样式重写 */
.login-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  background: #1296db;
  color: white;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(18, 150, 219, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn::after {
  border: none;
}

.login-btn.loading {
  background: #ccc;
}

.login-btn[disabled] {
  background: #ccc;
  color: #999;
}

.login-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 32rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.link-text {
  font-size: 24rpx;
  color: #1296db;
  text-decoration: underline;
}

/* 访客模式 */
.guest-section {
  margin-bottom: 64rpx;
}

.guest-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  border: 2rpx solid #e0e0e0;
  background: white;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guest-btn::after {
  border: none;
}

/* 弹窗遮罩 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-end;
}

.popup-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 弹窗样式 */
.popup-content {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.popup-mask.show .popup-content {
  transform: translateY(0);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
}

.popup-body {
  flex: 1;
  padding: 32rpx;
  max-height: 500rpx;
}

.privacy-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}

.popup-actions {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.agree-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: #1296db;
  color: white;
  font-size: 32rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agree-btn::after {
  border: none;
}

/* 响应式适配 */
@media (max-height: 600px) {
  .header {
    margin-top: 60rpx;
    margin-bottom: 40rpx;
  }
  
  .features {
    margin-bottom: 40rpx;
  }
}

/* 安全区域适配 */
.container {
  padding-bottom: env(safe-area-inset-bottom);
}