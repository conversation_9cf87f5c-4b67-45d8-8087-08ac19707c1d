<!--pages/login/login.wxml-->
<view class="container">
  <!-- 应用Logo和介绍 -->
  <view class="header">
    <image class="logo" src="/assets/icons/app-icon-120.png" mode="aspectFit"/>
    <text class="app-name">一句话全能助手</text>
    <text class="app-desc">语音记录，智能分类，轻松管理</text>
  </view>

  <!-- 功能特色展示 -->
  <view class="features">
    <view class="feature-item">
      <t-icon name="microphone" size="48" color="#1296db" />
      <text class="feature-text">语音输入</text>
    </view>
    <view class="feature-item">
      <t-icon name="chart-bubble" size="48" color="#1296db" />
      <text class="feature-text">智能分析</text>
    </view>
    <view class="feature-item">
      <t-icon name="calendar" size="48" color="#1296db" />
      <text class="feature-text">日程同步</text>
    </view>
  </view>

  <!-- 登录区域 -->
  <view class="login-section">
    <t-button 
      theme="primary" 
      size="large"
      bind:tap="handleLogin"
      loading="{{loginLoading}}"
      disabled="{{loginLoading}}"
      class="login-btn"
    >
      {{loginLoading ? '登录中...' : '微信快捷登录'}}
    </t-button>
    
    <view class="login-tips">
      <text class="tip-text">登录即同意</text>
      <text class="link-text" bind:tap="showPrivacy">《隐私政策》</text>
      <text class="tip-text">和</text>
      <text class="link-text" bind:tap="showTerms">《服务条款》</text>
    </view>
  </view>

  <!-- 免登录体验 -->
  <view class="guest-section">
    <t-button 
      theme="default" 
      variant="outline"
      size="large"
      bind:tap="handleGuestMode"
      class="guest-btn"
    >
      先看看 (访客模式)
    </t-button>
  </view>
</view>

<!-- 隐私政策弹窗 -->
<t-popup 
  visible="{{showPrivacyPopup}}" 
  bind:visible-change="onPopupChange"
  placement="bottom"
>
  <view class="popup-content">
    <view class="popup-header">
      <text class="popup-title">隐私政策</text>
      <t-icon name="close" size="24" bind:tap="closePrivacy" />
    </view>
    <scroll-view class="popup-body" scroll-y>
      <text class="privacy-text" user-select>
        我们重视您的隐私保护。使用本应用时，我们可能收集以下信息：
        
        1. 基本信息：微信昵称、头像用于个性化显示
        2. 使用数据：语音输入内容用于智能分析和功能改进
        3. 设备信息：设备型号用于兼容性优化
        
        我们承诺：
        • 不会收集您的隐私敏感信息
        • 所有数据仅用于应用功能提供
        • 严格遵守数据安全规范
        • 支持数据删除和注销账户
        
        详细条款请访问官网查看。
      </text>
    </scroll-view>
    <view class="popup-actions">
      <t-button theme="primary" bind:tap="agreePrivacy">同意</t-button>
    </view>
  </view>
</t-popup>