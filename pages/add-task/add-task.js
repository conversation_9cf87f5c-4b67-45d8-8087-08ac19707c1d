Page({
  data: {
    inputText: '',
    isRecording: false,
    parseResult: {},
    quickOptions: [
      {
        id: 1,
        text: '记录支出',
        icon: 'money-circle',
        color: '#ff4d4f',
        type: 'expense'
      },
      {
        id: 2,
        text: '添加日程',
        icon: 'calendar',
        color: '#1890ff',
        type: 'schedule'
      },
      {
        id: 3,
        text: '记录收入',
        icon: 'dollar-circle',
        color: '#52c41a',
        type: 'income'
      },
      {
        id: 4,
        text: '添加待办',
        icon: 'check-circle',
        color: '#722ed1',
        type: 'todo'
      }
    ]
  },

  onLoad(options) {
    // 如果从其他页面传入了类型参数
    if (options.type) {
      const option = this.data.quickOptions.find(opt => opt.type === options.type);
      if (option) {
        this.selectQuickOption({ currentTarget: { dataset: { option } } });
      }
    }
  },

  // 关闭模态框
  closeModal() {
    wx.navigateBack();
  },

  // 输入文本改变
  onInputChange(e) {
    const text = e.detail.value;
    this.setData({
      inputText: text
    });
    
    // 实时解析用户输入
    if (text.length > 2) {
      this.parseUserInput(text);
    } else {
      this.setData({
        parseResult: {}
      });
    }
  },

  // 开始录音
  startRecording() {
    this.setData({
      isRecording: true
    });
    
    // 这里可以集成语音识别功能
    console.log('开始录音');
    
    // 模拟录音结果
    setTimeout(() => {
      this.setData({
        isRecording: false,
        inputText: '今天在超市买菜花了50元'
      });
      this.parseUserInput('今天在超市买菜花了50元');
    }, 3000);
  },

  // 停止录音
  stopRecording() {
    this.setData({
      isRecording: false
    });
    console.log('停止录音');
  },

  // 选择快速选项
  selectQuickOption(e) {
    const option = e.currentTarget.dataset.option;
    this.setData({
      inputText: `添加${option.text}`,
      parseResult: {
        type: option.type,
        typeText: option.text
      }
    });
  },

  // 解析用户输入
  parseUserInput(text) {
    // 这里实现NLP解析逻辑
    const result = this.simpleNLPParse(text);
    this.setData({
      parseResult: result
    });
  },

  // 简单的NLP解析模拟
  simpleNLPParse(text) {
    const result = {};
    
    // 检测类型
    if (text.includes('花了') || text.includes('支出') || text.includes('买')) {
      result.type = 'expense';
      result.typeText = '支出记录';
    } else if (text.includes('收入') || text.includes('赚了') || text.includes('工资')) {
      result.type = 'income';
      result.typeText = '收入记录';
    } else if (text.includes('会议') || text.includes('约') || text.includes('时间')) {
      result.type = 'schedule';
      result.typeText = '日程安排';
    } else {
      result.type = 'todo';
      result.typeText = '待办事项';
    }
    
    // 提取金额
    const amountMatch = text.match(/(\d+(?:\.\d+)?)元?/);
    if (amountMatch) {
      result.amount = amountMatch[1];
    }
    
    // 提取时间
    const timeMatch = text.match(/(\d{1,2}[：:]\d{2})/);
    if (timeMatch) {
      result.time = timeMatch[1];
    } else if (text.includes('明天')) {
      result.time = '明天';
    } else if (text.includes('今天')) {
      result.time = '今天';
    }
    
    // 提取分类
    if (text.includes('超市') || text.includes('菜')) {
      result.category = '生活用品';
    } else if (text.includes('餐厅') || text.includes('吃')) {
      result.category = '餐饮';
    } else if (text.includes('交通') || text.includes('打车')) {
      result.category = '交通';
    }
    
    return result;
  },

  // 确认任务
  confirmTask() {
    const { inputText, parseResult } = this.data;
    
    if (!inputText && !parseResult.type) {
      wx.showToast({
        title: '请输入任务内容',
        icon: 'none'
      });
      return;
    }

    // 跳转到确认页面
    const taskData = {
      text: inputText,
      ...parseResult
    };
    
    wx.navigateTo({
      url: `/pages/confirm/confirm?data=${encodeURIComponent(JSON.stringify(taskData))}`
    });
  }
})