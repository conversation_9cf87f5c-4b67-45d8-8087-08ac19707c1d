/* pages/add-task/add-task.wxss */
.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin: 16rpx 24rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.placeholder {
  width: 48rpx;
}

/* 语音输入区域 */
.voice-input-section {
  margin: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.task-input {
  min-height: 200rpx;
  background: transparent !important;
}

/* 录音按钮 */
.voice-record {
  margin: 24rpx 32rpx;
}

.record-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

/* 录制中状态 */
.recording-state {
  margin: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.recording-animation {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.wave {
  width: 8rpx;
  height: 40rpx;
  background: #1890ff;
  border-radius: 4rpx;
  animation: wave 1.2s ease-in-out infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.2s;
}

.wave:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
}

.recording-text {
  font-size: 32rpx;
  color: #1890ff;
  margin-bottom: 32rpx;
  display: block;
}

.stop-btn {
  width: 200rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* 快速选项 */
.quick-options {
  margin: 24rpx 32rpx;
}

.options-title {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 24rpx;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.option-item {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  transition: all 0.2s;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.option-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

/* 解析结果 */
.parse-result {
  margin: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  display: flex;
  align-items: center;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  padding: 24rpx 32rpx 48rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

.confirm-btn {
  width: 100%;
}