<!--add-task.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <t-icon name="close" size="24" color="#333" bind:tap="closeModal" />
    <text class="title">新任务</text>
    <view class="placeholder"></view>
  </view>

  <!-- 语音输入区域 -->
  <view class="voice-input-section">
    <t-textarea
      placeholder="说说你想做什么？"
      value="{{inputText}}"
      bind:input="onInputChange"
      maxlength="200"
      indicator
      class="task-input"
    />
  </view>

  <!-- 语音录制按钮 -->
  <view class="voice-record" wx:if="{{!isRecording}}">
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="startRecording"
      class="record-btn"
    >
      <t-icon name="microphone" size="24" /> 按住说话
    </t-button>
  </view>

  <!-- 录制中状态 -->
  <view class="recording-state" wx:if="{{isRecording}}">
    <view class="recording-animation">
      <view class="wave"></view>
      <view class="wave"></view>
      <view class="wave"></view>
    </view>
    <text class="recording-text">正在聆听...</text>
    <t-button 
      theme="default" 
      size="medium" 
      bind:tap="stopRecording"
      class="stop-btn"
    >
      停止录音
    </t-button>
  </view>

  <!-- 快速选项 -->
  <view class="quick-options" wx:if="{{!inputText && !isRecording}}">
    <text class="options-title">或选择快速选项：</text>
    <view class="options-grid">
      <view 
        class="option-item" 
        wx:for="{{quickOptions}}" 
        wx:key="id"
        bind:tap="selectQuickOption"
        data-option="{{item}}"
      >
        <t-icon name="{{item.icon}}" size="20" color="{{item.color}}" />
        <text class="option-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 解析结果预览 -->
  <view class="parse-result" wx:if="{{parseResult.type}}">
    <view class="result-header">
      <t-icon name="check-circle" size="20" color="#52c41a" />
      <text class="result-title">识别结果</text>
    </view>
    
    <view class="result-content">
      <view class="result-item">
        <text class="result-label">类型：</text>
        <text class="result-value">{{parseResult.typeText}}</text>
      </view>
      
      <view class="result-item" wx:if="{{parseResult.amount}}">
        <text class="result-label">金额：</text>
        <text class="result-value">¥{{parseResult.amount}}</text>
      </view>
      
      <view class="result-item" wx:if="{{parseResult.time}}">
        <text class="result-label">时间：</text>
        <text class="result-value">{{parseResult.time}}</text>
      </view>
      
      <view class="result-item" wx:if="{{parseResult.category}}">
        <text class="result-label">分类：</text>
        <text class="result-value">{{parseResult.category}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="confirmTask"
      disabled="{{!inputText && !parseResult.type}}"
      class="confirm-btn"
    >
      {{parseResult.type ? '确认添加' : '添加'}}
    </t-button>
  </view>
</view>