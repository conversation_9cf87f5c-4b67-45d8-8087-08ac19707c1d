// pages/chat/chat.js
const app = getApp()

Page({
  data: {
    messages: [], // 消息列表
    inputValue: '', // 输入框的值
    isInputFocused: false, // 输入框是否聚焦
    isVoiceInputMode: false, // 是否为语音输入模式
    isRecording: false, // 是否正在录音
    recordingTime: 0, // 录音时长
    showCancelTip: false, // 是否显示取消录音提示
    isTyping: false, // 是否正在输入（AI响应中）
    showMoreActions: false, // 是否显示更多操作面板
    scrollToMessage: '', // 要滚动到的消息ID
    isLoadingHistory: false, // 是否正在加载历史消息
    playingVoiceId: '', // 正在播放的语音消息ID
    userAvatar: '', // 用户头像
    assistantAvatar: '/assets/icons/assistant-avatar.png', // 助手头像
    recorderManager: null, // 录音管理器
    innerAudioContext: null, // 音频播放器
    startY: 0, // 开始触摸的Y坐标
    currentConversationId: '', // 当前会话ID
    pageSize: 20, // 每页加载的消息数量
    hasMoreHistory: true, // 是否还有更多历史消息
  },

  onLoad(options) {
    // 初始化录音管理器
    this.initRecorder()
    // 初始化音频播放器
    this.initAudioContext()
    // 获取用户信息
    this.setUserInfo()
    // 创建新会话或加载已有会话
    this.initConversation(options.conversationId)
  },

  onShow() {
    // 标记当前页面为活跃状态
    this.isPageActive = true
  },

  onHide() {
    // 标记当前页面为非活跃状态
    this.isPageActive = false
    // 停止语音播放
    this.stopVoicePlay()
  },

  onUnload() {
    // 页面卸载时清理资源
    this.stopVoicePlay()
    this.recorderManager?.stop()
    this.innerAudioContext?.destroy()
  },

  // 初始化录音管理器
  initRecorder() {
    this.recorderManager = wx.getRecorderManager()
    
    this.recorderManager.onStart(() => {
      this.setData({ 
        isRecording: true,
        recordingTime: 0
      })
      // 开始计时
      this.startRecordingTimer()
    })

    this.recorderManager.onStop((res) => {
      clearInterval(this.recordingTimer)
      this.setData({ 
        isRecording: false,
        recordingTime: 0
      })
      
      if (!this.showCancelTip && res.duration > 1000) {
        this.sendVoiceMessage(res.tempFilePath, res.duration)
      }
    })

    this.recorderManager.onError((error) => {
      console.error('录音错误:', error)
      wx.showToast({
        title: '录音失败',
        icon: 'none'
      })
      this.setData({ isRecording: false })
    })
  },

  // 初始化音频播放器
  initAudioContext() {
    this.innerAudioContext = wx.createInnerAudioContext()
    
    this.innerAudioContext.onEnded(() => {
      this.setData({ playingVoiceId: '' })
    })

    this.innerAudioContext.onError((error) => {
      console.error('音频播放错误:', error)
      this.setData({ playingVoiceId: '' })
    })
  },

  // 设置用户信息
  setUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userAvatar: userInfo.avatarUrl
      })
    }
  },

  // 初始化会话
  async initConversation(conversationId) {
    if (conversationId) {
      // 加载已有会话
      this.setData({ currentConversationId: conversationId })
      await this.loadHistoryMessages()
    } else {
      // 创建新会话
      const newConversationId = `conv_${Date.now()}`
      this.setData({ currentConversationId: newConversationId })
    }
  },

  // 加载历史消息
  async loadHistoryMessages() {
    if (!this.data.hasMoreHistory || this.data.isLoadingHistory) return

    this.setData({ isLoadingHistory: true })

    try {
      // 从本地存储加载历史消息
      const historyKey = `chat_history_${this.data.currentConversationId}`
      const history = wx.getStorageSync(historyKey) || []
      
      const start = this.data.messages.length
      const end = start + this.data.pageSize
      const messages = history.slice(start, end)

      if (messages.length > 0) {
        this.setData({
          messages: [...messages, ...this.data.messages],
          hasMoreHistory: history.length > end
        })
      } else {
        this.setData({ hasMoreHistory: false })
      }
    } catch (error) {
      console.error('加载历史消息失败:', error)
      wx.showToast({
        title: '加载历史消息失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoadingHistory: false })
    }
  },

  // 保存消息到历史记录
  saveMessageToHistory(message) {
    try {
      const historyKey = `chat_history_${this.data.currentConversationId}`
      const history = wx.getStorageSync(historyKey) || []
      history.push(message)
      wx.setStorageSync(historyKey, history)
    } catch (error) {
      console.error('保存消息失败:', error)
    }
  },

  // 发送文本消息
  async sendTextMessage(content) {
    if (!content.trim()) return

    const message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      type: 'text',
      content: content.trim(),
      time: this.formatTime(new Date()),
      status: 'sending'
    }

    this.setData({
      messages: [...this.data.messages, message],
      inputValue: ''
    })
    this.scrollToBottom()
    this.saveMessageToHistory(message)

    // 发送到服务器并等待回复
    await this.sendToServer(message)
  },

  // 发送语音消息
  async sendVoiceMessage(tempFilePath, duration) {
    const message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      type: 'voice',
      content: tempFilePath,
      duration: Math.round(duration / 1000),
      time: this.formatTime(new Date()),
      status: 'sending'
    }

    this.setData({
      messages: [...this.data.messages, message],
    })
    this.scrollToBottom()
    this.saveMessageToHistory(message)

    // TODO: 上传语音文件到服务器
    await this.sendToServer(message)
  },

  // 发送图片消息
  async sendImageMessage(tempFilePath) {
    const message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      type: 'image',
      content: tempFilePath,
      time: this.formatTime(new Date()),
      status: 'sending'
    }

    this.setData({
      messages: [...this.data.messages, message],
    })
    this.scrollToBottom()
    this.saveMessageToHistory(message)

    // TODO: 上传图片到服务器
    await this.sendToServer(message)
  },

  // 发送到服务器
  async sendToServer(message) {
    try {
      // 更新消息状态为已发送
      this.updateMessageStatus(message.id, 'sent')
      
      // 显示AI正在输入状态
      this.setData({ isTyping: true })
      
      // TODO: 调用实际的API
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟AI回复
      const reply = {
        id: `msg_${Date.now()}`,
        role: 'assistant',
        type: 'text',
        content: '这是一条AI助手的回复消息，实际开发时需要替换为真实的API调用。',
        time: this.formatTime(new Date()),
        status: 'sent'
      }

      this.setData({
        isTyping: false,
        messages: [...this.data.messages, reply]
      })
      this.scrollToBottom()
      this.saveMessageToHistory(reply)

    } catch (error) {
      console.error('发送消息失败:', error)
      this.updateMessageStatus(message.id, 'error')
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      })
    }
  },

  // 更新消息状态
  updateMessageStatus(messageId, status) {
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, status }
      }
      return msg
    })
    this.setData({ messages })
  },

  // 输入框事件处理
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  onInputFocus() {
    this.setData({
      isInputFocused: true,
      showMoreActions: false
    })
    this.scrollToBottom()
  },

  onInputBlur() {
    this.setData({
      isInputFocused: false
    })
  },

  // 发送消息
  sendMessage() {
    if (this.data.inputValue) {
      this.sendTextMessage(this.data.inputValue)
    }
  },

  // 语音相关方法
  toggleVoiceInput() {
    this.setData({
      isVoiceInputMode: !this.data.isVoiceInputMode,
      showMoreActions: false
    })
  },

  startRecording() {
    this.setData({ 
      startY: 0,
      showCancelTip: false
    })
    
    // 检查录音权限
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        this.recorderManager.start({
          duration: 60000, // 最长录音时间，单位ms
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 48000,
          format: 'mp3'
        })
      },
      fail: () => {
        wx.showToast({
          title: '请授权录音权限',
          icon: 'none'
        })
      }
    })
  },

  stopRecording() {
    this.recorderManager.stop()
  },

  onTouchMove(e) {
    if (!this.data.startY) {
      this.setData({ startY: e.touches[0].clientY })
      return
    }

    const moveY = e.touches[0].clientY
    const diffY = this.data.startY - moveY

    // 上滑超过50显示取消发送提示
    this.setData({
      showCancelTip: diffY > 50
    })
  },

  // 开始录音计时
  startRecordingTimer() {
    this.recordingTimer = setInterval(() => {
      this.setData({
        recordingTime: this.data.recordingTime + 1
      })
    }, 1000)
  },

  // 语音播放控制
  playVoice(e) {
    const voiceId = e.currentTarget.dataset.voiceId
    const message = this.data.messages.find(msg => msg.id === voiceId)
    
    if (!message) return

    if (this.data.playingVoiceId === voiceId) {
      // 停止播放
      this.stopVoicePlay()
    } else {
      // 播放新的语音
      this.stopVoicePlay()
      this.innerAudioContext.src = message.content
      this.innerAudioContext.play()
      this.setData({ playingVoiceId: voiceId })
    }
  },

  stopVoicePlay() {
    if (this.data.playingVoiceId) {
      this.innerAudioContext.stop()
      this.setData({ playingVoiceId: '' })
    }
  },

  // 更多操作面板
  showMoreActions() {
    this.setData({
      showMoreActions: true,
      isInputFocused: false
    })
  },

  hideMoreActions() {
    this.setData({
      showMoreActions: false
    })
  },

  onMoreActionsVisibleChange(e) {
    if (!e.detail.visible) {
      this.hideMoreActions()
    }
  },

  // 选择图片
  async chooseImage() {
    try {
      const res = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera']
      })
      
      if (res.tempFiles.length > 0) {
        this.sendImageMessage(res.tempFiles[0].tempFilePath)
      }
      
      this.hideMoreActions()
    } catch (error) {
      console.error('选择图片失败:', error)
    }
  },

  // 选择文件
  chooseFile() {
    // 微信小程序暂不支持选择文件
    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    })
    this.hideMoreActions()
  },

  // 清空对话
  clearConversation() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前对话吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ messages: [] })
          // 清除本地存储
          const historyKey = `chat_history_${this.data.currentConversationId}`
          wx.removeStorageSync(historyKey)
        }
      }
    })
    this.hideMoreActions()
  },

  // 消息操作
  copyMessage(e) {
    const content = e.currentTarget.dataset.content
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  likeMessage(e) {
    const messageId = e.currentTarget.dataset.id
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { 
          ...msg, 
          liked: !msg.liked,
          disliked: false 
        }
      }
      return msg
    })
    this.setData({ messages })
  },

  dislikeMessage(e) {
    const messageId = e.currentTarget.dataset.id
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { 
          ...msg, 
          disliked: !msg.disliked,
          liked: false 
        }
      }
      return msg
    })
    this.setData({ messages })
  },

  // 图片预览
  previewImage(e) {
    const current = e.currentTarget.dataset.src
    const urls = this.data.messages
      .filter(msg => msg.type === 'image')
      .map(msg => msg.content)
    
    wx.previewImage({
      current,
      urls
    })
  },

  // 滚动相关
  onScrollToUpper() {
    this.loadHistoryMessages()
  },

  scrollToBottom() {
    this.setData({
      scrollToMessage: `msg_${Date.now()}`
    })
  },

  // 工具方法
  formatTime(date) {
    const hour = date.getHours()
    const minute = date.getMinutes()
    return `${hour}:${minute < 10 ? '0' + minute : minute}`
  }
})