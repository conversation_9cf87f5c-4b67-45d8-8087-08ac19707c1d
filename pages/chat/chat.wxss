/* pages/chat/chat.wxss */

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

/* 聊天列表区域 */
.chat-list {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
}

/* 历史加载提示 */
.history-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-text {
  font-size: 24rpx;
  color: var(--text-color-light);
  margin-left: 10rpx;
}

/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.message-user {
  flex-direction: row-reverse;
}

/* 头像 */
.avatar-container {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

/* 消息内容 */
.message-content {
  max-width: 70%;
  margin: 0 20rpx;
  display: flex;
  flex-direction: column;
}

.message-user .message-content {
  align-items: flex-end;
}

.message-assistant .message-content {
  align-items: flex-start;
}

/* 消息气泡 */
.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 18rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
  position: relative;
}

.message-user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-top-right-radius: 4rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.message-assistant .message-bubble {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  color: var(--text-color);
  border-top-left-radius: 4rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 图片消息 */
.image-message {
  padding: 10rpx;
  background-color: transparent;
  box-shadow: none;
}

.message-image {
  max-width: 100%;
  border-radius: 12rpx;
}

/* 语音消息 */
.voice-message {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  max-width: 400rpx;
}

.voice-icon {
  margin-right: 10rpx;
}

.voice-icon.playing {
  animation: pulse 1s infinite;
}

.voice-duration {
  font-size: 24rpx;
}

/* 文件消息 */
.file-message {
  display: flex;
  align-items: center;
}

.file-name {
  margin-left: 10rpx;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 消息状态 */
.message-status {
  margin-top: 10rpx;
  display: flex;
  justify-content: center;
}

.message-status.error {
  color: #e54d42;
}

/* 消息时间 */
.message-time {
  font-size: 22rpx;
  color: var(--text-color-placeholder);
  margin-top: 10rpx;
}

/* 消息操作菜单 */
.message-actions {
  position: absolute;
  right: 100%;
  top: 0;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 10rpx;
  margin-right: 20rpx;
  opacity: 0;
  transform: translateX(20rpx);
  transition: all 0.2s ease;
}

.message-user .message-actions {
  right: auto;
  left: 100%;
  margin-right: 0;
  margin-left: 20rpx;
  transform: translateX(-20rpx);
}

.message-item:hover .message-actions {
  opacity: 1;
  transform: translateX(0);
}

.action-item {
  padding: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 正在输入提示 */
.typing-indicator {
  padding: 10rpx 0;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: var(--text-color-light);
  margin: 0 6rpx;
  animation: bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* 底部占位 */
.bottom-space {
  height: 20rpx;
}

/* 输入区域 */
.input-area {
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.input-focused {
  padding-bottom: 20rpx;
}

/* 功能按钮区 */
.function-bar {
  display: flex;
  margin-bottom: 20rpx;
}

.function-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f8f8f8;
}

/* 文本输入容器 */
.text-input-container {
  display: flex;
  align-items: center;
}

.message-input {
  flex: 1;
  background-color: #f8f8f8;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
  font-size: 28rpx;
}

.input-actions {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.action-button {
  margin-right: 20rpx;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.send-button:active {
  transform: scale(0.95);
}

/* 语音输入容器 */
.voice-input-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.voice-button {
  width: 100%;
  height: 100rpx;
  background-color: #f8f8f8;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-color);
  min-height: 100rpx;
}

.voice-button.recording {
  background-color: rgba(18, 150, 219, 0.1);
  color: var(--primary-color);
}

/* 更多操作弹出层 */
.more-actions {
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 40rpx 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.action-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #f8f8f8;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.action-name {
  font-size: 24rpx;
  color: var(--text-color);
}

.cancel-button {
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: var(--text-color);
  border-top: 1rpx solid var(--border-color);
}

/* 录音取消提示 */
.recording-cancel-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 100;
}

.recording-cancel-tip.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.recording-cancel-tip text {
  color: #fff;
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 录音状态指示器 */
.recording-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 100;
}

.recording-indicator.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.recording-waves {
  display: flex;
  align-items: center;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.wave {
  width: 8rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  margin: 0 6rpx;
  border-radius: 4rpx;
  animation: wave 1.2s infinite ease-in-out;
}

.recording-time {
  color: #fff;
  font-size: 28rpx;
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes wave {
  0%, 100% {
    height: 30rpx;
  }
  50% {
    height: 80rpx;
  }
}

/* 适配暗黑模式 */
page[data-theme="dark"] .chat-container {
  background-color: #1a1a1a;
}

page[data-theme="dark"] .message-assistant .message-bubble {
  background-color: #2a2a2a;
  color: #f0f0f0;
}

page[data-theme="dark"] .input-area {
  background-color: #1a1a1a;
  border-top-color: #333;
}

page[data-theme="dark"] .function-button,
page[data-theme="dark"] .message-input,
page[data-theme="dark"] .voice-button,
page[data-theme="dark"] .action-icon {
  background-color: #2a2a2a;
}

page[data-theme="dark"] .more-actions {
  background-color: #1a1a1a;
}

page[data-theme="dark"] .cancel-button {
  border-top-color: #333;
}