<!-- pages/chat/chat.wxml -->
<view class="chat-container">
  <!-- 聊天消息列表 -->
  <scroll-view 
    class="chat-list" 
    scroll-y="{{true}}" 
    scroll-into-view="{{scrollToMessage}}"
    scroll-with-animation="{{true}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    bindscrolltoupper="onScrollToUpper"
  >
    <!-- 历史加载提示 -->
    <view class="history-loading" wx:if="{{isLoadingHistory}}">
      <t-loading theme="circular" size="40rpx" />
      <text class="loading-text">加载历史消息...</text>
    </view>

    <!-- 消息列表 -->
    <block wx:for="{{messages}}" wx:key="id">
      <view id="msg-{{item.id}}" class="message-item {{item.role === 'user' ? 'message-user' : 'message-assistant'}}">
        <!-- 头像 -->
        <view class="avatar-container">
          <image 
            class="avatar" 
            src="{{item.role === 'user' ? userAvatar : assistantAvatar}}"
          ></image>
        </view>

        <!-- 消息内容 -->
        <view class="message-content">
          <!-- 文本消息 -->
          <block wx:if="{{item.type === 'text'}}">
            <view class="message-bubble">
              <text user-select="{{true}}">{{item.content}}</text>
            </view>
          </block>

          <!-- 图片消息 -->
          <block wx:elif="{{item.type === 'image'}}">
            <view class="message-bubble image-message">
              <image 
                src="{{item.content}}" 
                mode="widthFix" 
                class="message-image"
                bindtap="previewImage"
                data-src="{{item.content}}"
              ></image>
            </view>
          </block>

          <!-- 语音消息 -->
          <block wx:elif="{{item.type === 'voice'}}">
            <view class="message-bubble voice-message" bindtap="playVoice" data-voice-id="{{item.id}}">
              <view class="voice-icon {{playingVoiceId === item.id ? 'playing' : ''}}">
                <t-icon name="{{item.role === 'user' ? 'sound' : 'sound'}}" size="40rpx" />
              </view>
              <text class="voice-duration">{{item.duration}}''</text>
            </view>
          </block>

          <!-- 文件消息 -->
          <block wx:elif="{{item.type === 'file'}}">
            <view class="message-bubble file-message" bindtap="openFile" data-file="{{item.content}}">
              <t-icon name="file" size="40rpx" />
              <text class="file-name">{{item.fileName}}</text>
            </view>
          </block>

          <!-- 加载中状态 -->
          <view class="message-status" wx:if="{{item.status === 'sending'}}">
            <t-loading theme="dots" size="36rpx" />
          </view>

          <!-- 错误状态 -->
          <view class="message-status error" wx:elif="{{item.status === 'error'}}">
            <t-icon name="error-circle" size="36rpx" color="#e54d42" />
          </view>

          <!-- 消息时间 -->
          <view class="message-time">{{item.time}}</view>
        </view>

        <!-- 消息操作菜单 -->
        <view class="message-actions" wx:if="{{item.role === 'assistant'}}">
          <view class="action-item" bindtap="copyMessage" data-content="{{item.content}}">
            <t-icon name="file-copy" size="32rpx" />
          </view>
          <view class="action-item" bindtap="likeMessage" data-id="{{item.id}}">
            <t-icon name="{{item.liked ? 'thumb-up-filled' : 'thumb-up'}}" size="32rpx" />
          </view>
          <view class="action-item" bindtap="dislikeMessage" data-id="{{item.id}}">
            <t-icon name="{{item.disliked ? 'thumb-down-filled' : 'thumb-down'}}" size="32rpx" />
          </view>
        </view>
      </view>
    </block>

    <!-- 正在输入提示 -->
    <view class="typing-indicator" wx:if="{{isTyping}}">
      <view class="message-item message-assistant">
        <view class="avatar-container">
          <image class="avatar" src="{{assistantAvatar}}"></image>
        </view>
        <view class="message-content">
          <view class="message-bubble">
            <view class="typing-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位，确保滚动到底部时内容完全可见 -->
    <view class="bottom-space" id="bottom-anchor"></view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-area {{isInputFocused ? 'input-focused' : ''}}">
    <!-- 功能按钮区 -->
    <view class="function-bar">
      <view class="function-button" bindtap="toggleVoiceInput">
        <t-icon name="{{isVoiceInputMode ? 'keyboard' : 'mic'}}" size="48rpx" />
      </view>
    </view>

    <!-- 文本输入模式 -->
    <view class="text-input-container" wx:if="{{!isVoiceInputMode}}">
      <t-input
        class="message-input"
        value="{{inputValue}}"
        placeholder="输入消息..."
        confirm-type="send"
        cursor-spacing="20"
        adjust-position="{{false}}"
        bindfocus="onInputFocus"
        bindblur="onInputBlur"
        bindinput="onInput"
        bindconfirm="sendMessage"
        maxlength="{{2000}}"
        borderless
      />
      <view class="input-actions">
        <view class="action-button" bindtap="showMoreActions">
          <t-icon name="add-circle" size="48rpx" />
        </view>
        <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage">
          <t-icon name="send" size="48rpx" />
        </view>
      </view>
    </view>

    <!-- 语音输入模式 -->
    <view class="voice-input-container" wx:else>
      <view 
        class="voice-button {{isRecording ? 'recording' : ''}}" 
        bindtouchstart="startRecording" 
        bindtouchend="stopRecording"
        bindtouchmove="onTouchMove"
      >
        {{isRecording ? '松开发送' : '按住说话'}}
      </view>
    </view>
  </view>

  <!-- 更多操作弹出层 -->
  <t-popup visible="{{showMoreActions}}" placement="bottom" bind:visible-change="onMoreActionsVisibleChange">
    <view class="more-actions">
      <view class="action-grid">
        <view class="action-item" bindtap="chooseImage">
          <view class="action-icon">
            <t-icon name="image" size="56rpx" />
          </view>
          <text class="action-name">图片</text>
        </view>
        <view class="action-item" bindtap="chooseFile">
          <view class="action-icon">
            <t-icon name="file" size="56rpx" />
          </view>
          <text class="action-name">文件</text>
        </view>
        <view class="action-item" bindtap="clearConversation">
          <view class="action-icon">
            <t-icon name="delete" size="56rpx" />
          </view>
          <text class="action-name">清空对话</text>
        </view>
      </view>
      <view class="cancel-button" bindtap="hideMoreActions">取消</view>
    </view>
  </t-popup>

  <!-- 录音取消提示 -->
  <view class="recording-cancel-tip {{showCancelTip ? 'show' : ''}}" wx:if="{{isRecording}}">
    <t-icon name="close-circle" size="64rpx" color="#e54d42" />
    <text>松开手指，取消发送</text>
  </view>

  <!-- 录音状态指示器 -->
  <view class="recording-indicator {{isRecording && !showCancelTip ? 'show' : ''}}">
    <view class="recording-waves">
      <view class="wave" wx:for="{{5}}" wx:key="index" style="animation-delay: {{index * 0.1}}s"></view>
    </view>
    <text class="recording-time">{{recordingTime}}s</text>
  </view>
</view>