Page({
  data: {
    taskData: {
      type: 'expense',
      category: '生活用品',
      amount: '120.00',
      date: '今天',
      merchant: '全食超市'
    },
    categoryIcon: 'shop',
    selectedCategory: 'groceries',
    showCategoryPicker: false,
    categoryOptions: [
      {
        label: '生活用品',
        value: 'groceries',
        icon: 'shop',
        color: '#52c41a'
      },
      {
        label: '餐饮',
        value: 'dining',
        icon: 'food',
        color: '#fa8c16'
      },
      {
        label: '交通',
        value: 'transport',
        icon: 'car',
        color: '#1890ff'
      },
      {
        label: '娱乐',
        value: 'entertainment',
        icon: 'play-circle',
        color: '#722ed1'
      },
      {
        label: '购物',
        value: 'shopping',
        icon: 'shopping-bag',
        color: '#eb2f96'
      },
      {
        label: '生活费用',
        value: 'utilities',
        icon: 'lightning',
        color: '#faad14'
      }
    ]
  },

  onLoad(options) {
    if (options.data) {
      try {
        const taskData = JSON.parse(decodeURIComponent(options.data));
        this.setData({
          taskData: {
            ...this.data.taskData,
            ...taskData
          }
        });
        
        // 设置对应的分类图标
        this.updateCategoryIcon();
      } catch (e) {
        console.error('解析任务数据失败:', e);
      }
    }
  },

  // 关闭模态框
  closeModal() {
    wx.navigateBack({
      delta: 2 // 返回到首页
    });
  },

  // 更新分类图标
  updateCategoryIcon() {
    const category = this.data.categoryOptions.find(
      cat => cat.label === this.data.taskData.category
    );
    if (category) {
      this.setData({
        categoryIcon: category.icon,
        selectedCategory: category.value
      });
    }
  },

  // 金额输入
  onAmountInput(e) {
    this.setData({
      'taskData.amount': e.detail.value
    });
  },

  // 商户输入
  onMerchantInput(e) {
    const field = this.data.taskData.type === 'expense' || this.data.taskData.type === 'income' 
      ? 'merchant' : 'description';
    this.setData({
      [`taskData.${field}`]: e.detail.value
    });
  },

  // 备注输入
  onNoteInput(e) {
    this.setData({
      'taskData.note': e.detail.value
    });
  },

  // 选择分类
  selectCategory() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 关闭分类选择器
  closeCategoryPicker() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 分类弹窗状态改变
  onCategoryPopupChange(e) {
    this.setData({
      showCategoryPicker: e.detail.visible
    });
  },

  // 选择分类
  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      'taskData.category': category.label,
      categoryIcon: category.icon,
      selectedCategory: category.value,
      showCategoryPicker: false
    });
  },

  // 选择日期
  selectDate() {
    // 这里可以打开日期选择器
    wx.showToast({
      title: '日期选择功能开发中',
      icon: 'none'
    });
  },

  // 选择时间
  selectTime() {
    // 这里可以打开时间选择器
    wx.showToast({
      title: '时间选择功能开发中',
      icon: 'none'
    });
  },

  // 编辑任务
  editTask() {
    wx.navigateBack();
  },

  // 确认任务
  confirmTask() {
    const { taskData } = this.data;
    
    // 验证必填字段
    if (taskData.type === 'expense' || taskData.type === 'income') {
      if (!taskData.amount) {
        wx.showToast({
          title: '请输入金额',
          icon: 'none'
        });
        return;
      }
    }

    // 保存任务数据
    this.saveTaskData(taskData);
  },

  // 保存任务数据
  saveTaskData(taskData) {
    console.log('保存任务:', taskData);
    
    // 🎯 构造日程数据
    const scheduleToSave = {
      id: Date.now(), // 使用时间戳作为ID
      title: taskData.description || taskData.text || '新日程',
      time: taskData.time || '09:00',
      description: taskData.note || '',
      date: this.parseDate(taskData.date), // 解析日期
      type: taskData.type || 'schedule',
      priority: this.mapPriorityToStandard(taskData.priority),
      urgency: taskData.urgency,
      createdAt: new Date().toISOString(),
      source: 'manual'
    };
    
    console.log('🎯 处理后的日程:', scheduleToSave);
    
    // 🎯 保存到本地存储
    try {
      // 获取现有的日程数据
      const existingSchedules = wx.getStorageSync('allSchedules') || {};
      
      // 按日期分组存储
      const dateKey = scheduleToSave.date;
      if (!existingSchedules[dateKey]) {
        existingSchedules[dateKey] = [];
      }
      
      // 添加新日程
      existingSchedules[dateKey].push(scheduleToSave);
      
      // 保存到本地存储
      wx.setStorageSync('allSchedules', existingSchedules);
      
      console.log('✅ 日程已保存到本地存储:', dateKey, scheduleToSave);
      
      wx.hideLoading();
      wx.showToast({
        title: '日程保存成功',
        icon: 'success'
      });

      // 🎯 返回首页并刷新数据
      setTimeout(() => {
        // 通过全局事件通知首页刷新
        getApp().globalData.shouldRefreshSchedules = true;
        
        wx.navigateBack({
          delta: 2
        });
      }, 1500);
      
    } catch (error) {
      console.error('保存日程失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'error'
      });
    }
  },

  // 🎯 解析日期字符串
  parseDate(dateInput) {
    const today = new Date();
    
    if (!dateInput || dateInput === '今天') {
      return this.formatDate(today);
    }
    
    if (dateInput === '明天') {
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      return this.formatDate(tomorrow);
    }
    
    // 如果是具体日期格式，直接返回
    if (dateInput.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateInput;
    }
    
    // 默认返回今天
    return this.formatDate(today);
  },

  // 🎯 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 🎯 映射优先级到标准格式
  mapPriorityToStandard(priority) {
    // 这里需要根据实际需求实现映射逻辑
    return priority || '普通';
  }
})