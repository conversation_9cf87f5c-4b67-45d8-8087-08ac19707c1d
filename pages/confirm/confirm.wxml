<!--confirm.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <t-icon name="close" size="24" color="#333" bind:tap="closeModal" />
    <text class="title">确认任务</text>
    <view class="placeholder"></view>
  </view>

  <!-- 确认内容 -->
  <view class="confirm-content">
    <!-- 优先级选择 -->
    <view class="form-item">
      <text class="form-label">优先级</text>
      <t-cell 
        title="{{taskData.priority || '选择优先级'}}"
        arrow
        hover
        bind:click="selectCategory"
        class="form-cell"
      >
        <view slot="left-icon" class="category-icon">
          <t-icon name="{{categoryIcon}}" size="20" color="#1890ff" />
        </view>
      </t-cell>
    </view>

    <!-- 紧急程度选择 -->
    <view class="form-item">
      <text class="form-label">紧急程度</text>
      <t-cell 
        title="{{taskData.urgency || '选择紧急程度'}}"
        arrow
        hover
        bind:click="selectUrgency"
        class="form-cell"
      >
        <view slot="left-icon" class="urgency-icon">
          <t-icon name="time" size="20" color="#fa8c16" />
        </view>
      </t-cell>
    </view>

    <!-- 日期选择 -->
    <view class="form-item">
      <text class="form-label">截止日期</text>
      <t-cell 
        title="{{taskData.date || '今天'}}"
        arrow
        hover
        bind:click="selectDate"
        class="form-cell"
      />
    </view>

    <!-- 任务描述 -->
    <view class="form-item">
      <text class="form-label">任务描述</text>
      <t-input
        value="{{taskData.description}}"
        placeholder="输入任务描述"
        bind:input="onMerchantInput"
        class="form-input"
      />
    </view>

    <!-- 时间选择（可选） -->
    <view class="form-item">
      <text class="form-label">提醒时间</text>
      <t-cell 
        title="{{taskData.time || '选择时间'}}"
        arrow
        hover
        bind:click="selectTime"
        class="form-cell"
      />
    </view>

    <!-- 备注 -->
    <view class="form-item">
      <text class="form-label">备注</text>
      <t-textarea
        value="{{taskData.note}}"
        placeholder="添加备注信息（可选）"
        bind:input="onNoteInput"
        maxlength="100"
        class="form-textarea"
      />
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <t-button 
      theme="default" 
      size="large" 
      bind:tap="editTask"
      class="edit-btn"
    >
      编辑
    </t-button>
    
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="confirmTask"
      class="confirm-btn"
    >
      确认
    </t-button>

    <!-- 新增保存按钮 -->
    <t-button 
      theme="success" 
      size="large" 
      bind:tap="saveTask"
      class="save-btn"
    >
      保存
    </t-button>
  </view>

  <!-- 优先级选择器弹窗 -->
  <t-popup 
    visible="{{showCategoryPicker}}" 
    bind:visible-change="onCategoryPopupChange"
    placement="bottom"
  >
    <view class="category-picker">
      <view class="picker-header">
        <text class="picker-title">选择优先级</text>
        <t-icon name="close" size="20" bind:tap="closeCategoryPicker" />
      </view>
      <view class="category-list">
        <view 
          class="category-item {{item.value === selectedPriority ? 'selected' : ''}}"
          wx:for="{{categoryOptions}}" 
          wx:key="value"
          bind:tap="onCategorySelect"
          data-category="{{item}}"
        >
          <t-icon name="{{item.icon}}" size="20" color="{{item.color}}" />
          <view class="category-info">
            <text class="category-name">{{item.label}}</text>
            <text class="category-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 紧急程度选择器弹窗 -->
  <t-popup 
    visible="{{showUrgencyPicker}}" 
    bind:visible-change="onUrgencyPopupChange"
    placement="bottom"
  >
    <view class="category-picker">
      <view class="picker-header">
        <text class="picker-title">选择紧急程度</text>
        <t-icon name="close" size="20" bind:tap="closeUrgencyPicker" />
      </view>
      <view class="category-list">
        <view 
          class="category-item {{item.value === selectedUrgency ? 'selected' : ''}}"
          wx:for="{{urgencyOptions}}" 
          wx:key="value"
          bind:tap="onUrgencySelect"
          data-urgency="{{item}}"
        >
          <t-icon name="{{item.icon}}" size="20" color="{{item.color}}" />
          <view class="category-info">
            <text class="category-name">{{item.label}}</text>
            <text class="category-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </t-popup>
</view>