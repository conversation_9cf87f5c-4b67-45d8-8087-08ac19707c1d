/* pages/confirm/confirm.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx; /* 增加底部空间以容纳三个按钮 */
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 48rpx;
}

/* 确认内容 */
.confirm-content {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.form-cell {
  background: white;
  border-radius: 12rpx;
}

.form-input {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.form-textarea {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 120rpx;
}

.category-icon, .urgency-icon {
  margin-right: 16rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx 48rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

.bottom-actions .t-button {
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.edit-btn {
  order: 3; /* 编辑按钮放在最后 */
}

.confirm-btn {
  order: 1; /* 确认按钮放在第一个 */
}

.save-btn {
  order: 2; /* 保存按钮放在中间 */
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  border: none !important;
  color: white !important;
}

.save-btn:active {
  background: linear-gradient(135deg, #389e0d 0%, #237804 100%) !important;
}

/* 分类选择器 */
.category-picker {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 70vh;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-list {
  padding: 16rpx 0;
  max-height: 500rpx;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 24rpx;
  transition: background-color 0.2s;
}

.category-item:active {
  background-color: #f5f5f5;
}

.category-item.selected {
  background-color: #e6f7ff;
  border-left: 4rpx solid #1890ff;
}

.category-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.category-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.category-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}