/* pages/tasks/tasks.wxss */
.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部导航 - 现代卡片式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin: 16rpx 24rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
  letter-spacing: -0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* 统计信息卡片 */
.stats-container {
  display: flex;
  justify-content: space-around;
  margin: 24rpx 24rpx 16rpx;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 500;
}

/* 视图切换器 - 胶囊式设计 */
.view-switcher {
  display: flex;
  gap: 8rpx;
  padding: 8rpx;
  margin: 24rpx 24rpx 16rpx;
  background: transparent;
  border-radius: 24rpx;
}

/* 四象限视图 - 增强层次感 */
.quadrant-view {
  margin: 0 24rpx;
}

.quadrant-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  height: 65vh;
  min-height: 600rpx;
}

.quadrant {
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid transparent;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  max-height: 100%; /* 确保不超出网格高度 */
  position: relative; /* 支持滚动指示器 */
}

.quadrant:active {
  transform: scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.quadrant-header {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* 四象限整体背景 */
.urgent-important {
  background: linear-gradient(135deg, #fecaca 0%, #fbb6ce 100%);
}

.important-not-urgent {
  background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%);
}

.urgent-not-important {
  background: linear-gradient(135deg, #fed7aa 0%, #fbbf24 100%);
}

.not-important-not-urgent {
  background: linear-gradient(135deg, #ddd6fe 0%, #c084fc 100%);
}

.quadrant-title {
  font-size: 26rpx;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: -0.3rpx;
  text-shadow: 0 1rpx 4rpx rgba(255, 255, 255, 0.5);
}

.task-count {
  font-size: 22rpx;
  color: #374151;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.7);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 滚动条样式优化 - 兼容微信小程序 */
.task-scroll::-webkit-scrollbar {
  width: 6rpx;
}

.task-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
}

.task-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6rpx;
}

.task-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 微信小程序滚动增强 */
.task-scroll {
  flex: 1;
  padding: 16rpx 20rpx;
  height: 0; /* 强制高度约束，配合flex布局 */
  min-height: 200rpx;
  max-height: calc(68vh - 100rpx); /* 减去header高度 */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  scroll-behavior: smooth; /* 平滑滚动 */
  /* 微信小程序强制显示滚动条 */
  overflow-y: scroll;
}

/* 任务项 */
.task-item {
  margin-bottom: 6rpx;
  padding: 10rpx 14rpx;
  border-radius: 10rpx;
  background: transparent;
  backdrop-filter: none;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 96rpx;
}

.task-item:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.task-item.completed {
  opacity: 0.7;
  background: transparent;
}

/* 任务内容 */
.task-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  min-width: 0;
}

/* 任务勾选框 */
.task-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent;
}

.task-checkbox.unchecked {
  border: 3rpx solid #d1d5db;
  background: transparent;
  box-shadow: none;
}

.task-checkbox.unchecked:active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  transform: scale(0.9);
}

.task-checkbox.checked {
  border: 3rpx solid #10b981;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.4);
}

.task-checkbox.checked:active {
  transform: scale(0.9);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}

.checkbox-text {
  font-size: 28rpx;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  pointer-events: none;
  user-select: none;
}

.task-checkbox.checked .checkbox-text {
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 任务文本容器 */
.task-text-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.task-text {
  font-size: 24rpx;
  color: #1f2937;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
  transition: all 0.3s ease;
}

.task-title {
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
  transition: all 0.3s ease;
}

.completed-text {
  text-decoration: line-through;
  color: #9ca3af !important;
  opacity: 0.8;
}

/* 任务元信息 */
.task-meta {
  display: flex;
  gap: 8rpx;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 4rpx;
}

.task-due-date {
  font-size: 20rpx;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.6);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.task-priority {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.priority-high {
  background: rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.priority-low {
  background: rgba(107, 114, 128, 0.2);
  color: #4b5563;
}

.task-description {
  font-size: 20rpx;
  color: #6b7280;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.6);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

/* 任务操作按钮 */
.task-actions {
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-left: 8rpx;
}

.quick-edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.quick-edit-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.9);
}

.edit-icon {
  font-size: 24rpx;
}

/* 菜单按钮 */
.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}

.menu-button:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.menu-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 列表视图 */
.list-view {
  margin: 0 24rpx;
}

.task-list {
  background: transparent;
  border-radius: 16rpx;
  backdrop-filter: none;
  overflow: hidden;
}

.task-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: none;
  transition: background-color 0.2s ease;
  background: transparent;
}

.task-list-item:last-child {
  border-bottom: none;
}

.task-list-item:active {
  background: rgba(255, 255, 255, 0.1);
}

.task-list-item.completed {
  opacity: 0.7;
}

.task-left {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  flex: 1;
  min-width: 0;
}

.task-title.completed-text {
  text-decoration: line-through;
  color: #6b7280;
}

.task-status {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.7;
}

.empty-title {
  font-size: 32rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 浮动按钮 */
.fab-container {
  position: fixed;
  bottom: 45rpx;
  right: 32rpx;
  z-index: 100;
  filter: drop-shadow(0 12rpx 32rpx rgba(102, 126, 234, 0.3));
}

/* 自定义FAB按钮 */
.custom-fab {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: all 0.2s ease;
  cursor: pointer;
}

.custom-fab:active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: scale(0.95);
  box-shadow: 0 6rpx 20rpx rgba(90, 103, 216, 0.4);
}

.plus-icon {
  position: relative;
  width: 32rpx;
  height: 32rpx;
}

.plus-vertical {
  position: absolute;
  width: 4rpx;
  height: 32rpx;
  background: white;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  border-radius: 2rpx;
}

.plus-horizontal {
  position: absolute;
  width: 32rpx;
  height: 4rpx;
  background: white;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 2rpx;
}

/* 按钮样式 */
.view-switcher .t-button {
  background: rgba(255, 255, 255, 0.7) !important;
  border: 1rpx solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 18rpx !important;
  color: #6b7280 !important;
  font-weight: 500 !important;
  font-size: 26rpx !important;
  transition: all 0.2s ease !important;
  backdrop-filter: blur(8rpx) !important;
}

.view-switcher .t-button:active {
  transform: scale(0.95) !important;
}

.view-switcher .t-button--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 700 !important;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4) !important;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2) !important;
}

/* 列表视图中的勾选框 */
.task-list-item .task-checkbox {
  margin-top: 4rpx;
  margin-right: 12rpx;
}

/* 任务文本容器 - 列表视图 */
.task-list-item .task-text-container {
  flex: 1;
  min-width: 0;
}

/* 响应式滚动优化 */
@media (max-height: 800rpx) {
  .quadrant-grid {
    height: 60vh;
    min-height: 500rpx;
  }
  
  .task-scroll {
    max-height: calc(60vh - 80rpx);
  }
}

@media (max-height: 600rpx) {
  .quadrant-grid {
    height: 55vh;
    min-height: 400rpx;
  }
  
  .task-scroll {
    max-height: calc(55vh - 60rpx);
    min-height: 150rpx;
  }
}

/* 滚动阴影提示 - 当有更多内容时显示 */
.quadrant::before {
  content: '';
  position: absolute;
  right: 0;
  top: 60rpx;
  bottom: 0;
  width: 2rpx;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 50%, transparent 100%);
  z-index: 1;
  pointer-events: none;
}

