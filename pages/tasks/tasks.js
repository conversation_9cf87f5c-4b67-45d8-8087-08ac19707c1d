import dataApi from '../../utils/dataApi.js';

Page({
  data: {
    viewMode: 'quadrant', // 'quadrant' 或 'list'
    quadrants: {
      urgentImportant: [],     // 紧急且重要
      importantNotUrgent: [],  // 重要但不紧急
      urgentNotImportant: [],  // 紧急但不重要
      notImportantNotUrgent: [] // 既不紧急也不重要
    },
    allTasks: [],
    stats: {
      total: 0,
      completed: 0,
      pending: 0,
      overdue: 0
    },
    loading: false,
    isDataLoaded: false,
    filter: {
      status: 'all', // all, pending, completed, overdue
      priority: 'all' // all, high, medium, low
    },
    lastTapTime: 0,
    lastTappedTaskId: null
  },

  async onLoad() {
    console.log('=== 待办任务页面 onLoad ===');
    
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    
    // 初始化数据API
    await this.initDataApi();
    
    // 检查云开发状态
    this.checkCloudStatus();
    
    // 加载待办任务数据
    await this.loadTasksData();
  },

  async onShow() {
    console.log('=== 待办任务页面 onShow ===');
    
    // 检查登录状态
    const app = getApp();
    if (!app.requireLogin()) {
      return;
    }
    
    // 刷新数据
    await this.refreshTasksData();
  },

  // 初始化数据API
  async initDataApi() {
    try {
      const success = await dataApi.init();
      if (success) {
        console.log('数据API初始化成功');
      } else {
        console.warn('数据API初始化失败，将使用降级方案');
      }
    } catch (error) {
      console.error('数据API初始化出错:', error);
    }
  },

  // 加载待办任务数据
  async loadTasksData() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      console.log('开始加载待办任务数据...');
      
      // 获取所有待办任务
      const todos = await dataApi.getTodos({ limit: 200 });
      console.log('获取到的待办数据:', todos);
      
      // 处理和分类任务
      const processedData = this.processTasksData(todos);
      
      this.setData({
        ...processedData,
        loading: false,
        isDataLoaded: true
      });
      
      console.log(`待办数据加载完成，共${todos.length}个任务`);
      
    } catch (error) {
      console.error('加载待办数据失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 处理和分类任务数据
  processTasksData(todos) {
    const quadrants = {
      urgentImportant: [],
      importantNotUrgent: [],
      urgentNotImportant: [],
      notImportantNotUrgent: []
    };
    
    const allTasks = [];
    const stats = {
      total: todos.length,
      completed: 0,
      pending: 0,
      overdue: 0
    };
    
    todos.forEach(todo => {
      const task = this.formatTask(todo);
      allTasks.push(task);
      
      // 统计状态
      if (task.status === 'completed') {
        stats.completed++;
      } else if (task.isOverdue) {
        stats.overdue++;
      } else {
        stats.pending++;
      }
      
      // 按四象限分类
      const category = this.categorizeTask(task);
      quadrants[category].push(task);
    });
    
    // 按优先级和截止时间排序
    Object.keys(quadrants).forEach(key => {
      quadrants[key].sort((a, b) => {
        // 先按状态排序（未完成的在前）
        if (a.status !== b.status) {
          return a.status === 'completed' ? 1 : -1;
        }
        // 再按优先级排序
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    });
    
    // 检查即将到期的任务并显示提醒
    this.checkUpcomingTasks(allTasks);
    
    return {
      quadrants,
      allTasks,
      stats
    };
  },

  // 检查即将到期的任务
  checkUpcomingTasks(tasks) {
    const now = new Date();
    const oneHour = 60 * 60 * 1000;
    const twentyFourHours = 24 * 60 * 60 * 1000;
    
    const urgentTasks = tasks.filter(task => {
      if (task.status === 'completed' || !task.dueDate) return false;
      
      const timeToDeadline = task.dueDate.getTime() - now.getTime();
      return timeToDeadline > 0 && timeToDeadline <= twentyFourHours;
    });
    
    if (urgentTasks.length > 0) {
      // 按紧急程度排序
      urgentTasks.sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());
      
      // 显示提醒
      const nextTask = urgentTasks[0];
      const timeToDeadline = nextTask.dueDate.getTime() - now.getTime();
      
      let reminderText = '';
      if (timeToDeadline <= oneHour) {
        reminderText = `⚠️ 紧急提醒\n"${nextTask.title}"将在1小时内到期！`;
      } else {
        const hoursLeft = Math.ceil(timeToDeadline / oneHour);
        reminderText = `⏰ 任务提醒\n"${nextTask.title}"将在${hoursLeft}小时后到期`;
      }
      
      if (urgentTasks.length > 1) {
        reminderText += `\n\n📋 还有${urgentTasks.length - 1}个任务即将到期`;
      }
      
      // 使用轻量级提示，不打断用户操作
      this.showSmartReminder(reminderText, urgentTasks);
    }
  },

  // 显示智能提醒
  showSmartReminder(message, urgentTasks) {
    // 检查是否已经在本次会话中显示过提醒
    const app = getApp();
    const lastReminderTime = app.globalData.lastReminderTime || 0;
    const now = Date.now();
    
    // 如果距离上次提醒不到30分钟，则不重复提醒
    if (now - lastReminderTime < 30 * 60 * 1000) {
      return;
    }
    
    // 记录提醒时间
    app.globalData.lastReminderTime = now;
    
    // 显示模态框
    wx.showModal({
      title: '智能提醒',
      content: message,
      confirmText: '查看任务',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 跳转到第一个紧急任务的编辑页面
          const firstTask = urgentTasks[0];
          wx.navigateTo({
            url: `/pages/task-edit/task-edit?taskId=${firstTask.id}`
          });
        }
      }
    });
  },

  // 格式化任务数据
  formatTask(todo) {
    const now = new Date();
    const dueDate = todo.dueDate ? new Date(todo.dueDate) : null;
    const isOverdue = dueDate && dueDate < now && todo.status !== 'completed';
    
    return {
      id: todo.id,
      title: todo.title,
      description: todo.description || '',
      priority: todo.priority || 'medium',
      status: todo.status || 'pending',
      dueDate: dueDate,
      dueDateStr: dueDate ? this.formatDueDate(dueDate) : '',
      isOverdue: isOverdue,
      createdAt: todo.createdAt,
      updatedAt: todo.updatedAt,
      source: todo.source || 'manual',
      originalData: todo
    };
  },

  // 任务四象限分类
  categorizeTask(task) {
    const isUrgent = this.isUrgentTask(task);
    const isImportant = this.isImportantTask(task);
    
    if (isUrgent && isImportant) {
      return 'urgentImportant';
    } else if (isImportant && !isUrgent) {
      return 'importantNotUrgent';
    } else if (isUrgent && !isImportant) {
      return 'urgentNotImportant';
    } else {
      return 'notImportantNotUrgent';
    }
  },

  // 判断任务是否紧急
  isUrgentTask(task) {
    if (task.isOverdue) return true;
    if (task.priority === 'high') return true;
    
    if (task.dueDate) {
      const now = new Date();
      const timeDiff = task.dueDate - now;
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
      
      // 3天内到期的任务视为紧急
      return daysDiff <= 3;
    }
    
    return false;
  },

  // 判断任务是否重要
  isImportantTask(task) {
    // 高优先级任务视为重要
    if (task.priority === 'high') return true;
    
    // 中等优先级且有截止时间的任务视为重要
    if (task.priority === 'medium' && task.dueDate) return true;
    
    // 手动标记的重要任务（可以扩展）
    const importantKeywords = ['重要', '关键', '核心', '必须', '紧急'];
    return importantKeywords.some(keyword => 
      task.title.includes(keyword) || task.description.includes(keyword)
    );
  },

  // 格式化截止时间
  formatDueDate(date) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    const timeDiff = taskDate - today;
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
    
    if (daysDiff === 0) {
      return '今天';
    } else if (daysDiff === 1) {
      return '明天';
    } else if (daysDiff === -1) {
      return '昨天';
    } else if (daysDiff > 1 && daysDiff <= 7) {
      return `${Math.floor(daysDiff)}天后`;
    } else if (daysDiff < -1 && daysDiff >= -7) {
      return `${Math.abs(Math.floor(daysDiff))}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  // 刷新任务数据
  async refreshTasksData() {
    await this.loadTasksData();
  },

  // 切换视图模式
  switchToQuadrant() {
    this.setData({ viewMode: 'quadrant' });
  },

  switchToList() {
    this.setData({ viewMode: 'list' });
  },

  // 任务点击事件
  onTaskClick(e) {
    const task = e.currentTarget.dataset.task;
    console.log('点击任务:', task.title);
    
    // 检查是否是双击
    const now = Date.now();
    const lastTapTime = this.data.lastTapTime || 0;
    const tapGap = now - lastTapTime;
    
    this.setData({ lastTapTime: now });
    
    // 双击快速完成/恢复任务（500ms内的连续点击视为双击）
    if (tapGap < 500 && this.data.lastTappedTaskId === task.id) {
      console.log('检测到双击，快速切换任务状态');
      
      // 触觉反馈
      wx.vibrateShort({ type: 'medium' });
      
      // 快速切换状态
      this.toggleTaskStatus(task);
      
      // 清除双击记录
      this.setData({ 
        lastTappedTaskId: null,
        lastTapTime: 0 
      });
      
      return;
    }
    
    // 记录本次点击的任务ID
    this.setData({ lastTappedTaskId: task.id });
    
    // 延迟执行单击操作，给双击留出时间
    setTimeout(() => {
      const currentTime = this.data.lastTapTime;
      if (currentTime === now) {
        // 确认是单击，显示任务详情
        this.showTaskDetail(task);
      }
    }, 300);
  },

  // 勾选框点击事件
  async onTaskCheckbox(e) {
    console.log('🔥 勾选框被点击！');
    
    // 阻止事件冒泡（兼容微信小程序）
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    const task = e.currentTarget.dataset.task;
    
    if (!task) {
      console.log('测试点击，无任务数据');
      wx.showToast({
        title: '测试点击成功！',
        icon: 'success'
      });
      return;
    }
    
    console.log('🎯 处理任务:', task.title, '状态:', task.status);
    
    try {
      // 触觉反馈
      wx.vibrateShort({ type: 'light' });
      
      // 切换状态
      await this.toggleTaskStatus(task);
      
    } catch (error) {
      console.error('勾选操作失败:', error);
      wx.showModal({
        title: '操作失败',
        content: `切换状态失败: ${error.message}`,
        showCancel: false
      });
    }
  },

  // 显示任务详情
  showTaskDetail(task) {
    const statusText = {
      pending: '待完成',
      completed: '已完成',
      cancelled: '已取消'
    };
    
    const priorityText = {
      high: '高',
      medium: '中',
      low: '低'
    };
    
    let content = `状态: ${statusText[task.status] || '未知'}\n`;
    content += `优先级: ${priorityText[task.priority] || '未知'}\n`;
    if (task.dueDateStr) {
      content += `截止时间: ${task.dueDateStr}\n`;
    }
    if (task.description) {
      content += `描述: ${task.description}\n`;
    }
    content += `创建时间: ${new Date(task.createdAt).toLocaleString()}\n\n`;
    content += `💡 提示：使用任务右侧的操作按钮可以编辑、完成或删除任务`;
    
    wx.showModal({
      title: task.title,
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 切换任务状态
  async toggleTaskStatus(task) {
    try {
      console.log('=== 🎯 开始切换任务状态 ===');
      console.log('📝 原任务:', {
        id: task.id,
        title: task.title,
        status: task.status,
        priority: task.priority
      });
      
      const newStatus = task.status === 'completed' ? 'pending' : 'completed';
      console.log('🔄 新状态:', newStatus);
      
      // 立即更新本地状态，提供即时反馈
      const { allTasks } = this.data;
      const updatedAllTasks = allTasks.map(t => {
        if (t.id === task.id) {
          return { ...t, status: newStatus, updatedAt: new Date().toISOString() };
        }
        return t;
      });
      
      // 重新处理数据并更新UI
      const processedData = this.processTasksData(updatedAllTasks.map(t => ({
        id: t.id,
        title: t.title,
        description: t.description,
        priority: t.priority,
        status: t.status,
        dueDate: t.dueDate ? (typeof t.dueDate === 'string' ? t.dueDate : t.dueDate.toISOString()) : null,
        createdAt: t.createdAt,
        updatedAt: t.updatedAt,
        source: t.source
      })));
      
      // 立即更新UI
      this.setData(processedData);
      console.log('✅ 本地状态已更新');
      
      // 显示即时反馈
      wx.showToast({
        title: newStatus === 'completed' ? '✓ 已完成' : '↺ 重新开始',
        icon: 'success',
        duration: 1000
      });
      
      // 尝试同步到云端（异步，不影响本地状态）
      try {
        console.log('☁️ 开始云端同步...');
        const updateData = {
          status: newStatus,
          updatedAt: new Date().toISOString()
        };
        
        const result = await dataApi.updateTodo(task.id, updateData);
        console.log('☁️ 云端同步结果:', result);
        
        if (result.success) {
          console.log('✅ 云端同步成功');
        } else {
          console.log('⚠️ 云端同步失败，但本地状态已保存:', result.error);
        }
      } catch (syncError) {
        console.log('⚠️ 云端同步异常，但本地状态已保存:', syncError.message);
        // 云端同步失败不影响本地功能
      }
      
    } catch (error) {
      console.error('❌ 切换状态失败:', error);
      
      wx.showToast({
        title: '操作失败',
        icon: 'error',
        duration: 2000
      });
      
      throw error;
    }
  },

  // 编辑任务
  editTask(e) {
    // 阻止事件冒泡（兼容微信小程序）
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    // 处理两种情况：事件对象 或 直接传递的任务对象
    let task;
    if (e && e.currentTarget && e.currentTarget.dataset) {
      // 事件对象情况
      task = e.currentTarget.dataset.task;
    } else {
      // 直接传递任务对象的情况
      task = e;
    }
    
    if (!task) {
      console.error('编辑任务失败：未找到任务数据');
      return;
    }
    
    console.log('编辑任务:', task.title);
    
    // 跳转到任务编辑页面
    wx.navigateTo({
      url: `/pages/task-edit/task-edit?taskId=${task.id}`
    });
  },

  // 添加任务
  addTask() {
    console.log('添加新任务');
    
    // 跳转到任务编辑页面（新建模式）
    wx.navigateTo({
      url: '/pages/task-edit/task-edit'
    });
  },

  // 创建任务
  async createTask(title) {
    try {
      wx.showLoading({ title: '创建中...' });
      
      const todoData = {
        title: title,
        description: '',
        priority: 'medium',
        status: 'pending',
        source: 'manual'
      };
      
      const result = await dataApi.createTodo(todoData);
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        await this.refreshTasksData();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('创建任务失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  // 显示筛选菜单
  showFilter() {
    wx.showActionSheet({
      itemList: ['显示全部', '仅显示待完成', '仅显示已完成', '仅显示逾期', '按优先级筛选'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.setFilter('status', 'all');
            break;
          case 1:
            this.setFilter('status', 'pending');
            break;
          case 2:
            this.setFilter('status', 'completed');
            break;
          case 3:
            this.setFilter('status', 'overdue');
            break;
          case 4:
            this.showPriorityFilter();
            break;
        }
      }
    });
  },

  // 显示优先级筛选
  showPriorityFilter() {
    wx.showActionSheet({
      itemList: ['所有优先级', '仅高优先级', '仅中优先级', '仅低优先级'],
      success: (res) => {
        const priorities = ['all', 'high', 'medium', 'low'];
        this.setFilter('priority', priorities[res.tapIndex]);
      }
    });
  },

  // 设置筛选条件
  setFilter(type, value) {
    const filter = { ...this.data.filter };
    filter[type] = value;
    
    this.setData({ filter });
    this.applyFilter();
  },

  // 应用筛选条件
  applyFilter() {
    // 重新处理数据以应用筛选
    const { allTasks } = this.data;
    const filteredTasks = this.filterTasks(allTasks);
    const processedData = this.processTasksData(filteredTasks.map(t => t.originalData));
    
    this.setData(processedData);
  },

  // 筛选任务
  filterTasks(tasks) {
    const { filter } = this.data;
    
    return tasks.filter(task => {
      // 状态筛选
      if (filter.status !== 'all') {
        if (filter.status === 'overdue' && !task.isOverdue) return false;
        if (filter.status !== 'overdue' && task.status !== filter.status) return false;
      }
      
      // 优先级筛选
      if (filter.priority !== 'all' && task.priority !== filter.priority) {
        return false;
      }
      
      return true;
    });
  },

  // 删除任务
  async deleteTask(e) {
    // 阻止事件冒泡（兼容微信小程序）
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    const task = e.currentTarget.dataset.task;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除任务"${task.title}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await dataApi.deleteTodo(task.id);
            
            if (result.success) {
              wx.showToast({
                title: '已删除',
                icon: 'success'
              });
              
              await this.refreshTasksData();
            } else {
              throw new Error(result.error);
            }
          } catch (error) {
            console.error('删除任务失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 显示主菜单（整合筛选和更多操作）
  showMainMenu() {
    console.log('=== showMainMenu 被调用 ===');
    
    wx.showActionSheet({
      itemList: [
        '🔍 筛选任务',
        '📊 数据统计', 
        '📤 导出任务',
        '️ 清理已完成',
        '☁️ 云开发诊断',
        '⚙️ 更多功能'
      ],
      success: async (res) => {
        try {
          switch (res.tapIndex) {
            case 0:
              this.showFilter();
              break;
            case 1:
              this.showStats();
              break;
            case 2:
              this.exportTasks();
              break;
            case 3:
              this.clearCompleted();
              break;
            case 4:
              await this.cloudDiagnostic();
              break;
            case 5:
              this.showMoreFeatures();
              break;
          }
        } catch (error) {
          console.error('菜单操作出错:', error);
          wx.showToast({
            title: '操作失败',
            icon: 'error'
          });
        }
      },
      fail: (error) => {
        console.error('显示菜单失败:', error);
      }
    });
  },

  // 显示更多功能菜单
  showMoreFeatures() {
    wx.showActionSheet({
      itemList: [
        '🔧 批量操作',
        '📖 使用指南',
        '🧪 测试勾选功能',
        '🔧 调试数据',
        '📋 检查任务数据',
        '⚡ 重新加载数据'
      ],
      success: async (res) => {
        try {
          switch (res.tapIndex) {
            case 0:
              this.showBatchActions();
              break;
            case 1:
              this.showUserGuide();
              break;
            case 2:
              await this.testCheckboxFunction();
              break;
            case 3:
              await this.debugTasksData();
              break;
            case 4:
              await this.checkTasksData();
              break;
            case 5:
              await this.refreshTasksData();
              wx.showToast({
                title: '数据已刷新',
                icon: 'success'
              });
              break;
          }
        } catch (error) {
          console.error('更多功能操作出错:', error);
          wx.showToast({
            title: '操作失败',
            icon: 'error'
          });
        }
      }
    });
  },

  // 显示统计信息
  showStats() {
    const { stats } = this.data;
    const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;
    
    const content = [
      `总任务数: ${stats.total}`,
      `已完成: ${stats.completed}`,
      `待完成: ${stats.pending}`,
      `已逾期: ${stats.overdue}`,
      `完成率: ${completionRate}%`
    ].join('\n');
    
    wx.showModal({
      title: '任务统计',
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 导出任务
  exportTasks() {
    const { allTasks } = this.data;
    const exportText = allTasks.map(task => {
      return `${task.title} - ${task.status} - ${task.priority} - ${task.dueDateStr || '无截止时间'}`;
    }).join('\n');
    
    wx.setClipboardData({
      data: exportText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 清理已完成任务
  async clearCompleted() {
    const completedTasks = this.data.allTasks.filter(task => task.status === 'completed');
    
    if (completedTasks.length === 0) {
      wx.showToast({
        title: '没有已完成的任务',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '清理已完成任务',
      content: `确定要删除${completedTasks.length}个已完成的任务吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '清理中...' });
            
            for (const task of completedTasks) {
              await dataApi.deleteTodo(task.id);
            }
            
            wx.hideLoading();
            wx.showToast({
              title: '清理完成',
              icon: 'success'
            });
            
            await this.refreshTasksData();
          } catch (error) {
            wx.hideLoading();
            console.error('清理失败:', error);
            wx.showToast({
              title: '清理失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 显示云端状态警告
  showCloudStatusWarning() {
    // 检查用户是否选择了不再提示
    const hideWarning = wx.getStorageSync('hide_cloud_warning');
    if (hideWarning) {
      console.log('用户选择不再显示云端状态警告');
      return;
    }
    
    wx.showModal({
      title: '云端同步不可用',
      content: '检测到云端同步服务暂时不可用，但您仍可以正常使用本地功能。数据将在云端服务恢复后自动同步。\n\n这不会影响您的日常使用。',
      showCancel: true,
      cancelText: '不再提示',
      confirmText: '我知道了',
      success: (res) => {
        if (res.cancel) {
          // 用户选择不再提示，记录偏好
          wx.setStorageSync('hide_cloud_warning', true);
          console.log('用户选择不再显示云端状态警告');
        }
      }
    });
  },

  // 云开发诊断
  async cloudDiagnostic() {
    wx.showLoading({ title: '诊断中...' });
    
    try {
      console.log('=== 云开发诊断开始 ===');
      
      // 1. 检查基础库版本
      const systemInfo = wx.getSystemInfoSync();
      const sdkVersion = systemInfo.SDKVersion;
      
      // 2. 检查云开发初始化状态
      let cloudInitStatus = '未知';
      try {
        if (wx.cloud) {
          cloudInitStatus = '已初始化';
        } else {
          cloudInitStatus = '未初始化';
        }
      } catch (e) {
        cloudInitStatus = '不支持';
      }
      
      // 3. 测试云函数调用
      let cloudFunctionStatus = '测试中...';
      try {
        await wx.cloud.callFunction({
          name: 'userLogin', // 测试一个存在的云函数
          data: { test: true }
        });
        cloudFunctionStatus = '连接正常';
      } catch (error) {
        if (error.errMsg && error.errMsg.includes('env check invalid')) {
          cloudFunctionStatus = '环境配置错误';
        } else if (error.errMsg && error.errMsg.includes('function not found')) {
          cloudFunctionStatus = '云函数未部署';
        } else {
          cloudFunctionStatus = `连接失败: ${error.errMsg || error.message}`;
        }
      }
      
      // 4. 检查同步状态
      const syncStatus = dataApi.getSyncStatus();
      
      // 5. 检查本地数据
      const localTaskCount = this.data.stats.total || 0;
      
      wx.hideLoading();
      
      // 生成诊断报告
      const diagnosticReport = [
        '=== 云开发诊断报告 ===',
        '',
        `基础库版本: ${sdkVersion}`,
        `云开发状态: ${cloudInitStatus}`,
        `云函数测试: ${cloudFunctionStatus}`,
        `同步状态: ${syncStatus.isOnline ? '在线' : '离线'}`,
        `本地任务数: ${localTaskCount}`,
        `同步队列: ${syncStatus.pendingCount || 0} 项待同步`,
        '',
        '=== 建议修复方案 ===',
        ''
      ];
      
      // 根据诊断结果提供建议
      if (cloudFunctionStatus.includes('环境配置错误')) {
        diagnosticReport.push(
          '🔧 环境配置问题:',
          '1. 检查app.js中的环境ID配置',
          '2. 或注释env参数使用默认环境',
          '3. 参考README.md中的配置指南',
          ''
        );
      }
      
      if (cloudFunctionStatus.includes('云函数未部署')) {
        diagnosticReport.push(
          '📁 云函数问题:',
          '1. 右键cloudfunctions文件夹',
          '2. 选择"上传并部署：云端安装依赖"',
          '3. 等待所有函数部署完成',
          ''
        );
      }
      
      if (!syncStatus.isOnline) {
        diagnosticReport.push(
          '🌐 同步问题:',
          '1. 检查网络连接',
          '2. 重启小程序尝试',
          '3. 本地功能不受影响',
          ''
        );
      }
      
      diagnosticReport.push(
        '💡 临时解决方案:',
        '• 所有功能都可以正常使用',
        '• 数据安全保存在本地',
        '• 云端恢复后会自动同步',
        '• 可在"不再提示"中关闭警告'
      );
      
      wx.showModal({
        title: '云开发诊断',
        content: diagnosticReport.join('\n'),
        showCancel: true,
        cancelText: '重置警告',
        confirmText: '我知道了',
        success: (res) => {
          if (res.cancel) {
            // 重置云端警告设置
            wx.removeStorageSync('hide_cloud_warning');
            wx.showToast({
              title: '已重置警告设置',
              icon: 'success'
            });
          }
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('云开发诊断失败:', error);
      
      wx.showModal({
        title: '诊断失败',
        content: `诊断过程中出错: ${error.message}\n\n但这不影响应用正常使用。`,
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  },

  // 显示使用指南
  showUserGuide() {
    const guideContent = `📝 任务管理指南\n\n` +
      `✨ 基本操作：\n` +
      `• 点击 + 按钮：创建新任务\n` +
      `• 点击勾选框：完成/取消完成任务\n` +
      `• 双击任务：快速切换完成状态\n` +
      `• 长按任务：显示操作菜单\n\n` +
      `🔧 编辑操作：\n` +
      `• 使用任务右侧的编辑按钮来修改任务\n` +
      `• 编辑页面可以修改标题、描述、优先级等\n` +
      `• 可以设置截止时间和任务状态\n\n` +
      `🗂️ 视图切换：\n` +
      `• 四象限视图：按重要性和紧急程度分类\n` +
      `• 列表视图：所有任务的简洁列表\n\n` +
      `💡 智能提醒：\n` +
      `• 自动检测即将到期的任务\n` +
      `• 24小时内到期的任务会收到提醒\n` +
      `• 1小时内到期会显示紧急提醒\n\n` +
      `💾 数据安全：\n` +
      `• 数据自动保存到本地\n` +
      `• 云端同步确保数据安全\n` +
      `• 支持离线使用`;
    
    wx.showModal({
      title: '使用指南',
      content: guideContent,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 显示任务操作菜单
  showTaskActions(e) {
    // 阻止事件冒泡
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    const task = e.currentTarget.dataset.task;
    
    if (!task) {
      console.error('显示任务操作失败：未找到任务数据');
      return;
    }
    
    // 根据任务状态定制菜单项
    const menuItems = ['📝 编辑任务', '🗑️ 删除任务'];
    
    // 如果任务未完成，添加快速完成选项
    if (task.status !== 'completed') {
      menuItems.unshift('✅ 标记完成');
    } else {
      menuItems.unshift('↺ 重新开始');
    }
    
    wx.showActionSheet({
      itemList: menuItems,
      success: async (res) => {
        try {
          switch (res.tapIndex) {
            case 0:
              // 切换完成状态
              await this.toggleTaskStatus(task);
              break;
            case 1:
              // 编辑任务
              this.editTask(task);
              break;
            case 2:
              // 删除任务
              this.deleteTaskWithConfirm(task);
              break;
          }
        } catch (error) {
          console.error('任务操作失败:', error);
          wx.showToast({
            title: '操作失败',
            icon: 'error'
          });
        }
      }
    });
  },

  // 确认删除任务
  deleteTaskWithConfirm(task) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除任务"${task.title}"吗？`,
      confirmColor: '#ff4d4f',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' });
            
            const result = await dataApi.deleteTodo(task.id);
            
            wx.hideLoading();
            
            if (result.success) {
              wx.showToast({
                title: '🗑️ 已删除',
                icon: 'success'
              });
              
              await this.refreshTasksData();
            } else {
              throw new Error(result.error);
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除任务失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 批量操作
  showBatchActions() {
    wx.showToast({
      title: '批量操作功能开发中',
      icon: 'none'
    });
  },

  // 调试任务数据
  async debugTasksData() {
    wx.showLoading({ title: '分析中...' });
    
    try {
      const todos = await dataApi.getTodos({ limit: 100 });
      
      const debugInfo = [
        '=== 待办任务数据调试 ===',
        '',
        `总任务数: ${todos.length}`,
        `数据API状态: ${dataApi.getSyncStatus().isOnline ? '在线' : '离线'}`,
        '',
        '任务分布:',
        `- 待完成: ${todos.filter(t => t.status === 'pending').length}`,
        `- 已完成: ${todos.filter(t => t.status === 'completed').length}`,
        `- 其他状态: ${todos.filter(t => t.status !== 'pending' && t.status !== 'completed').length}`,
        '',
        '优先级分布:',
        `- 高优先级: ${todos.filter(t => t.priority === 'high').length}`,
        `- 中优先级: ${todos.filter(t => t.priority === 'medium').length}`,
        `- 低优先级: ${todos.filter(t => t.priority === 'low').length}`,
        '',
        '最近5个任务:',
        ...todos.slice(0, 5).map((todo, index) => 
          `${index + 1}. ${todo.title} (${todo.status}, ${todo.priority})`
        )
      ].join('\n');
      
      wx.hideLoading();
      
      wx.showModal({
        title: '调试信息',
        content: debugInfo,
        showCancel: false,
        confirmText: '我知道了'
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('调试失败:', error);
      wx.showToast({
        title: '调试失败',
        icon: 'error'
      });
    }
  },

  // 测试勾选功能
  async testCheckboxFunction() {
    try {
      wx.showLoading({ title: '测试中...' });
      
      // 1. 获取所有任务
      const allTasks = this.data.allTasks;
      console.log('=== 勾选功能测试 ===');
      console.log('当前任务数量:', allTasks.length);
      
      if (allTasks.length === 0) {
        wx.hideLoading();
        wx.showModal({
          title: '测试失败',
          content: '没有任务可以测试，请先创建一些待办任务',
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }
      
      // 2. 找到第一个待完成的任务
      const pendingTask = allTasks.find(task => task.status === 'pending');
      
      if (!pendingTask) {
        wx.hideLoading();
        wx.showModal({
          title: '测试失败',
          content: '没有待完成的任务可以测试，请先创建一些待办任务',
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }
      
      console.log('测试任务:', pendingTask);
      
      // 3. 模拟勾选框点击
      console.log('开始模拟勾选操作...');
      await this.toggleTaskStatus(pendingTask);
      
      wx.hideLoading();
      
      // 4. 显示测试结果
      wx.showModal({
        title: '测试完成',
        content: `已将任务"${pendingTask.title}"标记为已完成。请检查界面是否正确更新了状态。`,
        showCancel: false,
        confirmText: '我知道了'
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('测试勾选功能失败:', error);
      
      wx.showModal({
        title: '测试失败',
        content: `测试过程中出错: ${error.message}`,
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  },

  // 检查任务数据
  async checkTasksData() {
    wx.showLoading({ title: '检查中...' });
    
    try {
      const todos = await dataApi.getTodos({ limit: 100 });
      
      const checkInfo = [
        '=== 待办任务数据检查 ===',
        '',
        `总任务数: ${todos.length}`,
        `数据API状态: ${dataApi.getSyncStatus().isOnline ? '在线' : '离线'}`,
        '',
        '任务分布:',
        `- 待完成: ${todos.filter(t => t.status === 'pending').length}`,
        `- 已完成: ${todos.filter(t => t.status === 'completed').length}`,
        `- 其他状态: ${todos.filter(t => t.status !== 'pending' && t.status !== 'completed').length}`,
        '',
        '优先级分布:',
        `- 高优先级: ${todos.filter(t => t.priority === 'high').length}`,
        `- 中优先级: ${todos.filter(t => t.priority === 'medium').length}`,
        `- 低优先级: ${todos.filter(t => t.priority === 'low').length}`,
        '',
        '最近5个任务:',
        ...todos.slice(0, 5).map((todo, index) => 
          `${index + 1}. ${todo.title} (${todo.status}, ${todo.priority})`
        )
      ].join('\n');
      
      wx.hideLoading();
      
      wx.showModal({
        title: '检查信息',
        content: checkInfo,
        showCancel: false,
        confirmText: '我知道了'
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('检查失败:', error);
      wx.showToast({
        title: '检查失败',
        icon: 'error'
      });
    }
  },

  // 检查云开发状态
  checkCloudStatus() {
    try {
      console.log('检查云开发状态...');
      
      // 获取同步状态
      const syncStatus = dataApi.getSyncStatus();
      console.log('当前同步状态:', syncStatus);
      
      // 如果检测到离线状态，可能是环境问题
      if (!syncStatus.isOnline) {
        console.warn('检测到云端同步不可用');
        
        // 延迟显示提示，避免影响页面加载
        setTimeout(() => {
          this.showCloudStatusWarning();
        }, 2000);
      }
      
    } catch (error) {
      console.error('检查云开发状态失败:', error);
    }
  }
});