<!--tasks.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <text class="title">待办任务</text>
    <view class="header-actions">
      <view class="menu-button" catchtap="showMainMenu">
        <text class="menu-icon">⋯</text>
      </view>
    </view>
  </view>

  <!-- 数据加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <t-loading size="40rpx" loading="{{loading}}" text="加载中..." />
  </view>

  <!-- 统计信息 -->
  <view class="stats-container" wx:if="{{!loading && isDataLoaded}}">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总任务</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.pending}}</text>
      <text class="stat-label">待完成</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.completed}}</text>
      <text class="stat-label">已完成</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.overdue}}</text>
      <text class="stat-label">已逾期</text>
    </view>
  </view>

  <!-- 视图切换 -->
  <view class="view-switcher" wx:if="{{!loading && isDataLoaded}}">
    <t-button 
      theme="{{viewMode === 'quadrant' ? 'primary' : 'default'}}" 
      size="small"
      bind:tap="switchToQuadrant"
    >
      四象限视图
    </t-button>
    <t-button 
      theme="{{viewMode === 'list' ? 'primary' : 'default'}}" 
      size="small"
      bind:tap="switchToList"
    >
      列表视图
    </t-button>
  </view>

  <!-- 四象限视图 -->
  <view class="quadrant-view" wx:if="{{!loading && viewMode === 'quadrant'}}">
    <view class="quadrant-grid">
      
      <!-- 重要且紧急 -->
      <view class="quadrant urgent-important">
        <view class="quadrant-header">
          <text class="quadrant-title">重要且紧急</text>
          <text class="task-count">{{quadrants.urgentImportant.length}}</text>
        </view>
        <scroll-view class="task-scroll" scroll-y="true" enable-flex="true" scroll-top="{{urgentImportantScrollTop}}" bindscroll="onQuadrantScroll" data-quadrant="urgentImportant">
          <view 
            class="task-item urgent-important-item {{item.status === 'completed' ? 'completed' : ''}}"
            wx:for="{{quadrants.urgentImportant}}" 
            wx:key="id"
            bind:tap="onTaskClick"
            bind:longpress="showTaskActions"
            data-task="{{item}}"
          >
            <view class="task-content">
              <!-- 勾选框 -->
              <view 
                class="task-checkbox {{item.status === 'completed' ? 'checked' : 'unchecked'}}" 
                catchtap="onTaskCheckbox" 
                data-task="{{item}}"
              >
                <text class="checkbox-text" wx:if="{{item.status === 'completed'}}">✓</text>
                <text class="checkbox-text" wx:else>☐</text>
              </view>
              <view class="task-text-container">
                <text class="task-text {{item.status === 'completed' ? 'completed-text' : ''}}">{{item.title}}</text>
                <view class="task-meta" wx:if="{{item.dueDateStr || item.priority !== 'medium'}}">
                  <text class="task-due-date" wx:if="{{item.dueDateStr}}">📅 {{item.dueDateStr}}</text>
                  <text class="task-priority priority-{{item.priority}}" wx:if="{{item.priority !== 'medium'}}">
                    {{item.priority === 'high' ? '🔥' : '📌'}} {{item.priority === 'high' ? '高' : '低'}}
                  </text>
                </view>
              </view>
            </view>
            <view class="task-actions">
              <view class="quick-edit-btn" catchtap="editTask" data-task="{{item}}">
                <text class="edit-icon">✏️</text>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 滚动控制按钮 -->
        <view class="scroll-controls" wx:if="{{quadrants.urgentImportant.length > 3}}">
          <view class="scroll-btn scroll-top-btn" catchtap="scrollToTop" data-quadrant="urgentImportant">
            <text class="scroll-icon">↑</text>
          </view>
          <view class="scroll-btn scroll-bottom-btn" catchtap="scrollToBottom" data-quadrant="urgentImportant">
            <text class="scroll-icon">↓</text>
          </view>
        </view>
      </view>

      <!-- 重要不紧急 -->
      <view class="quadrant important-not-urgent">
        <view class="quadrant-header">
          <text class="quadrant-title">重要不紧急</text>
          <text class="task-count">{{quadrants.importantNotUrgent.length}}</text>
        </view>
        <scroll-view class="task-scroll" scroll-y="true" enable-flex="true" scroll-top="{{importantNotUrgentScrollTop}}" bindscroll="onQuadrantScroll" data-quadrant="importantNotUrgent">
          <view 
            class="task-item important-not-urgent-item {{item.status === 'completed' ? 'completed' : ''}}"
            wx:for="{{quadrants.importantNotUrgent}}" 
            wx:key="id"
            bind:tap="onTaskClick"
            bind:longpress="showTaskActions"
            data-task="{{item}}"
          >
            <view class="task-content">
              <!-- 勾选框 -->
              <view 
                class="task-checkbox {{item.status === 'completed' ? 'checked' : 'unchecked'}}" 
                catchtap="onTaskCheckbox" 
                data-task="{{item}}"
              >
                <text class="checkbox-text" wx:if="{{item.status === 'completed'}}">✓</text>
                <text class="checkbox-text" wx:else>☐</text>
              </view>
              <view class="task-text-container">
                <text class="task-text {{item.status === 'completed' ? 'completed-text' : ''}}">{{item.title}}</text>
                <view class="task-meta" wx:if="{{item.dueDateStr || item.priority !== 'medium'}}">
                  <text class="task-due-date" wx:if="{{item.dueDateStr}}">📅 {{item.dueDateStr}}</text>
                  <text class="task-priority priority-{{item.priority}}" wx:if="{{item.priority !== 'medium'}}">
                    {{item.priority === 'high' ? '🔥' : '📌'}} {{item.priority === 'high' ? '高' : '低'}}
                  </text>
                </view>
              </view>
            </view>
            <view class="task-actions">
              <view class="quick-edit-btn" catchtap="editTask" data-task="{{item}}">
                <text class="edit-icon">✏️</text>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 滚动控制按钮 -->
        <view class="scroll-controls" wx:if="{{quadrants.importantNotUrgent.length > 3}}">
          <view class="scroll-btn scroll-top-btn" catchtap="scrollToTop" data-quadrant="importantNotUrgent">
            <text class="scroll-icon">↑</text>
          </view>
          <view class="scroll-btn scroll-bottom-btn" catchtap="scrollToBottom" data-quadrant="importantNotUrgent">
            <text class="scroll-icon">↓</text>
          </view>
        </view>
      </view>

      <!-- 紧急不重要 -->
      <view class="quadrant urgent-not-important">
        <view class="quadrant-header">
          <text class="quadrant-title">紧急不重要</text>
          <text class="task-count">{{quadrants.urgentNotImportant.length}}</text>
        </view>
        <scroll-view class="task-scroll" scroll-y="true" enable-flex="true" scroll-top="{{urgentNotImportantScrollTop}}" bindscroll="onQuadrantScroll" data-quadrant="urgentNotImportant">
          <view 
            class="task-item urgent-not-important-item {{item.status === 'completed' ? 'completed' : ''}}"
            wx:for="{{quadrants.urgentNotImportant}}" 
            wx:key="id"
            bind:tap="onTaskClick"
            bind:longpress="showTaskActions"
            data-task="{{item}}"
          >
            <view class="task-content">
              <!-- 勾选框 -->
              <view 
                class="task-checkbox {{item.status === 'completed' ? 'checked' : 'unchecked'}}" 
                catchtap="onTaskCheckbox" 
                data-task="{{item}}"
              >
                <text class="checkbox-text" wx:if="{{item.status === 'completed'}}">✓</text>
                <text class="checkbox-text" wx:else>☐</text>
              </view>
              <view class="task-text-container">
                <text class="task-text {{item.status === 'completed' ? 'completed-text' : ''}}">{{item.title}}</text>
                <view class="task-meta" wx:if="{{item.dueDateStr || item.priority !== 'medium'}}">
                  <text class="task-due-date" wx:if="{{item.dueDateStr}}">📅 {{item.dueDateStr}}</text>
                  <text class="task-priority priority-{{item.priority}}" wx:if="{{item.priority !== 'medium'}}">
                    {{item.priority === 'high' ? '🔥' : '📌'}} {{item.priority === 'high' ? '高' : '低'}}
                  </text>
                </view>
              </view>
            </view>
            <view class="task-actions">
              <view class="quick-edit-btn" catchtap="editTask" data-task="{{item}}">
                <text class="edit-icon">✏️</text>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 滚动控制按钮 -->
        <view class="scroll-controls" wx:if="{{quadrants.urgentNotImportant.length > 3}}">
          <view class="scroll-btn scroll-top-btn" catchtap="scrollToTop" data-quadrant="urgentNotImportant">
            <text class="scroll-icon">↑</text>
          </view>
          <view class="scroll-btn scroll-bottom-btn" catchtap="scrollToBottom" data-quadrant="urgentNotImportant">
            <text class="scroll-icon">↓</text>
          </view>
        </view>
      </view>

      <!-- 不重要不紧急 -->
      <view class="quadrant not-important-not-urgent">
        <view class="quadrant-header">
          <text class="quadrant-title">不重要不紧急</text>
          <text class="task-count">{{quadrants.notImportantNotUrgent.length}}</text>
        </view>
        <scroll-view class="task-scroll" scroll-y="true" enable-flex="true" scroll-top="{{notImportantNotUrgentScrollTop}}" bindscroll="onQuadrantScroll" data-quadrant="notImportantNotUrgent">
          <view 
            class="task-item not-important-not-urgent-item {{item.status === 'completed' ? 'completed' : ''}}"
            wx:for="{{quadrants.notImportantNotUrgent}}" 
            wx:key="id"
            bind:tap="onTaskClick"
            bind:longpress="showTaskActions"
            data-task="{{item}}"
          >
            <view class="task-content">
              <!-- 勾选框 -->
              <view 
                class="task-checkbox {{item.status === 'completed' ? 'checked' : 'unchecked'}}" 
                catchtap="onTaskCheckbox" 
                data-task="{{item}}"
              >
                <text class="checkbox-text" wx:if="{{item.status === 'completed'}}">✓</text>
                <text class="checkbox-text" wx:else>☐</text>
              </view>
              <view class="task-text-container">
                <text class="task-text {{item.status === 'completed' ? 'completed-text' : ''}}">{{item.title}}</text>
                <view class="task-meta" wx:if="{{item.dueDateStr || item.priority !== 'medium'}}">
                  <text class="task-due-date" wx:if="{{item.dueDateStr}}">📅 {{item.dueDateStr}}</text>
                  <text class="task-priority priority-{{item.priority}}" wx:if="{{item.priority !== 'medium'}}">
                    {{item.priority === 'high' ? '🔥' : '📌'}} {{item.priority === 'high' ? '高' : '低'}}
                  </text>
                </view>
              </view>
            </view>
            <view class="task-actions">
              <view class="quick-edit-btn" catchtap="editTask" data-task="{{item}}">
                <text class="edit-icon">✏️</text>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 滚动控制按钮 -->
        <view class="scroll-controls" wx:if="{{quadrants.notImportantNotUrgent.length > 3}}">
          <view class="scroll-btn scroll-top-btn" catchtap="scrollToTop" data-quadrant="notImportantNotUrgent">
            <text class="scroll-icon">↑</text>
          </view>
          <view class="scroll-btn scroll-bottom-btn" catchtap="scrollToBottom" data-quadrant="notImportantNotUrgent">
            <text class="scroll-icon">↓</text>
          </view>
        </view>
      </view>

    </view>
  </view>

  <!-- 列表视图 -->
  <view class="list-view" wx:if="{{!loading && viewMode === 'list'}}">
    <view class="task-list">
      <view 
        wx:for="{{allTasks}}" 
        wx:key="id"
        class="task-list-item {{item.status === 'completed' ? 'completed' : ''}}"
        bind:tap="onTaskClick"
        bind:longpress="showTaskActions"
        data-task="{{item}}"
      >
        <view class="task-left">
          <!-- 勾选框 -->
          <view 
            class="task-checkbox {{item.status === 'completed' ? 'checked' : 'unchecked'}}" 
            catchtap="onTaskCheckbox" 
            data-task="{{item}}"
          >
            <text class="checkbox-text" wx:if="{{item.status === 'completed'}}">✓</text>
            <text class="checkbox-text" wx:else>☐</text>
          </view>
          <view class="task-text-container">
            <text class="task-title {{item.status === 'completed' ? 'completed-text' : ''}}">{{item.title}}</text>
            <view class="task-meta" wx:if="{{item.dueDateStr || item.description}}">
              <text class="task-due-date" wx:if="{{item.dueDateStr}}">📅 {{item.dueDateStr}}</text>
              <text class="task-description" wx:if="{{item.description}}">{{item.description}}</text>
            </view>
          </view>
        </view>
        <view class="task-actions">
          <view class="quick-edit-btn" catchtap="editTask" data-task="{{item}}">
            <text class="edit-icon">✏️</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && isDataLoaded && stats.total === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">还没有任务</text>
    <text class="empty-desc">点击下方按钮创建你的第一个任务</text>
  </view>

  <!-- 添加按钮 -->
  <view class="fab-container" wx:if="{{!loading}}">
    <view class="custom-fab" bind:tap="addTask">
      <view class="plus-icon">
        <view class="plus-vertical"></view>
        <view class="plus-horizontal"></view>
      </view>
    </view>
  </view>
</view>