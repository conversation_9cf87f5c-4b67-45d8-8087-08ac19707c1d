<!--finances.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <t-icon name="chevron-left" size="24" color="#333" bind:tap="goBack" />
    <text class="title">财务管理</text>
    <text class="test-btn" bind:tap="addTestData">测试</text>
  </view>

  <!-- 概览卡片 -->
  <view class="overview-section">
    <text class="section-title">本月概览</text>
    
    <view class="overview-cards">
      <view class="overview-card income">
        <text class="card-label">收入</text>
        <text class="card-amount">¥{{overview.income}}</text>
        <text class="card-change {{overview.incomeChange >= 0 ? 'positive' : 'negative'}}">
          {{overview.incomeChange >= 0 ? '+' : ''}}{{overview.incomeChange}}%
        </text>
      </view>
      
      <view class="overview-card expense">
        <text class="card-label">支出</text>
        <text class="card-amount">¥{{overview.expense}}</text>
        <text class="card-change {{overview.expenseChange >= 0 ? 'positive' : 'negative'}}">
          {{overview.expenseChange >= 0 ? '+' : ''}}{{overview.expenseChange}}%
        </text>
      </view>
    </view>

    <view class="budget-card">
      <text class="card-label">结余</text>
      <text class="card-amount">¥{{overview.budget}}</text>
      <text class="card-change {{overview.budgetChange >= 0 ? 'positive' : 'negative'}}">
        {{overview.budgetChange >= 0 ? '+' : ''}}{{overview.budgetChange}}%
      </text>
    </view>
  </view>

  <!-- 支出分析 -->
  <view class="expense-section" wx:if="{{expenseAnalysis.breakdown.length > 0}}">
    <text class="section-title">支出分析</text>
    <text class="expense-total">¥{{expenseAnalysis.total}}</text>
    <text class="expense-period">本月 {{expenseAnalysis.change >= 0 ? '+' : ''}}{{expenseAnalysis.change}}%</text>
    
    <view class="expense-breakdown">
      <view 
        class="expense-item" 
        wx:for="{{expenseAnalysis.breakdown}}" 
        wx:key="category"
      >
        <view class="expense-bar">
          <view 
            class="expense-fill {{item.cssClass}}" 
            style="width: {{item.percentage}}%"
          ></view>
        </view>
        <view class="expense-info">
          <text class="expense-category">{{item.category}}</text>
          <text class="expense-amount">¥{{item.amount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 关键指标 -->
  <view class="metrics-section" wx:if="{{totalIncome > 0 || totalExpense > 0}}">
    <text class="section-title">收入趋势</text>
    
    <view class="income-trend">
      <text class="metric-title">本月收入</text>
      <text class="metric-amount">¥{{metrics.incomeTrend}}</text>
      <text class="metric-period">相比上月 {{metrics.incomeGrowth >= 0 ? '+' : ''}}{{metrics.incomeGrowth}}%</text>
      
      <!-- 简单的趋势指示 -->
      <view class="trend-indicator">
        <text class="trend-text">{{metrics.incomeGrowth >= 0 ? '📈 增长' : '📉 下降'}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{rawExpenses.length === 0 && !loading}}">
    <view class="empty-icon">💰</view>
    <text class="empty-title">暂无财务记录</text>
    <text class="empty-subtitle">使用AI输入功能快速记录收支</text>
    <view class="empty-actions">
      <button class="action-btn primary" bind:tap="addTestData">添加测试数据</button>
      <button class="action-btn" bind:tap="goToInput">去输入页面</button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 财务记录详情（隐藏，用于调试） -->
  <view class="debug-info" wx:if="{{false}}">
    <text class="debug-title">调试信息 ({{rawExpenses.length}}条记录)</text>
    <view class="debug-item" wx:for="{{rawExpenses}}" wx:key="id">
      <text class="debug-text">{{item.category}}: ¥{{item.amount}} ({{item.type}})</text>
    </view>
  </view>
</view>