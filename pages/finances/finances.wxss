/* pages/finances/finances.wxss */
.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin: 16rpx 24rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.test-btn {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 12rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.test-btn:active {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 1);
}

.placeholder {
  width: 48rpx;
}

/* 金额概览 */
.overview-section {
  padding: 32rpx;
  background: transparent;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.overview-cards {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.overview-card {
  flex: 1;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.overview-card.income {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.3);
}

.overview-card.expense {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(245, 158, 11, 0.3);
}

.budget-card {
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border-radius: 16rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(139, 92, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.card-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  margin-bottom: 8rpx;
}

.card-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.card-change {
  font-size: 24rpx;
  font-weight: 500;
}

.card-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.card-change.negative {
  color: rgba(255, 255, 255, 0.7);
}

/* 关键指标 */
.metrics-section {
  margin: 24rpx 32rpx;
}

.income-trend {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.05);
}

.metric-title {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.metric-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.metric-period {
  font-size: 24rpx;
  color: #10b981;
  display: block;
  margin-bottom: 24rpx;
}

.trend-indicator {
  padding: 16rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 12rpx;
  text-align: center;
}

.trend-text {
  font-size: 28rpx;
  color: #10b981;
  font-weight: 600;
}

/* 支出分析 */
.expense-section {
  margin: 24rpx 32rpx;
}

.expense-total {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.expense-period {
  font-size: 24rpx;
  color: #f87171;
  display: block;
  margin-bottom: 32rpx;
}

.expense-breakdown {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.05);
}

.expense-item {
  margin-bottom: 24rpx;
}

.expense-item:last-child {
  margin-bottom: 0;
}

.expense-bar {
  height: 16rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}

.expense-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.expense-fill.food {
  background: #fa8c16;
}

.expense-fill.rent {
  background: #1890ff;
}

.expense-fill.living {
  background: #52c41a;
}

.expense-fill.entertainment {
  background: #722ed1;
}

.expense-fill.transport {
  background: #13c2c2;
}

.expense-fill.shopping {
  background: #eb2f96;
}

.expense-fill.groceries {
  background: #a0d911;
}

.expense-fill.other {
  background: #8c8c8c;
}

.expense-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expense-category {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.expense-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.7;
  filter: drop-shadow(0 8rpx 24rpx rgba(0, 0, 0, 0.1));
}

.empty-title {
  font-size: 36rpx;
  color: #2d3748;
  margin-bottom: 16rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #718096;
  line-height: 1.6;
  font-weight: 400;
  margin-bottom: 48rpx;
}

.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  max-width: 400rpx;
}

.action-btn {
  padding: 24rpx 48rpx !important;
  border-radius: 25rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border: none !important;
  transition: all 0.3s ease !important;
  background: rgba(255, 255, 255, 0.8) !important;
  color: #4a5568 !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.action-btn:active {
  transform: scale(0.98) !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #718096;
  font-weight: 500;
}

/* 调试信息（隐藏） */
.debug-info {
  margin: 32rpx;
  padding: 24rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.debug-title {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.debug-item {
  margin-bottom: 8rpx;
}

.debug-text {
  font-size: 22rpx;
  color: #888;
  font-family: monospace;
}

/* 图表容器 */
.chart-container {
  background: white;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}