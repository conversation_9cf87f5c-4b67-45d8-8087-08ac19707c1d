import dataApi from '../../utils/dataApi.js';

Page({
  data: {
    overview: {
      income: '0',
      expense: '0',
      budget: '0',
      incomeChange: 0,
      expenseChange: 0,
      budgetChange: 0
    },
    metrics: {
      incomeTrend: '0',
      incomeGrowth: 0
    },
    expenseAnalysis: {
      total: '0',
      change: 0,
      breakdown: []
    },
    // 原始财务数据
    rawExpenses: [],
    totalIncome: 0,
    totalExpense: 0,
    loading: false
  },

  async onLoad() {
    console.log('财务页面加载');

    // 检查用户认证
    if (!this.checkUserAuth()) {
      return;
    }

    // 初始化数据API
    await this.initDataApi();

    // 加载财务数据
    await this.loadFinanceData();
  },

  // 检查用户认证状态
  checkUserAuth() {
    const app = getApp();
    const isLoggedIn = app.globalData.isLoggedIn && app.globalData.uid;

    if (!isLoggedIn) {
      console.log('用户未登录，跳转到登录页面');
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }

    console.log('用户认证通过，uid:', app.globalData.uid);
    return true;
  },

  async onShow() {
    console.log('财务页面显示');

    // 检查用户认证
    if (!this.checkUserAuth()) {
      return;
    }

    // 每次显示都重新加载数据
    await this.loadFinanceData();
  },

  // 加载财务数据（带用户隔离）
  async loadFinanceData() {
    if (this.data.loading) return;

    // 再次检查用户认证
    if (!this.checkUserAuth()) {
      return;
    }

    this.setData({ loading: true });

    try {
      console.log('=== 开始加载财务数据 ===');

      // 获取当前用户的财务记录
      const app = getApp();
      const userQuery = app.getUserQuery(); // 获取用户查询条件
      console.log('用户查询条件:', userQuery);

      // 获取所有财务记录
      const expenses = await dataApi.getExpenses({
        orderBy: 'date',
        order: 'desc',
        ...userQuery // 添加用户隔离条件
      });

      console.log('获取到财务记录:', expenses.length, '条');
      console.log('财务记录详情:', expenses);

      if (expenses.length === 0) {
        // 没有数据时显示空状态
        this.setData({
          overview: {
            income: '0',
            expense: '0',
            budget: '0',
            incomeChange: 0,
            expenseChange: 0,
            budgetChange: 0
          },
          expenseAnalysis: {
            total: '0',
            change: 0,
            breakdown: []
          },
          rawExpenses: [],
          loading: false
        });
        console.log('没有财务数据，显示空状态');
        return;
      }

      // 计算财务统计
      const stats = this.calculateExpenseStats(expenses);
      console.log('计算出的财务统计:', stats);

      // 格式化并设置数据
      this.formatAndSetData(expenses, stats);

    } catch (error) {
      console.error('加载财务数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 初始化数据API
  async initDataApi() {
    try {
      const success = await dataApi.init();
      if (success) {
        console.log('财务页面 - 数据API初始化成功');
      } else {
        console.warn('财务页面 - 数据API初始化失败，将使用降级方案');
      }
    } catch (error) {
      console.error('财务页面 - 数据API初始化出错:', error);
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },



  // 计算财务统计
  calculateExpenseStats(expenses) {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    // 本月数据
    const thisMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === currentMonth && 
             expenseDate.getFullYear() === currentYear;
    });
    
    // 上月数据（用于计算变化率）
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const lastMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === lastMonth && 
             expenseDate.getFullYear() === lastMonthYear;
    });
    
    // 计算收入和支出
    const thisMonthStats = this.calculateMonthStats(thisMonthExpenses);
    const lastMonthStats = this.calculateMonthStats(lastMonthExpenses);
    
    // 计算变化率
    const incomeChange = this.calculateChangeRate(lastMonthStats.income, thisMonthStats.income);
    const expenseChange = this.calculateChangeRate(lastMonthStats.expense, thisMonthStats.expense);
    
    // 分类统计
    const categoryStats = this.calculateCategoryStats(thisMonthExpenses);
    
    return {
      thisMonth: thisMonthStats,
      lastMonth: lastMonthStats,
      incomeChange,
      expenseChange,
      categoryStats
    };
  },

  // 计算单月统计
  calculateMonthStats(expenses) {
    let income = 0;
    let expense = 0;
    
    expenses.forEach(item => {
      if (item.type === 'income') {
        income += item.amount;
      } else {
        expense += item.amount;
      }
    });
    
    return { income, expense, count: expenses.length };
  },

  // 计算变化率
  calculateChangeRate(oldValue, newValue) {
    if (oldValue === 0) {
      return newValue > 0 ? 100 : 0;
    }
    return Math.round(((newValue - oldValue) / oldValue) * 100);
  },

  // 计算分类统计
  calculateCategoryStats(expenses) {
    const categoryMap = new Map();
    let totalExpense = 0;
    
    expenses.forEach(expense => {
      if (expense.type !== 'income') {
        const category = expense.category || '其他';
        if (!categoryMap.has(category)) {
          categoryMap.set(category, { amount: 0, count: 0 });
        }
        const stats = categoryMap.get(category);
        stats.amount += expense.amount;
        stats.count += 1;
        totalExpense += expense.amount;
      }
    });
    
    // 转换为数组并计算百分比
    const categoryArray = [];
    categoryMap.forEach((stats, category) => {
      const percentage = totalExpense > 0 ? (stats.amount / totalExpense) * 100 : 0;
      categoryArray.push({
        category,
        amount: stats.amount,
        count: stats.count,
        percentage: Math.round(percentage),
        cssClass: this.getCssClassForCategory(category)
      });
    });
    
    // 按金额排序
    categoryArray.sort((a, b) => b.amount - a.amount);
    
    return {
      categories: categoryArray,
      totalExpense
    };
  },

  // 格式化并设置数据
  formatAndSetData(expenses, stats) {
    const { thisMonth, incomeChange, expenseChange, categoryStats } = stats;
    
    // 格式化金额显示
    const formatAmount = (amount) => {
      return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    };
    
    this.setData({
      overview: {
        income: formatAmount(thisMonth.income),
        expense: formatAmount(thisMonth.expense),
        budget: formatAmount(Math.max(0, thisMonth.income - thisMonth.expense)),
        incomeChange,
        expenseChange,
        budgetChange: Math.round((thisMonth.income - thisMonth.expense) / 
                                 (thisMonth.income || 1) * 100)
      },
      metrics: {
        incomeTrend: formatAmount(thisMonth.income),
        incomeGrowth: incomeChange
      },
      expenseAnalysis: {
        total: formatAmount(categoryStats.totalExpense),
        change: expenseChange,
        breakdown: categoryStats.categories.slice(0, 6).map(item => ({
          category: item.category,
          amount: formatAmount(item.amount),
          percentage: item.percentage,
          cssClass: item.cssClass
        }))
      },
      rawExpenses: expenses,
      totalIncome: thisMonth.income,
      totalExpense: thisMonth.expense
    });
    
    console.log('=== 财务数据设置完成 ===');
    console.log('概览数据:', this.data.overview);
    console.log('支出分析:', this.data.expenseAnalysis);
    
    // 显示数据更新提示
    if (expenses.length > 0) {
      wx.showToast({
        title: `已加载${expenses.length}条记录`,
        icon: 'success',
        duration: 1500
      });
    }
  },

  // 跳转到收入详情
  goToIncomeDetail() {
    wx.navigateTo({
      url: '/pages/income-detail/income-detail'
    });
  },

  // 跳转到支出详情
  goToExpenseDetail() {
    wx.navigateTo({
      url: '/pages/expense-detail/expense-detail'
    });
  },

  // 跳转到预算设置
  goToBudgetSetting() {
    wx.navigateTo({
      url: '/pages/budget-setting/budget-setting'
    });
  },

  // 跳转到输入页面
  goToInput() {
    wx.switchTab({
      url: '/pages/input/input'
    });
  },

  // 中文类别到英文CSS类名的映射
  getCssClassForCategory(category) {
    const categoryMap = {
      '餐饮': 'food',
      '房租': 'rent', 
      '生活费用': 'living',
      '娱乐': 'entertainment',
      '交通': 'transport',
      '购物': 'shopping',
      '生活用品': 'groceries',
      '其他': 'other'
    };
    return categoryMap[category] || 'other';
  },

  // 手动刷新数据
  async onPullDownRefresh() {
    await this.loadFinanceData();
    wx.stopPullDownRefresh();
  },

  // 显示财务记录详情
  showExpenseDetail(e) {
    const expense = e.currentTarget.dataset.expense;
    const content = [
      `分类: ${expense.category}`,
      `金额: ¥${expense.amount}`,
      `描述: ${expense.description || '无'}`,
      `日期: ${new Date(expense.date).toLocaleDateString()}`,
      `类型: ${expense.type === 'income' ? '收入' : '支出'}`
    ].join('\n');
    
    wx.showModal({
      title: '财务记录详情',
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 添加测试财务数据
  async addTestData() {
    wx.showActionSheet({
      itemList: ['添加测试支出数据', '添加测试收入数据', '测试淘宝购物数据', '显示调试信息'],
      success: async (res) => {
        if (res.tapIndex === 0) {
          // 添加测试支出数据
          await this.createTestExpenseData();
        } else if (res.tapIndex === 1) {
          // 添加测试收入数据
          await this.createTestIncomeData();
        } else if (res.tapIndex === 2) {
          // 测试淘宝购物数据
          await this.createTestShoppingData();
        } else if (res.tapIndex === 3) {
          // 显示调试信息
          await this.showDebugInfo();
        }
      }
    });
  },

  // 创建测试支出数据
  async createTestExpenseData() {
    try {
      wx.showLoading({ title: '创建测试支出...' });
      
      const testExpenses = [
        { amount: 50, category: '餐饮', description: '午餐', type: 'expense' },
        { amount: 200, category: '购物', description: '买衣服', type: 'expense' },
        { amount: 30, category: '交通', description: '打车费', type: 'expense' },
        { amount: 100, category: '娱乐', description: '看电影', type: 'expense' }
      ];
      
      for (const data of testExpenses) {
        await dataApi.createExpense({
          ...data,
          date: new Date().toISOString(),
          source: 'test_expense'
        });
      }
      
      wx.hideLoading();
      wx.showToast({ title: '测试支出已添加', icon: 'success' });
      await this.loadFinanceData();
      
    } catch (error) {
      wx.hideLoading();
      console.error('添加测试支出失败:', error);
      wx.showToast({ title: '添加失败', icon: 'error' });
    }
  },

  // 创建测试收入数据
  async createTestIncomeData() {
    try {
      wx.showLoading({ title: '创建测试收入...' });
      
      const testIncomes = [
        { amount: 5000, category: '工资', description: '月薪', type: 'income' },
        { amount: 500, category: '奖金', description: '绩效奖金', type: 'income' },
        { amount: 200, category: '退款', description: '商品退款', type: 'income' }
      ];
      
      for (const data of testIncomes) {
        await dataApi.createExpense({
          ...data,
          date: new Date().toISOString(),
          source: 'test_income'
        });
      }
      
      wx.hideLoading();
      wx.showToast({ title: '测试收入已添加', icon: 'success' });
      await this.loadFinanceData();
      
    } catch (error) {
      wx.hideLoading();
      console.error('添加测试收入失败:', error);
      wx.showToast({ title: '添加失败', icon: 'error' });
    }
  },

  // 创建测试购物数据
  async createTestShoppingData() {
    try {
      wx.showLoading({ title: '创建测试购物...' });
      
      const testShoppingData = [
        { amount: 89, category: '购物', description: '淘宝买鞋子89元', type: 'expense' },
        { amount: 156, category: '购物', description: '天猫购买化妆品156元', type: 'expense' },
        { amount: 299, category: '购物', description: '京东买手机壳299元', type: 'expense' },
        { amount: 68, category: '购物', description: '拼多多买日用品68元', type: 'expense' }
      ];
      
      for (const data of testShoppingData) {
        await dataApi.createExpense({
          ...data,
          date: new Date().toISOString(),
          source: 'test_shopping'
        });
      }
      
      wx.hideLoading();
      wx.showToast({ title: '测试购物数据已添加', icon: 'success' });
      await this.loadFinanceData();
      
    } catch (error) {
      wx.hideLoading();
      console.error('添加测试购物失败:', error);
      wx.showToast({ title: '添加失败', icon: 'error' });
    }
  },

  // 显示调试信息
  async showDebugInfo() {
    try {
      console.log('=== 财务页面调试信息 ===');
      
      // 获取所有财务数据
      const allExpenses = await dataApi.getExpenses();
      console.log('所有财务记录:', allExpenses);
      
      // 统计信息
      const incomeRecords = allExpenses.filter(item => item.type === 'income');
      const expenseRecords = allExpenses.filter(item => item.type === 'expense');
      const shoppingRecords = allExpenses.filter(item => item.category === '购物');
      
      const debugInfo = [
        `总记录数: ${allExpenses.length}`,
        `收入记录: ${incomeRecords.length}条`,
        `支出记录: ${expenseRecords.length}条`,
        `购物记录: ${shoppingRecords.length}条`,
        '',
        '最近5条记录:',
        ...allExpenses.slice(0, 5).map((item, index) => 
          `${index+1}. ${item.category} ¥${item.amount} (${item.type})`
        )
      ].join('\n');
      
      wx.showModal({
        title: '调试信息',
        content: debugInfo,
        showCancel: false,
        confirmText: '我知道了'
      });
      
    } catch (error) {
      console.error('获取调试信息失败:', error);
      wx.showToast({ title: '获取失败', icon: 'error' });
    }
  }
})