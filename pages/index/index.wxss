/* pages/index/index.wxss */
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 160rpx; /* 为底部按钮留空间 */
}

/* 月份导航 */
.month-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  margin: 20rpx 24rpx 0;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.nav-arrow {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.nav-arrow:active {
  background: linear-gradient(135deg, #ff5252, #ff9800);
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.5);
}

.arrow-icon {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.month-title {
  font-size: 42rpx;
  font-weight: 800;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 星期标题 */
.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 0;
  margin: 16rpx 24rpx 0;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.weekday {
  text-align: center;
  font-size: 30rpx;
  color: #2d3748;
  font-weight: 700;
  text-shadow: 0 1rpx 3rpx rgba(255, 255, 255, 0.9);
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20rpx);
  gap: 8rpx;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  padding: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  /* 🎯 动态高度：通过style属性控制，移除固定高度 */
  /* min-height: 280rpx; */
  /* max-height: 760rpx; */
  /* 高度完全由calendarStyle动态控制 */
}

.calendar-day {
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  min-height: 115rpx;
  max-height: 125rpx;
  border-radius: 12rpx;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.1);
}

.calendar-day:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.95);
}

.calendar-day.today {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.calendar-day.selected {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(1.05);
}

.calendar-day.other-month {
  background: rgba(255, 255, 255, 0.4);
  color: #718096;
}

.calendar-day.other-month .day-number {
  opacity: 0.7;
  color: #a0aec0;
}

/* 🎯 空白日期样式 - 完全隐藏 */
.calendar-day.empty-day {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  pointer-events: none; /* 禁用点击 */
  backdrop-filter: none !important;
}

.calendar-day.empty-day:active {
  background: transparent !important;
  transform: none !important;
}

.day-number {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.calendar-day.today .day-number,
.calendar-day.selected .day-number {
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.event-dots {
  display: flex;
  gap: 4rpx;
  position: absolute;
  bottom: 8rpx;
}

.event-dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.event-dot.schedule {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  animation: pulse 2s infinite;
}

.calendar-day.today .event-dot.schedule,
.calendar-day.selected .event-dot.schedule {
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* 选中日期显示 */
.selected-date {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20rpx);
  padding: 32rpx 40rpx;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.selected-date-text {
  font-size: 38rpx;
  font-weight: 800;
  color: #2d3748;
  text-shadow: 0 1rpx 3rpx rgba(255, 255, 255, 0.8);
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding: 32rpx;
}

/* 通用样式 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-icon {
  width: 10rpx;
  height: 36rpx;
  border-radius: 6rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.section-icon.schedule-icon {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.section-text {
  font-size: 34rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 日程列表 */
.schedule-section {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20rpx);
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.schedule-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.schedule-item:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.98);
}

.schedule-time {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 140rpx;
  text-align: center;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.data-type-icon {
  font-size: 28rpx;
}

.time-text {
  font-size: 24rpx;
}

.schedule-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.schedule-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.4;
}

.schedule-desc {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.4;
}

.schedule-actions {
  display: flex;
  align-items: center;
}

.edit-btn {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.edit-btn:active {
  background: linear-gradient(135deg, #ff5252, #ff9800);
  transform: scale(0.95);
}

/* 无日程状态 */
.no-schedule {
  text-align: center;
  padding: 60rpx 32rpx;
}

.no-schedule-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
  display: block;
  margin-bottom: 16rpx;
}

.add-hint {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 添加按钮 */
.add-btn {
  position: fixed;
  right: 48rpx;
  bottom: 48rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.4);
  z-index: 1000;
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.6);
}

.add-icon {
  width: 48rpx;
  height: 48rpx;
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

/* 🔍 临时调试按钮样式 */
.debug-btn {
  position: fixed;
  right: 48rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #722ed1, #eb2f96);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(114, 46, 209, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}

.debug-btn:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(114, 46, 209, 0.6);
}

.debug-icon {
  color: white;
  font-size: 36rpx;
  line-height: 1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .month-nav {
    padding: 32rpx;
    margin: 16rpx 20rpx 0;
  }
  
  .calendar-grid {
    margin: 16rpx 20rpx;
    gap: 6rpx;
    padding: 12rpx;
  }
  
  .calendar-day {
    min-height: 100rpx;
    max-height: 110rpx;
  }
  
  .day-number {
    font-size: 28rpx;
    font-weight: 700;
  }
  
  .selected-date {
    margin: 16rpx 20rpx;
    padding: 24rpx 32rpx;
  }
  
  .selected-date-text {
    font-size: 34rpx;
  }
  
  .schedule-section {
    margin: 16rpx 20rpx;
    padding: 24rpx;
  }
  
  .schedule-item {
    padding: 20rpx;
    gap: 16rpx;
  }
  
  .schedule-time {
    min-width: 100rpx;
    font-size: 22rpx;
    padding: 10rpx 16rpx;
  }
  
  .schedule-title {
    font-size: 28rpx;
  }
  
  .schedule-desc {
    font-size: 24rpx;
  }
}

/* 特殊日期样式 */
.calendar-day.weekend {
  background: rgba(255, 182, 193, 0.3);
}

.calendar-day.weekend.today {
  background: linear-gradient(135deg, #ff6b6b, #ff8a80);
}

.calendar-day.holiday {
  background: rgba(255, 215, 0, 0.3);
  color: #ff6b6b;
}

.calendar-day.holiday .day-number {
  font-weight: 700;
}

/* 🎯 不同数据类型的样式 */
.schedule-item.todo-item .schedule-time {
  background: linear-gradient(135deg, #722ed1, #eb2f96);
  box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.3);
}

.schedule-item.finance-item .schedule-time {
  background: linear-gradient(135deg, #52c41a, #13c2c2);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

.schedule-item.urgent-item {
  border-left: 8rpx solid #ff4d4f;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 107, 107, 0.05));
  animation: pulse-urgent 2s infinite;
}

@keyframes pulse-urgent {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.2);
  }
  50% {
    box-shadow: 0 8rpx 32rpx rgba(255, 77, 79, 0.4);
  }
}

/* 🎯 标签系统 */
.schedule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
  white-space: nowrap;
}

.category-tag {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.3);
}

.priority-tag {
  border: 1rpx solid;
}

.priority-urgent {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-color: rgba(255, 77, 79, 0.3);
}

.priority-high {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
  border-color: rgba(250, 140, 22, 0.3);
}

.priority-medium {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: rgba(82, 196, 26, 0.3);
}

.priority-low {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
  border-color: rgba(140, 140, 140, 0.3);
}

.ai-tag {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  font-weight: 600;
}

/* 开发模式按钮组 */
.dev-buttons {
  position: fixed;
  left: 24rpx;
  bottom: 48rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  z-index: 998;
}

.dev-btn {
  width: 120rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.dev-btn.init-data {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.dev-btn.show-stats {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.dev-btn.clear-data {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.dev-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.dev-icon {
  font-size: 24rpx;
  color: white;
  line-height: 1;
}

.dev-text {
  font-size: 20rpx;
  color: white;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  line-height: 1;
}
