<!--index.wxml-->
<view class="container">
  <!-- 月份导航 -->
  <view class="month-nav">
    <view class="nav-arrow" bind:tap="prevMonth">
      <text class="arrow-icon">‹</text>
    </view>
    <text class="month-title">{{currentMonth}}</text>
    <view class="nav-arrow" bind:tap="nextMonth">
      <text class="arrow-icon">›</text>
    </view>
  </view>

  <!-- 星期标题 -->
  <view class="weekdays">
    <view class="weekday" wx:for="{{weekdays}}" wx:key="*this">{{item}}</view>
  </view>

  <!-- 月历网格 -->
  <view class="calendar-grid" style="{{calendarStyle}}">
    <view 
      class="calendar-day {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}} {{item.isOtherMonth ? 'other-month' : ''}} {{!item.showDate ? 'empty-day' : ''}}"
      wx:for="{{calendarDays}}"
      wx:key="id"
      data-date="{{item.dateStr}}"
      bind:tap="selectCalendarDate"
    >
      <!-- 只在showDate为true时显示日期内容 -->
      <block wx:if="{{item.showDate}}">
        <text class="day-number">{{item.day}}</text>
        <!-- 事件小点 -->
        <view class="event-dots" wx:if="{{item.hasSchedules}}">
          <view class="event-dot schedule"></view>
        </view>
      </block>
    </view>
  </view>

  <!-- 选中日期显示 -->
  <view class="selected-date">
    <text class="selected-date-text">{{selectedDate.monthDay}}</text>
  </view>

  <!-- 日程列表 -->
  <view class="schedule-section">
    <view class="section-title">
      <view class="section-icon schedule-icon"></view>
      <text class="section-text">{{selectedDate.monthDay}} 安排</text>
    </view>
    
    <view class="schedule-list" wx:if="{{selectedSchedules.length > 0}}">
      <view 
        class="schedule-item {{item.dataType === 'todo' ? 'todo-item' : ''}} {{item.dataType === 'finance' ? 'finance-item' : ''}} {{item.priority === 'urgent' ? 'urgent-item' : ''}}"
        wx:for="{{selectedSchedules}}" 
        wx:key="id"
        bind:tap="editSchedule"
        data-item="{{item}}"
      >
        <view class="schedule-time">
          <text class="data-type-icon">{{item.displayIcon}}</text>
          <text class="time-text">{{item.time}}</text>
        </view>
        <view class="schedule-content">
          <text class="schedule-title">{{item.title}}</text>
          <text class="schedule-desc" wx:if="{{item.description}}">{{item.description}}</text>
          <view class="schedule-tags" wx:if="{{item.category || item.priority}}">
            <text class="tag category-tag" wx:if="{{item.category}}">{{item.category}}</text>
            <text class="tag priority-tag priority-{{item.priority}}" wx:if="{{item.priority}}">{{item.priority}}</text>
            <text class="tag ai-tag" wx:if="{{item.source === 'ai_test_data'}}">AI分类</text>
          </view>
        </view>
        <view class="schedule-actions">
          <text class="edit-btn">详情</text>
        </view>
      </view>
    </view>
    
    <view class="no-schedule" wx:else>
      <text class="no-schedule-text">暂无安排</text>
      <text class="add-hint">点击右下角 + 添加</text>
    </view>
  </view>

  <!-- 添加按钮 -->
  <view class="add-btn" bind:tap="showAddMenu">
    <text class="add-icon">+</text>
  </view>

  <!-- 🔍 开发模式按钮组 -->
  <view class="dev-buttons" wx:if="{{showDevButtons}}">
    <view class="dev-btn init-data" bind:tap="initTestData">
      <text class="dev-icon">🧪</text>
      <text class="dev-text">测试数据</text>
    </view>
    <view class="dev-btn show-stats" bind:tap="showDataStats">
      <text class="dev-icon">📊</text>
      <text class="dev-text">数据统计</text>
    </view>
    <view class="dev-btn clear-data" bind:tap="clearAllData">
      <text class="dev-icon">🗑️</text>
      <text class="dev-text">清除数据</text>
    </view>
  </view>

  <!-- 🔍 临时调试按钮 -->
  <view class="debug-btn" bind:tap="testButtonResponse">
    <text class="debug-icon">🔍</text>
  </view>
</view>