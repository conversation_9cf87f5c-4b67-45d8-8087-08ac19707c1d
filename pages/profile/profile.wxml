<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-info">
      <t-avatar 
        size="large"
        image="{{userInfo.avatar || '/assets/icons/app-icon-120.png'}}"
        class="user-avatar"
      />
      <view class="user-details">
        <text class="user-name">{{userInfo.nick || '未设置昵称'}}</text>
        <view class="user-status">
          <t-tag 
            wx:if="{{userInfo.proUntil}}"
            theme="success" 
            size="small"
          >
            Pro 用户
          </t-tag>
          <t-tag 
            wx:else
            theme="default" 
            size="small"
          >
            普通用户
          </t-tag>
          <text wx:if="{{isGuest}}" class="guest-badge">访客模式</text>
        </view>
      </view>
    </view>
    
    <t-button 
      wx:if="{{!isGuest}}"
      theme="primary" 
      variant="outline"
      size="small"
      bind:tap="editProfile"
    >
      编辑资料
    </t-button>
  </view>

  <!-- 订阅状态 -->
  <view class="subscription-section" wx:if="{{!isGuest}}">
    <t-cell-group>
      <t-cell 
        title="Pro 订阅"
        description="{{subscriptionDesc}}"
        hover
        bind:click="manageSubscription"
      >
        <t-icon slot="left-icon" name="crown" size="24" color="#ffa500" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
    </t-cell-group>
  </view>

  <!-- 功能设置 -->
  <view class="settings-section">
    <view class="section-title">设置</view>
    <t-cell-group>
      <t-cell 
        title="数据同步"
        description="{{syncStatus}}"
        hover
        bind:click="manageSync"
      >
        <t-icon slot="left-icon" name="refresh" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
      
      <t-cell 
        title="设备信息"
        description="{{deviceInfo}}"
        hover
        bind:click="showDeviceInfo"
      >
        <t-icon slot="left-icon" name="mobile" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
      
      <t-cell 
        title="缓存管理"
        description="本地数据: {{cacheSize}}"
        hover
        bind:click="manageCache"
      >
        <t-icon slot="left-icon" name="folder" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
    </t-cell-group>
  </view>

  <!-- 其他功能 -->
  <view class="other-section">
    <view class="section-title">其他</view>
    <t-cell-group>
      <t-cell 
        title="意见反馈"
        hover
        bind:click="feedback"
      >
        <t-icon slot="left-icon" name="chat" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
      
      <t-cell 
        title="关于我们"
        hover
        bind:click="aboutUs"
      >
        <t-icon slot="left-icon" name="info-circle" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
      
      <t-cell 
        title="隐私政策"
        hover
        bind:click="showPrivacy"
      >
        <t-icon slot="left-icon" name="lock-on" size="24" color="#1296db" />
        <t-icon slot="right-icon" name="chevron-right" size="16" color="#ccc" />
      </t-cell>
    </t-cell-group>
  </view>

  <!-- 登出注销区域 -->
  <view class="logout-section">
    <t-button 
      wx:if="{{!isGuest}}"
      theme="default" 
      variant="outline"
      size="large"
      bind:tap="logout"
      class="logout-btn"
    >
      退出登录
    </t-button>
    
    <t-button 
      wx:if="{{isGuest}}"
      theme="primary" 
      size="large"
      bind:tap="goToLogin"
      class="login-btn"
    >
      立即登录
    </t-button>
    
    <t-button 
      wx:if="{{!isGuest}}"
      theme="danger" 
      variant="outline"
      size="large"
      bind:tap="deleteAccount"
      class="delete-btn"
    >
      注销账户
    </t-button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">版本: {{version}}</text>
  </view>
</view>

<!-- 设备信息弹窗 -->
<t-popup 
  visible="{{showDevicePopup}}" 
  bind:visible-change="onDevicePopupChange"
  placement="bottom"
>
  <view class="popup-content">
    <view class="popup-header">
      <text class="popup-title">设备信息</text>
      <t-icon name="close" size="24" bind:tap="closeDevicePopup" />
    </view>
    <view class="device-details">
      <view class="device-item">
        <text class="device-label">设备型号：</text>
        <text class="device-value">{{systemInfo.model}}</text>
      </view>
      <view class="device-item">
        <text class="device-label">系统版本：</text>
        <text class="device-value">{{systemInfo.system}}</text>
      </view>
      <view class="device-item">
        <text class="device-label">微信版本：</text>
        <text class="device-value">{{systemInfo.version}}</text>
      </view>
      <view class="device-item">
        <text class="device-label">屏幕尺寸：</text>
        <text class="device-value">{{systemInfo.screenWidth}} x {{systemInfo.screenHeight}}</text>
      </view>
    </view>
  </view>
</t-popup>