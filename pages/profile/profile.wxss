/* pages/profile/profile.wxss */
.container {
  background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 用户信息头部 */
.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 48rpx 32rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  margin: 16rpx 24rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.guest-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 订阅状态 */
.subscription-section {
  margin: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 设置区域 */
.settings-section,
.other-section {
  margin: 24rpx 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

/* 登出注销区域 */
.logout-section {
  margin: 48rpx 32rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.logout-btn,
.login-btn,
.delete-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  color: white;
  border: none;
}

.delete-btn {
  border: 2rpx solid #ff4757;
  color: #ff4757;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}

/* 弹窗样式 */
.popup-content {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 32rpx 32rpx 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.device-details {
  padding: 32rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.device-item:last-child {
  border-bottom: none;
}

.device-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.device-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  max-width: 300rpx;
  word-break: break-all;
}

/* TDesign 组件样式覆盖 */
.t-cell-group {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.t-cell {
  background: transparent !important;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

.t-cell:last-child {
  border-bottom: none !important;
}

.t-cell__content {
  background: transparent !important;
}

.t-cell__title {
  background: transparent !important;
}

.t-cell__description {
  background: transparent !important;
}

/* 响应式适配 */
@media (max-width: 750px) {
  .user-header {
    padding: 40rpx 24rpx 24rpx;
  }
  
  .settings-section,
  .other-section,
  .logout-section {
    margin-left: 24rpx;
    margin-right: 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(to bottom, #1f2937 0%, #111827 100%);
  }
  
  .t-cell-group {
    background: rgba(31, 41, 55, 0.3) !important;
  }
}