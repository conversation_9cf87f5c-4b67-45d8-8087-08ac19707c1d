// pages/profile/profile.js
Page({
  data: {
    userInfo: {},
    isGuest: false,
    subscriptionDesc: '未订阅 Pro 服务',
    syncStatus: '已同步',
    deviceInfo: '设备信息正常',
    cacheSize: '0MB',
    version: '1.0.0',
    showDevicePopup: false,
    systemInfo: {}
  },

  onLoad() {
    console.log('用户中心页面加载');
    this.loadUserData();
    this.getSystemInfo();
    this.calculateCacheSize();
  },

  onShow() {
    // 页面显示时重新加载用户数据
    this.loadUserData();
  },

  // 加载用户数据
  loadUserData() {
    const app = getApp();
    const userInfo = app.globalData.userInfo || {};
    const isGuest = app.globalData.isGuest || false;
    
    // 计算订阅状态
    let subscriptionDesc = '未订阅 Pro 服务';
    if (userInfo.proUntil) {
      const expireDate = new Date(userInfo.proUntil);
      const now = new Date();
      if (expireDate > now) {
        subscriptionDesc = `Pro 会员至 ${expireDate.toLocaleDateString()}`;
      } else {
        subscriptionDesc = 'Pro 会员已过期';
      }
    }
    
    // 计算同步状态
    let syncStatus = '已同步';
    if (isGuest) {
      syncStatus = '访客模式，未同步';
    }
    
    this.setData({
      userInfo,
      isGuest,
      subscriptionDesc,
      syncStatus
    });
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        console.log('获取系统信息成功:', res);
        this.setData({
          systemInfo: res,
          deviceInfo: `${res.model} ${res.system}`
        });
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error);
      }
    });
  },

  // 计算缓存大小
  calculateCacheSize() {
    try {
      wx.getStorageInfo({
        success: (res) => {
          const sizeInKB = res.currentSize;
          let sizeDisplay;
          
          if (sizeInKB > 1024) {
            sizeDisplay = (sizeInKB / 1024).toFixed(1) + 'MB';
          } else {
            sizeDisplay = sizeInKB + 'KB';
          }
          
          this.setData({
            cacheSize: sizeDisplay
          });
        }
      });
    } catch (error) {
      console.error('计算缓存大小失败:', error);
    }
  },

  // 编辑资料
  editProfile() {
    wx.showToast({
      title: '编辑资料功能开发中',
      icon: 'none'
    });
  },

  // 管理订阅
  manageSubscription() {
    wx.showModal({
      title: 'Pro 订阅',
      content: 'Pro 版本包含：\n1. OCR 票据识别\n2. 预算预警提醒\n3. PDF 报表导出\n4. 多人共享账本\n\n价格：¥18/月',
      confirmText: '立即订阅',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '订阅功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 管理数据同步
  manageSync() {
    if (this.data.isGuest) {
      wx.showModal({
        title: '需要登录',
        content: '访客模式下无法同步数据，请先登录您的账户。',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            this.goToLogin();
          }
        }
      });
      return;
    }
    
    wx.showLoading({ title: '同步中...' });
    
    // 模拟同步过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '同步完成',
        icon: 'success'
      });
      
      this.setData({
        syncStatus: '已同步'
      });
    }, 2000);
  },

  // 显示设备信息
  showDeviceInfo() {
    this.setData({ showDevicePopup: true });
  },

  // 关闭设备信息弹窗
  closeDevicePopup() {
    this.setData({ showDevicePopup: false });
  },

  // 设备信息弹窗状态改变
  onDevicePopupChange(e) {
    this.setData({ showDevicePopup: e.detail.visible });
  },

  // 管理缓存
  manageCache() {
    wx.showActionSheet({
      itemList: ['查看缓存详情', '清空缓存'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.showCacheDetails();
        } else if (res.tapIndex === 1) {
          this.clearCache();
        }
      }
    });
  },

  // 显示缓存详情
  showCacheDetails() {
    wx.getStorageInfo({
      success: (res) => {
        const content = `缓存大小: ${res.currentSize}KB\n缓存限制: ${res.limitSize}KB\n缓存项数: ${res.keys.length}个\n\n主要缓存项目:\n${res.keys.slice(0, 5).join('\n')}`;
        
        wx.showModal({
          title: '缓存详情',
          content,
          showCancel: false
        });
      }
    });
  },

  // 清空缓存
  clearCache() {
    wx.showModal({
      title: '清空缓存',
      content: '清空缓存将删除本地保存的数据，但不会影响云端数据。确定要清空吗？',
      confirmText: '确定清空',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorage({
            success: () => {
              wx.showToast({
                title: '缓存已清空',
                icon: 'success'
              });
              
              this.calculateCacheSize();
              
              // 清空后重新设置基本数据
              const app = getApp();
              app.globalData = {
                isGuest: true
              };
            },
            fail: (error) => {
              console.error('清空缓存失败:', error);
              wx.showToast({
                title: '清空失败',
                icon: 'error'
              });
            }
          });
        }
      }
    });
  },

  // 意见反馈
  feedback() {
    wx.showModal({
      title: '意见反馈',
      content: '您可以通过以下方式反馈意见：\n\n1. 应用内反馈\n2. 微信群\n3. 邮件反馈\n4. GitHub Issues',
      confirmText: '加入群聊',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '群聊二维码开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 关于我们
  aboutUs() {
    wx.showModal({
      title: '关于我们',
      content: '📡 一句话全能助手 v1.0.0\n\n🎆 产品特色：\n• 语音识别 + 智能分类\n• 日程、记账、待办一站式管理\n• 支持CalDAV日历同步\n\n👨‍💻 开发团队：独立开发者\n📧 联系邮箱：<EMAIL>',
      showCancel: false
    });
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/login/login?showPrivacy=true'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '退出后将清空本地缓存，但云端数据不会受影响。确定要退出吗？',
      confirmText: '确定退出',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      }
    });
  },

  // 执行退出登录
  performLogout() {
    // 清空全局数据
    const app = getApp();
    app.globalData = {};
    
    // 清空本地存储
    wx.clearStorageSync();
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
    
    // 跳转到登录页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }, 1000);
  },

  // 去登录
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 注销账户
  deleteAccount() {
    wx.showModal({
      title: '注销账户',
      content: '❗️ 警告：注销账户不可恢复！\n\n注销后将会：\n• 删除所有云端数据\n• 清空本地缓存\n• 无法恢复任何信息\n\n请确认您真的要注销账户。',
      confirmText: '确定注销',
      cancelText: '取消',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.confirmDeleteAccount();
        }
      }
    });
  },

  // 确认注销账户
  confirmDeleteAccount() {
    wx.showModal({
      title: '最后确认',
      content: '请输入“确认注销”四个字来确认此操作。',
      editable: true,
      placeholderText: '请输入：确认注销',
      success: (res) => {
        if (res.confirm && res.content === '确认注销') {
          this.performDeleteAccount();
        } else if (res.confirm) {
          wx.showToast({
            title: '输入不正确',
            icon: 'error'
          });
        }
      }
    });
  },

  // 执行注销账户
  async performDeleteAccount() {
    wx.showLoading({ title: '注销中...' });
    
    try {
      // 调用云函数删除用户数据
      const result = await wx.cloud.callFunction({
        name: 'deleteAccount'
      });
      
      console.log('注销云函数结果:', result);
      
      if (result.result.success) {
        wx.hideLoading();
        
        // 清空本地数据
        const app = getApp();
        app.clearLoginStatus();
        
        wx.showModal({
          title: '注销成功',
          content: `已成功删除 ${result.result.data.deletedRecords} 条数据记录。\n\n感谢您的使用，欢迎随时回来！`,
          showCancel: false,
          success: () => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      } else {
        throw new Error(result.result.message || '注销失败');
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('注销账户失败:', error);
      
      // 如果云函数调用失败，降级为本地清空
      wx.showModal({
        title: '注销失败',
        content: '云端注销失败，但已清空本地数据。请联系客服完成云端数据清理。',
        confirmText: '确定',
        success: () => {
          const app = getApp();
          app.clearLoginStatus();
          
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      });
    }
  }
});