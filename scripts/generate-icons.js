#!/usr/bin/env node

/**
 * 图标生成脚本
 * 为微信小程序生成标准尺寸的图标文件
 */

const fs = require('fs');
const path = require('path');

// 图标配置
const iconConfig = {
  // TabBar 图标 (推荐 81x81px)
  tabbar: {
    size: 81,
    icons: [
      { name: 'input', desc: '输入', color: '#666', selectedColor: '#667eea' },
      { name: 'calendar', desc: '日程', color: '#666', selectedColor: '#667eea' },
      { name: 'task', desc: '待办', color: '#666', selectedColor: '#667eea' },
      { name: 'profile', desc: '财务', color: '#666', selectedColor: '#667eea' }
    ]
  },
  // 应用图标 (多尺寸)
  app: {
    sizes: [120, 180, 1024], // 小程序图标标准尺寸
    name: 'app-icon'
  }
};

// 创建 SVG 图标模板
const createSVGIcon = (name, size, color, selected = false) => {
  const bgColor = color === '#666' ? '#667eea' : '#5a5fcf';
  const icons = {
    input: `
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="10" fill="${bgColor}"/>
        <rect x="6" y="8" width="12" height="8" rx="1" fill="none" stroke="white" stroke-width="1.5"/>
        <line x1="8" y1="10" x2="8" y2="10" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="10" y1="10" x2="10" y2="10" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="12" y1="10" x2="12" y2="10" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="14" y1="10" x2="14" y2="10" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="16" y1="10" x2="16" y2="10" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="8" y1="12" x2="10" y2="12" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="12" y1="12" x2="16" y2="12" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="10" y1="14" x2="14" y2="14" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    `,
    calendar: `
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="10" fill="${bgColor}"/>
        <rect x="8" y="7" width="8" height="9" rx="1" fill="none" stroke="white" stroke-width="1.5"/>
        <line x1="10" y1="5" x2="10" y2="9" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="14" y1="5" x2="14" y2="9" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="8" y1="10" x2="16" y2="10" stroke="white" stroke-width="1.5"/>
        <circle cx="10" cy="12" r="0.5" fill="white"/>
        <circle cx="12" cy="12" r="0.5" fill="white"/>
        <circle cx="14" cy="12" r="0.5" fill="white"/>
        <circle cx="10" cy="14" r="0.5" fill="white"/>
        <circle cx="12" cy="14" r="0.5" fill="white"/>
      </svg>
    `,
    task: `
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="10" fill="${bgColor}"/>
        <rect x="7" y="8" width="10" height="8" rx="1" fill="none" stroke="white" stroke-width="1.5"/>
        <path d="M9 11L11 13L15 9" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `,
    profile: `
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="10" fill="${bgColor}"/>
        <circle cx="12" cy="12" r="5" fill="none" stroke="white" stroke-width="2"/>
        <path d="M10.5 9.5C10.5 8.95 10.95 8.5 11.5 8.5H12.5C13.05 8.5 13.5 8.95 13.5 9.5C13.5 10.05 13.05 10.5 12.5 10.5H11.5C10.95 10.5 10.5 11.45 10.5 12C10.5 12.55 10.95 13 11.5 13H12.5C13.05 13 13.5 13.45 13.5 14C13.5 14.55 13.05 15 12.5 15H11.5C10.95 15 10.5 14.55 10.5 14" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="12" y1="6.5" x2="12" y2="8.5" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        <line x1="12" y1="15.5" x2="12" y2="17.5" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    `
  };
  
  return icons[name] || icons.calendar;
};

// 创建应用图标 SVG
const createAppIcon = (size) => `
  <svg width="${size}" height="${size}" viewBox="0 0 120 120" fill="none">
    <rect width="120" height="120" rx="20" fill="url(#gradient)"/>
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="60" cy="45" r="15" fill="white"/>
    <path d="M35 75 Q60 85 85 75" stroke="white" stroke-width="3" fill="none"/>
    <text x="60" y="95" text-anchor="middle" fill="white" font-size="12" font-family="Arial">助手</text>
  </svg>
`;

// 确保目录存在
const ensureDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// 生成图标文件
const generateIcons = () => {
  const assetsDir = path.join(__dirname, '../assets');
  const iconsDir = path.join(assetsDir, 'icons');
  
  ensureDir(iconsDir);
  
  console.log('🎨 开始生成图标文件...');
  
  // 生成 TabBar 图标
  iconConfig.tabbar.icons.forEach(icon => {
    const normalSVG = createSVGIcon(icon.name, iconConfig.tabbar.size, icon.color, false);
    const selectedSVG = createSVGIcon(icon.name, iconConfig.tabbar.size, icon.selectedColor, true);
    
    // 写入 SVG 文件（开发时可用）
    fs.writeFileSync(path.join(iconsDir, `${icon.name}.svg`), normalSVG);
    fs.writeFileSync(path.join(iconsDir, `${icon.name}_selected.svg`), selectedSVG);
    
    console.log(`✅ 生成 ${icon.desc} 图标: ${icon.name}.svg`);
  });
  
  // 生成应用图标
  iconConfig.app.sizes.forEach(size => {
    const appSVG = createAppIcon(size);
    fs.writeFileSync(path.join(iconsDir, `${iconConfig.app.name}-${size}.svg`), appSVG);
    console.log(`✅ 生成应用图标: ${iconConfig.app.name}-${size}.svg`);
  });
  
  // 创建图标使用说明
  const readme = `# 图标文件说明

## TabBar 图标 (81x81px) - 统一圆形设计
- input.png / input_selected.png - 输入图标（键盘设计）
- calendar.png / calendar_selected.png - 日程图标（圆形加号设计）
- task.png / task_selected.png - 待办图标（圆形加号设计）
- profile.png / profile_selected.png - 财务图标（钱币设计）

## 设计规范
- **统一背景**：所有图标使用紫色圆形背景 (#667eea 普通状态，#5a5fcf 选中状态)
- **图标颜色**：所有图标内容使用白色
- **尺寸标准**：81x81px PNG格式，适配微信小程序tabBar
- **视觉一致性**：保持现代化圆形设计风格

## 图标含义
- **输入页面**：键盘图标，代表文字输入和语音输入功能
- **日程页面**：加号图标，代表添加和管理日程安排
- **待办页面**：加号图标，代表添加和管理待办任务
- **财务页面**：钱币图标，代表财务记录和管理

## 应用图标
- app-icon-120.png - 小程序图标 (120x120)
- app-icon-180.png - 小程序图标 (180x180)
- app-icon-1024.png - 提交审核用图标 (1024x1024)

## 使用方法
1. 运行 \`node scripts/generate-icons.js\` 生成 SVG 文件
2. 运行 \`node scripts/create-circle-plus-png.js\` 生成 PNG 文件
3. PNG文件会自动生成到 assets/icons/ 目录

## 注意事项
- 所有图标已采用统一的紫色圆形设计
- 图标背景透明度为0，圆形背景色已内置
- 线条粗细适中，确保在小尺寸下清晰可见
- 保持与应用整体UI色彩方案一致
`;
  
  fs.writeFileSync(path.join(iconsDir, 'README.md'), readme);
  
  console.log('📝 生成图标说明文档');
  console.log('🎉 图标生成完成！请将 SVG 文件转换为 PNG 格式');
  console.log('💡 推荐使用: https://svgtopng.com/ 进行转换');
};

// 执行生成
generateIcons();