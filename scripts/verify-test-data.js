#!/usr/bin/env node

/**
 * 验证测试数据创建脚本
 * 检查小程序中的测试数据是否正确创建和显示
 */

console.log('🔍 === 测试数据验证工具 ===\n');

// 模拟小程序存储环境
const mockStorage = new Map();

// 模拟wx.getStorageSync
function getStorageSync(key) {
  return mockStorage.get(key) || null;
}

// 模拟wx.setStorageSync
function setStorageSync(key, data) {
  mockStorage.set(key, data);
  console.log(`💾 存储数据: ${key} (${Array.isArray(data) ? data.length : 1} 条记录)`);
}

// 创建测试数据
function createTestData() {
  console.log('🧪 创建测试数据...\n');
  
  // 日程数据
  const schedules = [
    {
      _id: 'schedule_test_001',
      type: 'schedule',
      title: '产品评审会议',
      description: '讨论Q2产品规划和功能优先级',
      date: '2025-06-17',
      time: '10:00',
      location: '会议室A',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    },
    {
      _id: 'schedule_test_002',
      type: 'schedule',
      title: '客户拜访',
      description: '拜访重要客户，讨论合作细节',
      date: '2025-06-18',
      time: '14:30',
      location: '客户办公室',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    },
    {
      _id: 'schedule_test_003',
      type: 'schedule',
      title: '团队建设活动',
      description: '部门团建，增进团队协作',
      date: '2025-06-19',
      time: '15:00',
      location: '户外拓展基地',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    }
  ];

  // 财务数据
  const expenses = [
    {
      _id: 'expense_test_001',
      type: 'expense',
      title: '星巴克咖啡',
      description: '上午工作时买的咖啡',
      amount: 35,
      category: '餐饮',
      date: '2025-06-16',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    },
    {
      _id: 'expense_test_002',
      type: 'expense',
      title: '技术书籍',
      description: '购买AI相关的技术书籍',
      amount: 120,
      category: '教育',
      date: '2025-06-15',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    }
  ];

  // 待办数据
  const todos = [
    {
      _id: 'todo_test_001',
      type: 'todo',
      title: '完成季度总结报告',
      description: '整理Q2的工作成果和下季度计划',
      priority: 'high',
      status: 'pending',
      deadline: '2025-06-20',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    },
    {
      _id: 'todo_test_002',
      type: 'todo',
      title: '准备技术分享PPT',
      description: '为下周的技术分享会准备演示文稿',
      priority: 'medium',
      status: 'in_progress',
      deadline: '2025-06-19',
      userId: 'test_user_123',
      createdAt: new Date().toISOString(),
      syncStatus: 'local'
    }
  ];

  // 存储数据
  setStorageSync('local_schedules', schedules);
  setStorageSync('local_expenses', expenses);
  setStorageSync('local_todos', todos);

  return { schedules, expenses, todos };
}

// 验证数据完整性
function verifyDataIntegrity(data) {
  console.log('\n🔍 验证数据完整性...\n');
  
  const { schedules, expenses, todos } = data;
  let issues = [];

  // 验证日程数据
  schedules.forEach((schedule, index) => {
    if (!schedule._id) issues.push(`日程${index + 1}: 缺少ID`);
    if (!schedule.title) issues.push(`日程${index + 1}: 缺少标题`);
    if (!schedule.date) issues.push(`日程${index + 1}: 缺少日期`);
    if (!schedule.time) issues.push(`日程${index + 1}: 缺少时间`);
    if (!schedule.userId) issues.push(`日程${index + 1}: 缺少用户ID`);
  });

  // 验证财务数据
  expenses.forEach((expense, index) => {
    if (!expense._id) issues.push(`财务${index + 1}: 缺少ID`);
    if (!expense.title) issues.push(`财务${index + 1}: 缺少标题`);
    if (!expense.amount) issues.push(`财务${index + 1}: 缺少金额`);
    if (!expense.category) issues.push(`财务${index + 1}: 缺少类别`);
    if (!expense.userId) issues.push(`财务${index + 1}: 缺少用户ID`);
  });

  // 验证待办数据
  todos.forEach((todo, index) => {
    if (!todo._id) issues.push(`待办${index + 1}: 缺少ID`);
    if (!todo.title) issues.push(`待办${index + 1}: 缺少标题`);
    if (!todo.priority) issues.push(`待办${index + 1}: 缺少优先级`);
    if (!todo.deadline) issues.push(`待办${index + 1}: 缺少截止日期`);
    if (!todo.userId) issues.push(`待办${index + 1}: 缺少用户ID`);
  });

  if (issues.length === 0) {
    console.log('✅ 数据完整性验证通过');
  } else {
    console.log('❌ 发现数据完整性问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }

  return issues.length === 0;
}

// 验证日期分布
function verifyDateDistribution(data) {
  console.log('\n📅 验证日期分布...\n');
  
  const { schedules, expenses, todos } = data;
  const dateStats = {};

  // 统计日程日期
  schedules.forEach(schedule => {
    const date = schedule.date;
    if (!dateStats[date]) dateStats[date] = { schedules: 0, expenses: 0, todos: 0 };
    dateStats[date].schedules++;
  });

  // 统计财务日期
  expenses.forEach(expense => {
    const date = expense.date;
    if (!dateStats[date]) dateStats[date] = { schedules: 0, expenses: 0, todos: 0 };
    dateStats[date].expenses++;
  });

  // 统计待办日期（使用deadline）
  todos.forEach(todo => {
    const date = todo.deadline;
    if (!dateStats[date]) dateStats[date] = { schedules: 0, expenses: 0, todos: 0 };
    dateStats[date].todos++;
  });

  console.log('📊 日期分布统计:');
  Object.keys(dateStats).sort().forEach(date => {
    const stats = dateStats[date];
    const total = stats.schedules + stats.expenses + stats.todos;
    console.log(`   ${date}: 📅${stats.schedules} 💰${stats.expenses} ✅${stats.todos} (共${total}条)`);
  });

  return Object.keys(dateStats).length;
}

// 模拟小程序页面数据加载
function simulatePageDataLoading() {
  console.log('\n🔄 模拟页面数据加载...\n');
  
  // 从存储中读取数据
  const schedules = getStorageSync('local_schedules') || [];
  const expenses = getStorageSync('local_expenses') || [];
  const todos = getStorageSync('local_todos') || [];

  console.log('📖 从存储读取的数据:');
  console.log(`   📅 日程: ${schedules.length} 条`);
  console.log(`   💰 财务: ${expenses.length} 条`);
  console.log(`   ✅ 待办: ${todos.length} 条`);

  // 模拟日程页面的数据处理逻辑
  const allSchedules = {};
  
  // 处理日程数据
  schedules.forEach(schedule => {
    const dateKey = schedule.date;
    if (!allSchedules[dateKey]) allSchedules[dateKey] = [];
    allSchedules[dateKey].push({
      id: schedule._id,
      title: schedule.title,
      time: schedule.time,
      description: schedule.description,
      location: schedule.location,
      dataType: 'schedule',
      displayIcon: '📅'
    });
  });

  // 处理财务数据
  expenses.forEach(expense => {
    const dateKey = expense.date;
    if (!allSchedules[dateKey]) allSchedules[dateKey] = [];
    allSchedules[dateKey].push({
      id: expense._id,
      title: expense.title,
      amount: expense.amount,
      category: expense.category,
      dataType: 'finance',
      displayIcon: '💰'
    });
  });

  // 处理待办数据
  todos.forEach(todo => {
    const dateKey = todo.deadline;
    if (!allSchedules[dateKey]) allSchedules[dateKey] = [];
    allSchedules[dateKey].push({
      id: todo._id,
      title: todo.title,
      priority: todo.priority,
      status: todo.status,
      dataType: 'todo',
      displayIcon: '✅'
    });
  });

  console.log('\n📋 处理后的页面数据:');
  Object.keys(allSchedules).sort().forEach(date => {
    const items = allSchedules[date];
    console.log(`   ${date}: ${items.length} 项安排`);
    items.forEach(item => {
      console.log(`     ${item.displayIcon} ${item.title}`);
    });
  });

  return allSchedules;
}

// 生成使用指南
function generateUsageGuide() {
  console.log('\n📖 === 使用指南 ===\n');
  
  console.log('🎯 如何在小程序中查看测试数据:');
  console.log('');
  console.log('1. 📱 启动小程序');
  console.log('   - 在微信开发者工具中打开项目');
  console.log('   - 或在真机上运行小程序');
  console.log('');
  console.log('2. 🧪 初始化测试数据');
  console.log('   - 方法1: 启动时会自动提示是否创建测试数据');
  console.log('   - 方法2: 在日程页面点击左下角的"🧪测试数据"按钮');
  console.log('   - 方法3: 在开发者工具控制台执行: getApp().manualInitTestData()');
  console.log('');
  console.log('3. 📅 查看日程数据');
  console.log('   - 打开日程页面（首页）');
  console.log('   - 在月历中点击有数据的日期（会显示小圆点）');
  console.log('   - 查看下方的安排列表');
  console.log('');
  console.log('4. 💰 查看财务数据');
  console.log('   - 切换到财务页面');
  console.log('   - 查看收支记录和统计');
  console.log('');
  console.log('5. ✅ 查看待办数据');
  console.log('   - 切换到待办页面');
  console.log('   - 查看任务列表和进度');
  console.log('');
  console.log('🔧 调试工具:');
  console.log('   - 📊 数据统计: 查看当前存储的数据数量');
  console.log('   - 🗑️ 清除数据: 删除所有测试数据');
  console.log('   - 🔍 调试按钮: 查看详细的数据状态');
  console.log('');
  console.log('⚠️ 注意事项:');
  console.log('   - 测试数据只在开发环境显示创建按钮');
  console.log('   - 数据存储在本地，清除缓存会丢失');
  console.log('   - 每次重新创建会覆盖之前的测试数据');
}

// 主函数
function main() {
  console.log('🚀 开始验证测试数据创建流程...\n');
  
  try {
    // 1. 创建测试数据
    const testData = createTestData();
    
    // 2. 验证数据完整性
    const isValid = verifyDataIntegrity(testData);
    
    // 3. 验证日期分布
    const dateCount = verifyDateDistribution(testData);
    
    // 4. 模拟页面数据加载
    const pageData = simulatePageDataLoading();
    
    // 5. 生成报告
    console.log('\n📊 === 验证报告 ===');
    console.log(`✅ 数据完整性: ${isValid ? '通过' : '失败'}`);
    console.log(`📅 日期覆盖: ${dateCount} 个不同日期`);
    console.log(`📋 页面数据: ${Object.keys(pageData).length} 个日期有安排`);
    console.log(`📊 总记录数: ${testData.schedules.length + testData.expenses.length + testData.todos.length} 条`);
    
    if (isValid && dateCount > 0 && Object.keys(pageData).length > 0) {
      console.log('\n🎉 测试数据验证成功！');
      console.log('✅ 数据已正确创建，可以在小程序中查看');
    } else {
      console.log('\n❌ 测试数据验证失败！');
      console.log('⚠️ 请检查数据创建逻辑');
    }
    
    // 6. 显示使用指南
    generateUsageGuide();
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  }
}

// 执行验证
main();
