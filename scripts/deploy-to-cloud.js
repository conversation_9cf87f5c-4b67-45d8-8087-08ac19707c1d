#!/usr/bin/env node
/**
 * 云函数部署脚本 - 通过微信开发者工具CLI
 */

const { execSync } = require('child_process');
const path = require('path');

const functions = ['writeRecord', 'userLogin', 'deleteAccount'];

console.log('🚀 开始部署云函数到云端...\n');

// 尝试使用微信开发者工具的CLI
try {
  // 首先检查是否有微信开发者工具的CLI
  const cliPath = '/Applications/wechatwebdevtools.app/Contents/MacOS/cli';
  
  functions.forEach(functionName => {
    console.log(`📦 部署 ${functionName}...`);
    
    try {
      // 使用微信开发者工具CLI部署云函数
      const command = `"${cliPath}" --upload-cloud-function ${functionName} --project ${process.cwd()}`;
      console.log(`执行命令: ${command}`);
      
      execSync(command, { stdio: 'inherit' });
      console.log(`✅ ${functionName} 部署成功\n`);
      
    } catch (error) {
      console.error(`❌ ${functionName} 部署失败:`, error.message);
    }
  });
  
} catch (error) {
  console.error('❌ 微信开发者工具CLI不可用:', error.message);
  console.log('\n📝 请手动在微信开发者工具中部署：');
  console.log('1. 重启微信开发者工具');
  console.log('2. 确保云开发已开通');
  console.log('3. 右键云函数文件夹选择"上传并部署（云端安装依赖）"');
}