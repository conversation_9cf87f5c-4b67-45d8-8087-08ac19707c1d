// scripts/deploy-cloud-functions.js - 云函数部署脚本

const fs = require('fs');
const path = require('path');

console.log('🚀 开始部署云函数...\n');

// 云函数列表
const cloudFunctions = ['writeRecord', 'userLogin', 'deleteAccount'];

// 检查云函数文件
function checkCloudFunctions() {
  console.log('📋 检查云函数文件...');
  
  const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions');
  
  if (!fs.existsSync(cloudfunctionsDir)) {
    console.error('❌ cloudfunctions 目录不存在');
    return false;
  }
  
  for (const funcName of cloudFunctions) {
    const funcDir = path.join(cloudfunctionsDir, funcName);
    const indexFile = path.join(funcDir, 'index.js');
    const packageFile = path.join(funcDir, 'package.json');
    
    console.log(`📁 检查 ${funcName}:`);
    
    if (!fs.existsSync(funcDir)) {
      console.error(`  ❌ 文件夹不存在: ${funcDir}`);
      return false;
    }
    
    if (!fs.existsSync(indexFile)) {
      console.error(`  ❌ index.js 不存在: ${indexFile}`);
      return false;
    }
    
    if (!fs.existsSync(packageFile)) {
      console.error(`  ❌ package.json 不存在: ${packageFile}`);
      return false;
    }
    
    // 检查依赖
    const packageJson = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
    const hasWxServerSdk = packageJson.dependencies && packageJson.dependencies['wx-server-sdk'];
    
    console.log(`  ✅ 文件夹存在`);
    console.log(`  ✅ index.js 存在`);
    console.log(`  ✅ package.json 存在`);
    console.log(`  ${hasWxServerSdk ? '✅' : '❌'} wx-server-sdk 依赖 ${hasWxServerSdk ? '已配置' : '缺失'}`);
    
    if (!hasWxServerSdk) {
      console.warn(`  ⚠️  建议在 ${funcName}/package.json 中添加 wx-server-sdk 依赖`);
    }
    
    console.log('');
  }
  
  return true;
}

// 生成部署命令
function generateDeployCommands() {
  console.log('📝 生成部署命令:\n');
  
  console.log('请在微信开发者工具中按以下步骤操作:\n');
  
  console.log('方法1: 使用图形界面');
  console.log('1. 打开微信开发者工具');
  console.log('2. 点击工具栏中的"云开发"按钮');
  console.log('3. 在云开发控制台中，依次对以下云函数进行操作:');
  
  cloudFunctions.forEach((funcName, index) => {
    console.log(`   ${index + 1}. 右键点击 "${funcName}" 文件夹`);
    console.log(`      → 选择"上传并部署（云端安装依赖）"`);
    console.log(`      → 等待部署完成`);
  });
  
  console.log('\n方法2: 使用命令行（如果可用）');
  console.log('在项目根目录执行:');
  cloudFunctions.forEach(funcName => {
    console.log(`npx @cloudbase/cli functions:deploy ${funcName} --envId <你的环境ID>`);
  });
  
  console.log('\n方法3: 使用腾讯云控制台');
  console.log('1. 访问: https://console.cloud.tencent.com/tcb');
  console.log('2. 选择对应的环境');
  console.log('3. 点击"云函数" → "新建云函数"');
  console.log('4. 上传对应的文件夹压缩包');
}

// 检查环境配置
function checkEnvironment() {
  console.log('🔧 检查环境配置...\n');
  
  const projectConfigFile = path.join(__dirname, '../project.config.json');
  
  if (!fs.existsSync(projectConfigFile)) {
    console.error('❌ project.config.json 不存在');
    return false;
  }
  
  const config = JSON.parse(fs.readFileSync(projectConfigFile, 'utf8'));
  
  console.log('项目配置:');
  console.log(`  AppID: ${config.appid || '未配置'}`);
  console.log(`  云函数根目录: ${config.cloudfunctionRoot || '未配置'}`);
  
  if (config.cloudfunctionRoot) {
    console.log('  ✅ 云函数根目录已配置');
  } else {
    console.log('  ❌ 云函数根目录未配置，建议添加: "cloudfunctionRoot": "cloudfunctions/"');
  }
  
  console.log('');
  return true;
}

// 生成部署状态检查脚本
function generateStatusCheck() {
  const statusCheck = `
// 部署状态检查脚本
// 复制此代码到微信开发者工具的调试控制台中运行

async function checkCloudFunctionStatus() {
  const functions = ['writeRecord', 'userLogin', 'deleteAccount'];
  
  console.log('🔍 检查云函数部署状态...');
  
  for (const funcName of functions) {
    try {
      const result = await wx.cloud.callFunction({
        name: funcName,
        data: { action: 'test' }
      });
      
      console.log(\`✅ \${funcName}: 部署成功\`);
    } catch (error) {
      console.error(\`❌ \${funcName}: 部署失败\`, error.errMsg);
      
      if (error.errMsg && error.errMsg.includes('Cannot find module')) {
        console.log(\`   → 建议重新部署并选择"云端安装依赖"\`);
      }
    }
  }
}

// 运行检查
checkCloudFunctionStatus();
`;

  const statusFile = path.join(__dirname, 'check-deploy-status.js');
  fs.writeFileSync(statusFile, statusCheck.trim());
  
  console.log(`📋 已生成部署状态检查脚本: ${statusFile}`);
  console.log('您可以将此脚本内容复制到微信开发者工具的调试控制台中运行\n');
}

// 主函数
function main() {
  console.log('====================================');
  console.log('   云函数部署助手');
  console.log('====================================\n');
  
  // 检查环境
  if (!checkEnvironment()) {
    process.exit(1);
  }
  
  // 检查云函数文件
  if (!checkCloudFunctions()) {
    console.error('❌ 云函数文件检查失败，请确保所有文件完整');
    process.exit(1);
  }
  
  console.log('✅ 所有云函数文件检查通过!\n');
  
  // 生成部署命令
  generateDeployCommands();
  
  // 生成状态检查脚本
  generateStatusCheck();
  
  console.log('====================================');
  console.log('✨ 部署助手运行完成！');
  console.log('请按照上述步骤在微信开发者工具中完成部署');
  console.log('====================================');
}

// 运行
main();