#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 创建用户管理系统所需的数据库集合和索引
 */

console.log('📚 数据库初始化脚本');
console.log('');
console.log('需要在云控制台手动创建以下集合：');
console.log('');

// 数据库集合配置
const collections = [
  {
    name: 'users',
    description: '用户信息表',
    schema: {
      _id: 'String (openid)',
      unionid: 'String (可选)',
      nick: 'String',
      avatar: 'String',
      gender: 'Number (0=未知, 1=男, 2=女)',
      country: 'String',
      province: 'String', 
      city: 'String',
      language: 'String',
      createdAt: 'Date',
      lastLoginAt: 'Date',
      lastLoginIP: 'String',
      proUntil: 'Date (Pro订阅到期时间)',
      settings: 'Object (用户设置)',
      totalUsageDays: 'Number (累计使用天数)',
      totalRecords: 'Number (累计记录数)'
    },
    indexes: [
      { field: 'unionid', unique: false },
      { field: 'createdAt', unique: false },
      { field: 'lastLoginAt', unique: false }
    ]
  },
  {
    name: 'schedules',
    description: '日程记录表',
    schema: {
      _id: 'String (自动生成)',
      uid: 'String (用户ID)',
      title: 'String (日程标题)',
      description: 'String (详细描述)',
      startTime: 'Date (开始时间)',
      endTime: 'Date (结束时间)',
      allDay: 'Boolean (是否全天)',
      location: 'String (地点)',
      reminder: 'Number (提醒时间，分钟)',
      repeat: 'String (重复规则)',
      calendarId: 'String (关联的日历ID)',
      status: 'String (状态: pending, synced, failed)',
      createdAt: 'Date',
      updatedAt: 'Date',
      syncedAt: 'Date (同步时间)'
    },
    indexes: [
      { field: 'uid', unique: false },
      { field: 'startTime', unique: false },
      { field: 'status', unique: false },
      { field: 'createdAt', unique: false }
    ]
  },
  {
    name: 'expenses',
    description: '财务记录表',
    schema: {
      _id: 'String (自动生成)',
      uid: 'String (用户ID)',
      amount: 'Number (金额)',
      category: 'String (分类)',
      description: 'String (描述)',
      date: 'Date (记录日期)',
      paymentMethod: 'String (支付方式)',
      location: 'String (消费地点)',
      tags: 'Array<String> (标签)',
      receiptUrl: 'String (小票照片URL)',
      type: 'String (income/expense)',
      status: 'String (状态)',
      createdAt: 'Date',
      updatedAt: 'Date'
    },
    indexes: [
      { field: 'uid', unique: false },
      { field: 'date', unique: false },
      { field: 'category', unique: false },
      { field: 'type', unique: false },
      { field: 'createdAt', unique: false }
    ]
  },
  {
    name: 'todos',
    description: '待办任务表',
    schema: {
      _id: 'String (自动生成)',
      uid: 'String (用户ID)',
      title: 'String (任务标题)',
      description: 'String (详细描述)',
      completed: 'Boolean (是否完成)',
      priority: 'Number (优先级 1-5)',
      urgent: 'Boolean (是否紧急)',
      important: 'Boolean (是否重要)',
      dueDate: 'Date (截止日期)',
      tags: 'Array<String> (标签)',
      category: 'String (分类)',
      estimatedMinutes: 'Number (预估时间)',
      completedAt: 'Date (完成时间)',
      createdAt: 'Date',
      updatedAt: 'Date'
    },
    indexes: [
      { field: 'uid', unique: false },
      { field: 'completed', unique: false },
      { field: 'priority', unique: false },
      { field: 'dueDate', unique: false },
      { field: 'createdAt', unique: false }
    ]
  },
  {
    name: 'sync_queue',
    description: '数据同步队列表',
    schema: {
      _id: 'String (自动生成)',
      uid: 'String (用户ID)',
      action: 'String (create/update/delete)',
      collection: 'String (目标集合名)',
      recordId: 'String (记录ID)',
      data: 'Object (数据内容)',
      status: 'String (pending/processing/success/failed)',
      retryCount: 'Number (重试次数)',
      error: 'String (错误信息)',
      createdAt: 'Date',
      processedAt: 'Date'
    },
    indexes: [
      { field: 'uid', unique: false },
      { field: 'status', unique: false },
      { field: 'collection', unique: false },
      { field: 'createdAt', unique: false }
    ]
  }
];

// 输出集合创建指南
collections.forEach((collection, index) => {
  console.log(`${index + 1}. 创建集合: ${collection.name}`);
  console.log(`   描述: ${collection.description}`);
  console.log('   字段结构:');
  
  Object.entries(collection.schema).forEach(([field, type]) => {
    console.log(`     ${field}: ${type}`);
  });
  
  console.log('   需要创建的索引:');
  collection.indexes.forEach(index => {
    console.log(`     字段: ${index.field}, 唯一: ${index.unique}`);
  });
  
  console.log('');
});

console.log('📋 创建步骤:');
console.log('1. 打开微信云控制台 https://console.cloud.tencent.com/tcb');
console.log('2. 选择对应的环境ID');
console.log('3. 进入"数据库"模块');
console.log('4. 依次创建上述集合');
console.log('5. 为每个集合添加对应的索引');
console.log('');
console.log('⚠️  注意: users集合的_id字段使用openid作为主键');
console.log('');

// 权限配置建议
console.log('🔐 权限配置建议:');
console.log('所有集合设置为"仅管理端可写，仅创建者可读写"');
console.log('这样可以确保数据安全，只有通过云函数才能操作数据');
console.log('');

console.log('🚀 创建完成后，可以运行以下命令部署云函数:');
console.log('npm run cloud:deploy'); 