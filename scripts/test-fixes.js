#!/usr/bin/env node

/**
 * 测试修复效果的脚本
 * 验证主要功能是否正常工作
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 === 开始测试项目修复效果 ===\n');

// 测试结果统计
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

/**
 * 添加测试结果
 */
function addTestResult(name, status, message = '') {
  const result = { name, status, message };
  testResults.details.push(result);
  
  if (status === 'pass') {
    testResults.passed++;
    console.log(`✅ ${name}: ${message || '通过'}`);
  } else if (status === 'fail') {
    testResults.failed++;
    console.log(`❌ ${name}: ${message || '失败'}`);
  } else if (status === 'warning') {
    testResults.warnings++;
    console.log(`⚠️  ${name}: ${message || '警告'}`);
  }
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  addTestResult(
    description,
    exists ? 'pass' : 'fail',
    exists ? '文件存在' : '文件不存在'
  );
  return exists;
}

/**
 * 检查文件内容是否包含特定字符串
 */
function checkFileContent(filePath, searchStrings, description) {
  if (!fs.existsSync(filePath)) {
    addTestResult(description, 'fail', '文件不存在');
    return false;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const missingStrings = searchStrings.filter(str => !content.includes(str));
    
    if (missingStrings.length === 0) {
      addTestResult(description, 'pass', '内容检查通过');
      return true;
    } else {
      addTestResult(description, 'fail', `缺少内容: ${missingStrings.join(', ')}`);
      return false;
    }
  } catch (error) {
    addTestResult(description, 'fail', `读取文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试AI服务修复
 */
function testAIServiceFixes() {
  console.log('\n🤖 测试AI服务修复...');
  
  // 检查AI服务文件
  checkFileExists('utils/aiService.js', 'AI服务文件存在');
  
  // 检查关键修复内容
  checkFileContent(
    'utils/aiService.js',
    [
      'tryProvider(providerId, provider, text)',
      'normalizedLength: normalizedText.length',
      'testSuccess: !!testResult',
      'url,',
      'success: pingResult'
    ],
    'AI服务未使用变量修复'
  );
  
  // 检查多提供商支持
  checkFileContent(
    'utils/aiService.js',
    [
      'providers',
      'deepseek',
      'silicon',
      'availableProviders'
    ],
    'AI服务多提供商支持'
  );
}

/**
 * 测试语音服务修复
 */
function testVoiceServiceFixes() {
  console.log('\n🎤 测试语音服务修复...');
  
  // 检查语音服务文件
  checkFileExists('utils/voiceService.js', '语音服务文件存在');
  
  // 检查输入页面语音功能修复
  checkFileContent(
    'pages/input/input.js',
    [
      'wx.getRecorderManager()',
      'checkRecordPermission',
      'setupRecorderEvents',
      'scope.record'
    ],
    '输入页面语音功能修复'
  );
  
  // 检查云函数
  checkFileExists('cloudfunctions/speechRecognition/index.js', '语音识别云函数存在');
  
  checkFileContent(
    'cloudfunctions/speechRecognition/index.js',
    [
      'exports.main',
      'mockRecognition',
      'tencentASR',
      'baiduASR'
    ],
    '语音识别云函数功能完整'
  );
}

/**
 * 测试数据同步修复
 */
function testDataSyncFixes() {
  console.log('\n💾 测试数据同步修复...');
  
  // 检查数据同步文件
  checkFileExists('utils/dataSync.js', '数据同步文件存在');
  
  // 检查修复内容
  checkFileContent(
    'utils/dataSync.js',
    [
      'syncData',
      'substring(2, 11)',
      '同步数据预览'
    ],
    '数据同步未使用变量修复'
  );
}

/**
 * 测试用户认证修复
 */
function testUserAuthFixes() {
  console.log('\n🔐 测试用户认证修复...');
  
  // 检查财务页面认证
  checkFileContent(
    'pages/finances/finances.js',
    [
      'checkUserAuth',
      'getUserQuery',
      'globalData.uid'
    ],
    '财务页面用户认证修复'
  );
  
  // 检查认证工具
  checkFileExists('utils/auth.js', '认证工具文件存在');
}

/**
 * 测试云函数完整性
 */
function testCloudFunctions() {
  console.log('\n☁️ 测试云函数完整性...');
  
  const cloudFunctions = [
    'userLogin',
    'deleteAccount',
    'speechRecognition',
    'writeRecord'
  ];
  
  cloudFunctions.forEach(funcName => {
    const funcPath = `cloudfunctions/${funcName}/index.js`;
    checkFileExists(funcPath, `云函数 ${funcName}`);
    
    const packagePath = `cloudfunctions/${funcName}/package.json`;
    checkFileExists(packagePath, `云函数 ${funcName} 配置文件`);
  });
}

/**
 * 测试页面完整性
 */
function testPageIntegrity() {
  console.log('\n📱 测试页面完整性...');
  
  const pages = [
    'login',
    'input', 
    'index',
    'profile',
    'finances',
    'tasks',
    'transactions'
  ];
  
  pages.forEach(pageName => {
    const jsPath = `pages/${pageName}/${pageName}.js`;
    const wxmlPath = `pages/${pageName}/${pageName}.wxml`;
    const wxssPath = `pages/${pageName}/${pageName}.wxss`;
    const jsonPath = `pages/${pageName}/${pageName}.json`;
    
    checkFileExists(jsPath, `页面 ${pageName} JS文件`);
    checkFileExists(wxmlPath, `页面 ${pageName} WXML文件`);
    checkFileExists(wxssPath, `页面 ${pageName} WXSS文件`);
    checkFileExists(jsonPath, `页面 ${pageName} JSON文件`);
  });
}

/**
 * 测试配置文件
 */
function testConfigFiles() {
  console.log('\n⚙️ 测试配置文件...');
  
  // 检查主要配置文件
  checkFileExists('app.js', '应用主文件');
  checkFileExists('app.json', '应用配置文件');
  checkFileExists('app.wxss', '应用样式文件');
  checkFileExists('package.json', '项目配置文件');
  
  // 检查app.json配置
  checkFileContent(
    'app.json',
    [
      'pages/login/login',
      'pages/input/input',
      'pages/finances/finances',
      'tabBar'
    ],
    '应用配置完整性'
  );
}

/**
 * 生成测试报告
 */
function generateReport() {
  console.log('\n📊 === 测试报告 ===');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`⚠️  警告: ${testResults.warnings}`);
  
  const total = testResults.passed + testResults.failed + testResults.warnings;
  const successRate = total > 0 ? Math.round((testResults.passed / total) * 100) : 0;
  
  console.log(`📈 成功率: ${successRate}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败项目:');
    testResults.details
      .filter(result => result.status === 'fail')
      .forEach(result => {
        console.log(`   - ${result.name}: ${result.message}`);
      });
  }
  
  if (testResults.warnings > 0) {
    console.log('\n⚠️  警告项目:');
    testResults.details
      .filter(result => result.status === 'warning')
      .forEach(result => {
        console.log(`   - ${result.name}: ${result.message}`);
      });
  }
  
  console.log('\n🎯 修复建议:');
  if (testResults.failed === 0) {
    console.log('🎉 所有测试都通过了！项目修复成功。');
    console.log('💡 建议：');
    console.log('   1. 在微信开发者工具中测试语音功能');
    console.log('   2. 测试AI分类功能');
    console.log('   3. 验证用户登录和数据隔离');
    console.log('   4. 检查云函数部署状态');
  } else {
    console.log('🔧 还有一些问题需要修复，请查看上面的失败项目。');
  }
}

// 执行所有测试
function runAllTests() {
  testAIServiceFixes();
  testVoiceServiceFixes();
  testDataSyncFixes();
  testUserAuthFixes();
  testCloudFunctions();
  testPageIntegrity();
  testConfigFiles();
  
  generateReport();
}

// 运行测试
runAllTests();
