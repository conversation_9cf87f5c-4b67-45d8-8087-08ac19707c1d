// 部署状态检查脚本
// 复制此代码到微信开发者工具的调试控制台中运行

async function checkCloudFunctionStatus() {
  const functions = ['writeRecord', 'userLogin', 'deleteAccount'];
  
  console.log('🔍 检查云函数部署状态...');
  
  for (const funcName of functions) {
    try {
      const result = await wx.cloud.callFunction({
        name: funcName,
        data: { action: 'test' }
      });
      
      console.log(`✅ ${funcName}: 部署成功`);
    } catch (error) {
      console.error(`❌ ${funcName}: 部署失败`, error.errMsg);
      
      if (error.errMsg && error.errMsg.includes('Cannot find module')) {
        console.log(`   → 建议重新部署并选择"云端安装依赖"`);
      }
    }
  }
}

// 运行检查
checkCloudFunctionStatus();