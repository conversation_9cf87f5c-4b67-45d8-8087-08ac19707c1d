#!/usr/bin/env node

/**
 * 图标文件检查脚本
 * 检查所有必需的图标文件是否存在，并提供修复建议
 */

const fs = require('fs');
const path = require('path');

// 读取 app.json 配置
const getRequiredIcons = () => {
  try {
    const appJsonPath = path.join(__dirname, '../app.json');
    const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    const requiredIcons = [];
    
    // 检查 TabBar 图标
    if (appConfig.tabBar && appConfig.tabBar.list) {
      appConfig.tabBar.list.forEach(tab => {
        if (tab.iconPath) requiredIcons.push(tab.iconPath);
        if (tab.selectedIconPath) requiredIcons.push(tab.selectedIconPath);
      });
    }
    
    return requiredIcons;
  } catch (error) {
    console.error('❌ 无法读取 app.json:', error.message);
    return [];
  }
};

// 检查文件是否存在
const checkFileExists = (filePath) => {
  const fullPath = path.join(__dirname, '..', filePath);
  return fs.existsSync(fullPath);
};

// 获取文件大小
const getFileSize = (filePath) => {
  try {
    const fullPath = path.join(__dirname, '..', filePath);
    const stats = fs.statSync(fullPath);
    return stats.size;
  } catch {
    return 0;
  }
};

// 主检查函数
const checkIcons = () => {
  console.log('🔍 检查图标文件状态...\n');
  
  const requiredIcons = getRequiredIcons();
  
  if (requiredIcons.length === 0) {
    console.log('⚠️  未找到需要检查的图标配置');
    return;
  }
  
  let missingIcons = [];
  let tinyIcons = [];
  let validIcons = [];
  
  requiredIcons.forEach(iconPath => {
    const exists = checkFileExists(iconPath);
    
    if (!exists) {
      missingIcons.push(iconPath);
    } else {
      const size = getFileSize(iconPath);
      if (size < 100) { // 小于 100 字节认为是临时图标
        tinyIcons.push({ path: iconPath, size });
      } else {
        validIcons.push({ path: iconPath, size });
      }
    }
  });
  
  // 输出检查结果
  console.log('📊 检查结果:');
  console.log(`✅ 有效图标: ${validIcons.length}`);
  console.log(`⚠️  临时图标: ${tinyIcons.length}`);
  console.log(`❌ 缺失图标: ${missingIcons.length}\n`);
  
  // 详细信息
  if (validIcons.length > 0) {
    console.log('✅ 有效图标:');
    validIcons.forEach(icon => {
      console.log(`   ${icon.path} (${(icon.size / 1024).toFixed(1)}KB)`);
    });
    console.log('');
  }
  
  if (tinyIcons.length > 0) {
    console.log('⚠️  临时图标 (需要替换):');
    tinyIcons.forEach(icon => {
      console.log(`   ${icon.path} (${icon.size}B - 临时文件)`);
    });
    console.log('');
  }
  
  if (missingIcons.length > 0) {
    console.log('❌ 缺失图标:');
    missingIcons.forEach(iconPath => {
      console.log(`   ${iconPath}`);
    });
    console.log('');
  }
  
  // 提供修复建议
  if (missingIcons.length > 0 || tinyIcons.length > 0) {
    console.log('🔧 修复建议:');
    
    if (missingIcons.length > 0) {
      console.log('1. 创建临时图标: pnpm run icons:temp');
    }
    
    if (tinyIcons.length > 0 || missingIcons.length > 0) {
      console.log('2. 生成 SVG 模板: pnpm run icons:generate');
      console.log('3. 将 SVG 转换为 PNG: https://svgtopng.com/');
      console.log('4. 推荐尺寸: TabBar 图标 81x81px');
    }
  } else {
    console.log('🎉 所有图标文件都已就绪！');
  }
};

// 执行检查
checkIcons();