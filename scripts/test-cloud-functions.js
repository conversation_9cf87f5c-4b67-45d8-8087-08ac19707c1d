/**
 * 云函数测试脚本
 * 复制此代码到微信开发者工具的调试控制台中运行
 * 用于测试云函数部署状态和依赖安装情况
 */

async function testCloudFunctions() {
  const functions = [
    {
      name: 'userLogin',
      testData: {
        code: 'test_code',
        userInfo: { nickName: '测试用户' }
      }
    },
    {
      name: 'writeRecord', 
      testData: {
        action: 'test',
        collection: 'test',
        data: { test: true }
      }
    },
    {
      name: 'deleteAccount',
      testData: {}
    }
  ];
  
  console.log('🧪 开始测试云函数...\n');
  
  const results = [];
  
  for (const func of functions) {
    console.log(`📋 测试云函数: ${func.name}`);
    
    try {
      const startTime = Date.now();
      
      const result = await wx.cloud.callFunction({
        name: func.name,
        data: func.testData
      });
      
      const duration = Date.now() - startTime;
      
      console.log(`✅ ${func.name}: 调用成功`);
      console.log(`⏱️  响应时间: ${duration}ms`);
      console.log(`📊 返回结果:`, result.result);
      
      results.push({
        name: func.name,
        status: 'success',
        duration: duration,
        result: result.result
      });
      
    } catch (error) {
      console.error(`❌ ${func.name}: 调用失败`);
      console.error(`🔍 错误详情:`, error);
      
      let analysis = '';
      let solution = '';
      
      if (error.errMsg) {
        if (error.errMsg.includes('Cannot find module \'wx-server-sdk\'')) {
          analysis = '依赖包缺失';
          solution = '运行: node scripts/fix-cloud-functions.js 然后重新部署';
        } else if (error.errMsg.includes('FunctionName parameter could not be found')) {
          analysis = '云函数未部署';
          solution = '在微信开发者工具中右键云函数文件夹选择"上传并部署"';
        } else if (error.errMsg.includes('env check invalid')) {
          analysis = '云开发环境配置问题';
          solution = '检查 project.config.json 中的云开发环境配置';
        } else {
          analysis = '未知错误';
          solution = '检查网络连接和云开发环境状态';
        }
      }
      
      console.log(`💡 问题分析: ${analysis}`);
      console.log(`🔧 解决方案: ${solution}`);
      
      results.push({
        name: func.name,
        status: 'failed',
        error: error.errMsg || error.message,
        analysis: analysis,
        solution: solution
      });
    }
    
    console.log(''); // 空行分隔
  }
  
  // 生成汇总报告
  console.log('📊 测试结果汇总');
  console.log('='.repeat(50));
  
  const successCount = results.filter(r => r.status === 'success').length;
  const failedCount = results.filter(r => r.status === 'failed').length;
  
  results.forEach(result => {
    const statusIcon = result.status === 'success' ? '✅' : '❌';
    const statusText = result.status === 'success' ? '正常' : '失败';
    
    console.log(`${statusIcon} ${result.name}: ${statusText}`);
    
    if (result.status === 'success' && result.duration) {
      console.log(`   响应时间: ${result.duration}ms`);
    } else if (result.status === 'failed') {
      console.log(`   问题: ${result.analysis}`);
      console.log(`   解决: ${result.solution}`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`📈 成功: ${successCount}/${functions.length}`);
  console.log(`📉 失败: ${failedCount}/${functions.length}`);
  
  if (successCount === functions.length) {
    console.log('🎉 所有云函数工作正常！');
    console.log('💡 可以启用云同步功能');
    
    // 自动启用云同步
    try {
      const app = getApp();
      if (app && app.dataSyncManager) {
        app.dataSyncManager.enableCloudSync();
        console.log('✅ 已自动启用云同步功能');
      }
    } catch (e) {
      console.log('⚠️  请手动启用云同步功能');
    }
    
  } else {
    console.log('⚠️  存在问题，请按照上述解决方案进行修复');
    console.log('📖 详细修复指南: docs/云函数故障排除指南.md');
    
    // 生成修复建议
    const fixes = results
      .filter(r => r.status === 'failed')
      .map(r => `• ${r.name}: ${r.solution}`)
      .join('\n');
      
    console.log('\n🔧 修复建议:');
    console.log(fixes);
  }
  
  return results;
}

// 快速测试单个云函数
async function quickTestFunction(funcName) {
  console.log(`🚀 快速测试: ${funcName}`);
  
  try {
    const result = await wx.cloud.callFunction({
      name: funcName,
      data: { action: 'test', timestamp: Date.now() }
    });
    
    console.log(`✅ ${funcName} 测试成功:`, result.result);
    return true;
  } catch (error) {
    console.error(`❌ ${funcName} 测试失败:`, error.errMsg);
    return false;
  }
}

// 检查云开发环境
async function checkCloudEnvironment() {
  console.log('🌐 检查云开发环境...');
  
  try {
    await wx.cloud.database().collection('_test_').limit(1).get();
    console.log('✅ 云开发环境连接正常');
    return true;
  } catch (error) {
    console.error('❌ 云开发环境连接失败:', error.errMsg);
    
    if (error.errMsg && error.errMsg.includes('env check invalid')) {
      console.log('💡 解决方案: 检查 project.config.json 中的云开发环境配置');
    }
    
    return false;
  }
}

// 导出功能供外部调用
window.testCloudFunctions = testCloudFunctions;
window.quickTestFunction = quickTestFunction;
window.checkCloudEnvironment = checkCloudEnvironment;

console.log('🧪 云函数测试工具已加载');
console.log('📋 可用命令:');
console.log('• testCloudFunctions() - 完整测试所有云函数');
console.log('• quickTestFunction("writeRecord") - 快速测试指定云函数');  
console.log('• checkCloudEnvironment() - 检查云开发环境连接');

// 自动运行完整测试
console.log('\n🚀 自动开始完整测试...');
testCloudFunctions();