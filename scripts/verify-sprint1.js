#!/usr/bin/env node

/**
 * Sprint 1 功能验证脚本
 * 检查用户管理 & 登录体系的完整性
 */

const fs = require('fs');
const path = require('path');

class Sprint1Verifier {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.successes = [];
  }

  log(type, message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    
    switch(type) {
      case 'error':
        this.errors.push(message);
        console.error(`❌ ${message}`);
        break;
      case 'warning':
        this.warnings.push(message);
        console.warn(`⚠️  ${message}`);
        break;
      case 'success':
        this.successes.push(message);
        console.log(`✅ ${message}`);
        break;
      default:
        console.log(`ℹ️  ${message}`);
    }
  }

  checkFileExists(filePath, description) {
    if (fs.existsSync(filePath)) {
      this.log('success', `${description}: ${filePath}`);
      return true;
    } else {
      this.log('error', `缺少文件 ${description}: ${filePath}`);
      return false;
    }
  }

  checkFileContent(filePath, requiredContent, description) {
    if (!fs.existsSync(filePath)) {
      this.log('error', `文件不存在: ${filePath}`);
      return false;
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasContent = requiredContent.every(item => content.includes(item));
      
      if (hasContent) {
        this.log('success', `${description}: 内容检查通过`);
        return true;
      } else {
        const missing = requiredContent.filter(item => !content.includes(item));
        this.log('error', `${description}: 缺少内容 ${missing.join(', ')}`);
        return false;
      }
    } catch (error) {
      this.log('error', `读取文件失败 ${filePath}: ${error.message}`);
      return false;
    }
  }

  verifyLoginPages() {
    console.log('\n🔍 检查登录页面...');
    
    // 检查登录页面文件
    const loginFiles = [
      ['pages/login/login.js', '登录页面逻辑'],
      ['pages/login/login.wxml', '登录页面模板'],
      ['pages/login/login.wxss', '登录页面样式'],
      ['pages/login/login.json', '登录页面配置']
    ];

    loginFiles.forEach(([file, desc]) => {
      this.checkFileExists(file, desc);
    });

    // 检查登录逻辑
    this.checkFileContent('pages/login/login.js', [
      'handleLogin',
      'cloudLogin',
      'fallbackLogin',
      'handleGuestMode'
    ], '登录页面功能');
  }

  verifyUserProfile() {
    console.log('\n🔍 检查用户中心页面...');
    
    const profileFiles = [
      ['pages/profile/profile.js', '用户中心逻辑'],
      ['pages/profile/profile.wxml', '用户中心模板'],
      ['pages/profile/profile.wxss', '用户中心样式'],
      ['pages/profile/profile.json', '用户中心配置']
    ];

    profileFiles.forEach(([file, desc]) => {
      this.checkFileExists(file, desc);
    });

    // 检查用户中心功能
    this.checkFileContent('pages/profile/profile.js', [
      'loadUserData',
      'logout',
      'deleteAccount',
      'manageSync'
    ], '用户中心功能');
  }

  verifyCloudFunctions() {
    console.log('\n🔍 检查云函数...');
    
    const cloudFunctions = [
      ['cloudfunctions/userLogin/index.js', '用户登录云函数'],
      ['cloudfunctions/deleteAccount/index.js', '用户注销云函数']
    ];

    cloudFunctions.forEach(([file, desc]) => {
      this.checkFileExists(file, desc);
    });

    // 检查云函数内容
    this.checkFileContent('cloudfunctions/userLogin/index.js', [
      'cloud.getWXContext',
      'db.collection',
      'OPENID'
    ], '登录云函数功能');

    this.checkFileContent('cloudfunctions/deleteAccount/index.js', [
      'cloud.getWXContext',
      'db.collection',
      'remove'
    ], '注销云函数功能');
  }

  verifyDataIsolation() {
    console.log('\n🔍 检查数据隔离机制...');
    
    // 检查全局用户管理
    this.checkFileContent('app.js', [
      'setLoginStatus',
      'clearLoginStatus',
      'getUserQuery',
      'requireLogin'
    ], 'App.js用户管理功能');

    // 检查认证工具
    this.checkFileExists('utils/auth.js', '认证工具函数');
    
    if (fs.existsSync('utils/auth.js')) {
      this.checkFileContent('utils/auth.js', [
        'isLoggedIn',
        'getCurrentUserId',
        'getUserQuery',
        'checkPermission'
      ], '认证工具功能');
    }

    // 检查页面数据隔离
    const pages = ['index', 'finances', 'transactions'];
    pages.forEach(page => {
      const filePath = `pages/${page}/${page}.js`;
      if (fs.existsSync(filePath)) {
        // 针对不同页面使用不同的检查内容
        let requiredFunctions = [];
        
        if (page === 'index') {
          requiredFunctions = ['checkLoginStatus', 'loadUserData'];
        } else if (page === 'finances') {
          requiredFunctions = ['checkUserAuth', 'loadFinanceData'];
        } else if (page === 'transactions') {
          requiredFunctions = ['checkUserAuth', 'loadTransactionData'];
        }
        
        this.checkFileContent(filePath, requiredFunctions, `${page}页面数据隔离`);
      }
    });
  }

  verifyAppConfig() {
    console.log('\n🔍 检查应用配置...');
    
    this.checkFileExists('app.json', '应用配置文件');
    
    if (fs.existsSync('app.json')) {
      try {
        const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'));
        
        // 检查登录页是否在首位
        if (appConfig.pages && appConfig.pages[0] === 'pages/login/login') {
          this.log('success', '登录页面已设置为首页');
        } else {
          this.log('warning', '登录页面不是首页，可能影响用户体验');
        }

        // 检查必要页面
        const requiredPages = [
          'pages/login/login',
          'pages/profile/profile',
          'pages/index/index'
        ];

        const missingPages = requiredPages.filter(page => 
          !appConfig.pages.includes(page)
        );

        if (missingPages.length === 0) {
          this.log('success', '所有必要页面已配置');
        } else {
          this.log('error', `缺少页面配置: ${missingPages.join(', ')}`);
        }

      } catch (error) {
        this.log('error', `解析app.json失败: ${error.message}`);
      }
    }
  }

  generateReport() {
    console.log('\n📊 Sprint 1 验证报告');
    console.log('='.repeat(50));
    
    console.log(`\n✅ 成功项目: ${this.successes.length}`);
    console.log(`⚠️  警告项目: ${this.warnings.length}`);
    console.log(`❌ 错误项目: ${this.errors.length}`);

    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告列表:');
      this.warnings.forEach(warning => console.log(`   - ${warning}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.errors.forEach(error => console.log(`   - ${error}`));
    }

    const totalChecks = this.successes.length + this.warnings.length + this.errors.length;
    const successRate = Math.round((this.successes.length / totalChecks) * 100);
    
    console.log(`\n📈 总体完成度: ${successRate}%`);
    
    if (this.errors.length === 0) {
      console.log('\n🎉 Sprint 1 验证通过！所有核心功能已实现。');
    } else {
      console.log('\n⚠️  Sprint 1 存在问题，请修复上述错误后重新验证。');
    }

    return this.errors.length === 0;
  }

  run() {
    console.log('🚀 开始验证 Sprint 1: 用户管理 & 登录体系');
    console.log('='.repeat(50));

    // 执行各项验证
    this.verifyLoginPages();
    this.verifyUserProfile();
    this.verifyCloudFunctions();
    this.verifyDataIsolation();
    this.verifyAppConfig();

    // 生成报告
    const success = this.generateReport();
    
    process.exit(success ? 0 : 1);
  }
}

// 执行验证
if (require.main === module) {
  const verifier = new Sprint1Verifier();
  verifier.run();
}

module.exports = Sprint1Verifier; 