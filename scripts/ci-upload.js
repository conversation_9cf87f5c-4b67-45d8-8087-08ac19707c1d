#!/usr/bin/env node
/**
 * CI 上传脚本
 * 用于 GitHub Actions 自动上传小程序
 */

const ci = require('miniprogram-ci');
const fs = require('fs');
const path = require('path');

async function upload() {
  try {
    console.log('🚀 开始上传小程序...');
    
    // 读取项目配置
    const projectConfig = JSON.parse(fs.readFileSync('project.config.json', 'utf8'));
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 检查私钥文件
    const privateKeyPath = path.resolve('./private.key');
    if (!fs.existsSync(privateKeyPath)) {
      throw new Error('私钥文件不存在，请检查 MINIPROGRAM_PRIVATE_KEY 环境变量');
    }
    
    // 创建项目实例
    const project = new ci.Project({
      appid: projectConfig.appid,
      type: 'miniProgram',
      projectPath: path.resolve('./'),
      privateKeyPath: privateKeyPath,
      ignores: ['node_modules/**/*'],
    });
    
    // 获取版本号和描述
    const version = packageJson.version || '1.0.0';
    const desc = `自动构建 ${version} - ${new Date().toLocaleString('zh-CN')}`;
    
    // 执行上传
    const uploadResult = await ci.upload({
      project,
      version,
      desc,
      setting: {
        es6: true,
        minify: true,
        autoPrefixWXSS: true,
      },
      onProgressUpdate: (progress) => {
        console.log(`上传进度: ${progress}%`);
      },
    });
    
    console.log('✅ 上传成功！');
    console.log('版本:', version);
    console.log('大小:', uploadResult.packageSize);
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
    process.exit(1);
  }
}

// 如果私钥存在则执行上传，否则跳过
if (fs.existsSync('./private.key')) {
  upload();
} else {
  console.log('⚠️  私钥文件不存在，跳过上传');
  console.log('💡 请设置 MINIPROGRAM_PRIVATE_KEY 环境变量');
}