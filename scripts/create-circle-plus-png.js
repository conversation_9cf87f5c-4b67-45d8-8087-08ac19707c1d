#!/usr/bin/env node

/**
 * 创建底部导航图标PNG文件
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// 输入图标 - 键盘设计
const createInputSVG = (isSelected = false) => {
  const color = isSelected ? '#5a5fcf' : '#667eea';
  return `
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="40.5" r="35" fill="${color}"/>
  <rect x="20.25" y="27" width="40.5" height="27" rx="3.375" fill="none" stroke="white" stroke-width="4"/>
  <line x1="27" y1="33.75" x2="27" y2="33.75" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="33.75" y1="33.75" x2="33.75" y2="33.75" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="40.5" y1="33.75" x2="40.5" y2="33.75" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="47.25" y1="33.75" x2="47.25" y2="33.75" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="54" y1="33.75" x2="54" y2="33.75" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="27" y1="40.5" x2="33.75" y2="40.5" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="40.5" y1="40.5" x2="54" y2="40.5" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="33.75" y1="47.25" x2="47.25" y2="47.25" stroke="white" stroke-width="4" stroke-linecap="round"/>
</svg>`;
};

// 财务图标 - 钱币设计
const createFinanceSVG = (isSelected = false) => {
  const color = isSelected ? '#5a5fcf' : '#667eea';
  return `
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="40.5" r="35" fill="${color}"/>
  <circle cx="40.5" cy="40.5" r="16.875" fill="none" stroke="white" stroke-width="6"/>
  <path d="M35.4375 32.0625C35.4375 30.203125 36.796875 28.6875 38.8125 28.6875H42.1875C44.203125 28.6875 45.5625 30.203125 45.5625 32.0625C45.5625 33.921875 44.203125 35.4375 42.1875 35.4375H38.8125C36.796875 35.4375 35.4375 38.1075 35.4375 40.5C35.4375 42.359375 36.796875 43.875 38.8125 43.875H42.1875C44.203125 43.875 45.5625 45.39075 45.5625 47.25C45.5625 49.109375 44.203125 50.625 42.1875 50.625H38.8125C36.796875 50.625 35.4375 49.109375 35.4375 47.25" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="40.5" y1="21.9375" x2="40.5" y2="28.6875" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="40.5" y1="52.3125" x2="40.5" y2="59.0625" stroke="white" stroke-width="4" stroke-linecap="round"/>
</svg>`;
};

// 圆形加号SVG（日程和待办使用）
const createCirclePlusSVG = (isSelected = false) => {
  const color = isSelected ? '#5a5fcf' : '#667eea';
  return `
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="40.5" r="35" fill="${color}"/>
  <path d="M40.5 25 L40.5 56" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <path d="M25 40.5 L56 40.5" stroke="white" stroke-width="4" stroke-linecap="round"/>
</svg>`;
};

// 日历图标 - 日历设计
const createCalendarSVG = (isSelected = false) => {
  const color = isSelected ? '#5a5fcf' : '#667eea';
  return `
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="40.5" r="35" fill="${color}"/>
  <rect x="27" y="23.625" width="27" height="30.375" rx="3.375" fill="none" stroke="white" stroke-width="4"/>
  <line x1="33.75" y1="16.875" x2="33.75" y2="30.375" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="47.25" y1="16.875" x2="47.25" y2="30.375" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="27" y1="33.75" x2="54" y2="33.75" stroke="white" stroke-width="4"/>
  <circle cx="33.75" cy="40.5" r="1.6875" fill="white"/>
  <circle cx="40.5" cy="40.5" r="1.6875" fill="white"/>
  <circle cx="47.25" cy="40.5" r="1.6875" fill="white"/>
  <circle cx="33.75" cy="47.25" r="1.6875" fill="white"/>
  <circle cx="40.5" cy="47.25" r="1.6875" fill="white"/>
</svg>`;
};

// 待办图标 - 复选框设计
const createTaskSVG = (isSelected = false) => {
  const color = isSelected ? '#5a5fcf' : '#667eea';
  return `
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="40.5" r="35" fill="${color}"/>
  <rect x="23.625" y="27" width="33.75" height="27" rx="3.375" fill="none" stroke="white" stroke-width="4"/>
  <path d="M30.375 37.125L37.125 43.875L50.625 30.375" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;
};

// 生成PNG文件
const generatePNGIcons = async () => {
  const iconsDir = path.join(__dirname, '../assets/icons');
  
  console.log('🎨 生成底部导航PNG图标...');
  
  try {
    // 生成输入图标
    const inputNormalSVG = createInputSVG(false);
    await sharp(Buffer.from(inputNormalSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'input.png'));
    console.log('✅ 生成 input.png');
    
    const inputSelectedSVG = createInputSVG(true);
    await sharp(Buffer.from(inputSelectedSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'input_selected.png'));
    console.log('✅ 生成 input_selected.png');
    
    // 生成财务图标
    const financeNormalSVG = createFinanceSVG(false);
    await sharp(Buffer.from(financeNormalSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'profile.png'));
    console.log('✅ 生成 profile.png');
    
    const financeSelectedSVG = createFinanceSVG(true);
    await sharp(Buffer.from(financeSelectedSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'profile_selected.png'));
    console.log('✅ 生成 profile_selected.png');
    
    // 生成日程图标
    const calendarNormalSVG = createCalendarSVG(false);
    await sharp(Buffer.from(calendarNormalSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'calendar.png'));
    console.log('✅ 生成 calendar.png');
    
    const calendarSelectedSVG = createCalendarSVG(true);
    await sharp(Buffer.from(calendarSelectedSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'calendar_selected.png'));
    console.log('✅ 生成 calendar_selected.png');
    
    // 生成待办图标
    const taskNormalSVG = createTaskSVG(false);
    await sharp(Buffer.from(taskNormalSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'task.png'));
    console.log('✅ 生成 task.png');
    
    const taskSelectedSVG = createTaskSVG(true);
    await sharp(Buffer.from(taskSelectedSVG))
      .png({ quality: 100 })
      .toFile(path.join(iconsDir, 'task_selected.png'));
    console.log('✅ 生成 task_selected.png');
    
    console.log('🎉 所有底部导航图标生成完成！');
  } catch (error) {
    console.error('❌ 生成PNG失败:', error.message);
  }
};

generatePNGIcons(); 