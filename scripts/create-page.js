#!/usr/bin/env node
/**
 * 新页面创建脚本
 * 用法: npm run page:new <pageName>
 */

const fs = require('fs');
const path = require('path');

const pageName = process.argv[2];

if (!pageName) {
  console.error('❌ 请提供页面名称: npm run page:new <pageName>');
  process.exit(1);
}

const pagesDir = 'pages';
const pageDir = path.join(pagesDir, pageName);

// 检查页面是否已存在
if (fs.existsSync(pageDir)) {
  console.error(`❌ 页面 ${pageName} 已存在`);
  process.exit(1);
}

// 创建页面目录
fs.mkdirSync(pageDir, { recursive: true });

// 创建页面文件
const files = {
  [`${pageName}.wxml`]: `<!--pages/${pageName}/${pageName}.wxml-->
<view class="container">
  <text class="title">{{title}}</text>
</view>`,
  
  [`${pageName}.js`]: `// pages/${pageName}/${pageName}.js
Page({
  data: {
    title: '${pageName} 页面'
  },

  onLoad() {
    console.log('${pageName} 页面加载');
  }
});`,

  [`${pageName}.wxss`]: `/* pages/${pageName}/${pageName}.wxss */
.container {
  padding: 32rpx;
}

.title {
  font-size: 32rpx;
  color: #333;
}`,

  [`${pageName}.json`]: `{
  "usingComponents": {
    "t-button": "tdesign-miniprogram/button/button",
    "t-icon": "tdesign-miniprogram/icon/icon"
  },
  "navigationBarTitleText": "${pageName}"
}`
};

// 写入文件
Object.entries(files).forEach(([filename, content]) => {
  const filePath = path.join(pageDir, filename);
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ 创建文件: ${filePath}`);
});

console.log(`🎉 页面 ${pageName} 创建成功！`);
console.log(`💡 请手动将页面路径添加到 app.json 的 pages 数组中：`);
console.log(`   "pages/${pageName}/${pageName}"`);