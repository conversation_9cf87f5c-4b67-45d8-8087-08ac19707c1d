#!/usr/bin/env node
/**
 * Starter-Auto 初始化脚本
 * 用于拉取 TDesign Demo 和初始化开发环境
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 初始化 Starter-Auto 开发环境...');

// 检查必要的依赖
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = ['tdesign-miniprogram'];

console.log('✅ 检查依赖包...');
requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`  ✓ ${dep} 已安装`);
  } else {
    console.log(`  ✗ ${dep} 未安装`);
  }
});

// 检查项目配置
console.log('✅ 检查项目配置...');
const configFiles = [
  'app.json',
  'project.config.json',
  'sitemap.json'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✓ ${file} 存在`);
  } else {
    console.log(`  ✗ ${file} 缺失`);
  }
});

// 检查页面结构
console.log('✅ 检查页面结构...');
const pagesDir = 'pages';
if (fs.existsSync(pagesDir)) {
  const pages = fs.readdirSync(pagesDir);
  console.log(`  ✓ 发现 ${pages.length} 个页面: ${pages.join(', ')}`);
} else {
  console.log(`  ✗ pages 目录不存在`);
}

console.log('✅ Starter-Auto 初始化完成！');
console.log('💡 下一步: 运行 npm run tdesign:build 构建 TDesign 组件');