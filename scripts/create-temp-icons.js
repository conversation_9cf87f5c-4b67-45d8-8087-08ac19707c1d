#!/usr/bin/env node

/**
 * 创建临时图标文件（Base64 编码的最小 PNG）
 * 用于开发阶段，避免图标缺失导致的编译错误
 */

const fs = require('fs');
const path = require('path');

// 最小的透明 PNG 图标 (1x1 像素，Base64 编码)
const minimalPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

// 创建临时图标文件
const createTempIcons = () => {
  const iconsDir = path.join(__dirname, '../assets/icons');
  
  // 确保目录存在
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }
  
  // 需要创建的图标文件
  const iconFiles = [
    'input.png',
    'input_selected.png',
    'calendar.png', 
    'calendar_selected.png',
    'task.png',
    'task_selected.png',
    'profile.png',
    'profile_selected.png'
  ];
  
  console.log('🔧 创建临时图标文件...');
  
  iconFiles.forEach(filename => {
    const filePath = path.join(iconsDir, filename);
    const buffer = Buffer.from(minimalPNG, 'base64');
    fs.writeFileSync(filePath, buffer);
    console.log(`✅ 创建临时图标: ${filename}`);
  });
  
  console.log('🎉 临时图标创建完成！');
  console.log('⚠️  这些是最小的透明图标，仅用于开发测试');
  console.log('📝 请稍后使用设计工具创建正式图标');
};

createTempIcons();