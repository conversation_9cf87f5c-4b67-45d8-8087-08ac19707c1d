#!/usr/bin/env node
/**
 * 主题色设置脚本
 * 用法: npm run theme:set #1296db
 */

const fs = require('fs');
const path = require('path');

const newColor = process.argv[2];

if (!newColor || !newColor.match(/^#[0-9a-fA-F]{6}$/)) {
  console.error('❌ 请提供有效的十六进制颜色值: npm run theme:set #RRGGBB');
  process.exit(1);
}

console.log(`🎨 设置主题色为: ${newColor}`);

// 更新 app.wxss 中的主色调
const appWxssPath = 'app.wxss';
if (fs.existsSync(appWxssPath)) {
  let content = fs.readFileSync(appWxssPath, 'utf8');
  
  // 替换 CSS 变量
  content = content.replace(
    /--td-brand-color:\s*#[0-9a-fA-F]{6};/g,
    `--td-brand-color: ${newColor};`
  );
  
  content = content.replace(
    /--primary-color:\s*#[0-9a-fA-F]{6};/g,
    `--primary-color: ${newColor};`
  );
  
  fs.writeFileSync(appWxssPath, content, 'utf8');
  console.log('✅ 已更新 app.wxss 中的主题色');
}

// 更新 project.config.json 中的主题色配置
const projectConfigPath = 'project.config.json';
if (fs.existsSync(projectConfigPath)) {
  const config = JSON.parse(fs.readFileSync(projectConfigPath, 'utf8'));
  
  if (!config.setting) config.setting = {};
  if (!config.setting.themeConfig) config.setting.themeConfig = {};
  
  config.setting.themeConfig.primaryColor = newColor;
  
  fs.writeFileSync(projectConfigPath, JSON.stringify(config, null, 2), 'utf8');
  console.log('✅ 已更新 project.config.json 中的主题配置');
}

console.log('🎉 主题色设置完成！');
console.log('💡 请重新编译小程序以应用新主题');