#!/usr/bin/env node
/**
 * 云函数依赖修复脚本
 * 用于解决 "Cannot find module 'wx-server-sdk'" 问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const cloudFunctions = [
  'userLogin',
  'writeRecord', 
  'deleteAccount'
];

console.log('🔧 开始修复云函数依赖问题...\n');

// 检查项目根目录
const projectRoot = path.join(__dirname, '..');
const cloudfunctionsDir = path.join(projectRoot, 'cloudfunctions');

if (!fs.existsSync(cloudfunctionsDir)) {
  console.error('❌ cloudfunctions 目录不存在');
  process.exit(1);
}

// 修复每个云函数
for (const funcName of cloudFunctions) {
  console.log(`🛠️  修复云函数: ${funcName}`);
  
  const funcDir = path.join(cloudfunctionsDir, funcName);
  
  if (!fs.existsSync(funcDir)) {
    console.warn(`⚠️  ${funcName} 目录不存在，跳过`);
    continue;
  }
  
  try {
    // 进入函数目录
    process.chdir(funcDir);
    console.log(`📁 进入目录: ${funcDir}`);
    
    // 检查 package.json
    const packageJsonPath = path.join(funcDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      console.log(`📦 创建 package.json`);
      
      const packageJson = {
        "name": funcName,
        "version": "1.0.0",
        "description": `${funcName} 云函数`,
        "main": "index.js",
        "dependencies": {
          "wx-server-sdk": "~2.6.3"
        },
        "author": "一句话全能助手",
        "license": "MIT"
      };
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    }
    
    // 删除旧的 node_modules 和 package-lock.json（如果存在）
    const nodeModulesPath = path.join(funcDir, 'node_modules');
    const lockFilePath = path.join(funcDir, 'package-lock.json');
    
    if (fs.existsSync(nodeModulesPath)) {
      console.log(`🗑️  删除旧的 node_modules`);
      execSync('rm -rf node_modules', { stdio: 'inherit' });
    }
    
    if (fs.existsSync(lockFilePath)) {
      console.log(`🗑️  删除旧的 package-lock.json`);
      fs.unlinkSync(lockFilePath);
    }
    
    // 安装依赖
    console.log(`📦 安装依赖包...`);
    execSync('npm install', { stdio: 'inherit' });
    
    // 验证安装
    const wxSdkPath = path.join(funcDir, 'node_modules', 'wx-server-sdk');
    if (fs.existsSync(wxSdkPath)) {
      console.log(`✅ wx-server-sdk 安装成功`);
      
      // 检查版本
      const wxSdkPackage = path.join(wxSdkPath, 'package.json');
      if (fs.existsSync(wxSdkPackage)) {
        const packageData = JSON.parse(fs.readFileSync(wxSdkPackage, 'utf8'));
        console.log(`📋 版本: ${packageData.version}`);
      }
    } else {
      console.error(`❌ wx-server-sdk 安装失败`);
    }
    
    console.log(`✅ ${funcName} 修复完成\n`);
    
  } catch (error) {
    console.error(`❌ ${funcName} 修复失败:`, error.message);
    console.log(`💡 建议手动处理: cd cloudfunctions/${funcName} && npm install\n`);
  }
}

// 返回项目根目录
process.chdir(projectRoot);

console.log('🎉 云函数依赖修复完成！');
console.log('\n📋 下一步操作：');
console.log('1. 重启微信开发者工具');
console.log('2. 进入"云开发"控制台');
console.log('3. 逐个右键云函数文件夹');
console.log('4. 选择"上传并部署（云端安装依赖）"');
console.log('5. 等待部署完成后测试功能');

console.log('\n🔍 验证脚本：');
console.log('可以运行以下脚本验证修复效果：');
console.log('node scripts/test-cloud-functions.js');