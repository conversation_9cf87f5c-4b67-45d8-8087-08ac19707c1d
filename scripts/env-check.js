#!/usr/bin/env node
/**
 * 环境检查脚本
 * 检查开发环境是否满足产品需求文档要求
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 环境检查 - Sprint 0 验收');
console.log('=' .repeat(50));

let checkCount = 0;
let passCount = 0;

function check(name, condition, suggestion = '') {
  checkCount++;
  if (condition) {
    console.log(`✅ ${name}`);
    passCount++;
  } else {
    console.log(`❌ ${name}`);
    if (suggestion) {
      console.log(`   💡 ${suggestion}`);
    }
  }
}

// 1. 检查 Node.js 版本
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
  check('Node.js >= 20', majorVersion >= 20, `当前版本: ${nodeVersion}, 需要 >= 20.0.0`);
} catch (error) {
  check('Node.js 安装', false, 'Node.js 未安装或不在 PATH 中');
}

// 2. 检查 pnpm 版本
try {
  const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
  const majorVersion = parseInt(pnpmVersion.split('.')[0]);
  check('pnpm >= 9', majorVersion >= 9, `当前版本: ${pnpmVersion}, 需要 >= 9.0.0`);
} catch (error) {
  check('pnpm 安装', false, '请安装 pnpm: npm install -g pnpm');
}

// 3. 检查项目配置文件
const configFiles = [
  'package.json',
  'app.json', 
  'project.config.json',
  'sitemap.json'
];

configFiles.forEach(file => {
  check(`配置文件 ${file}`, fs.existsSync(file), `缺少 ${file} 文件`);
});

// 4. 检查依赖安装
const nodeModulesExists = fs.existsSync('node_modules');
check('依赖已安装', nodeModulesExists, '运行: pnpm install');

if (nodeModulesExists) {
  const tdesignExists = fs.existsSync('node_modules/tdesign-miniprogram');
  check('TDesign 组件库', tdesignExists, '运行: pnpm install tdesign-miniprogram');
}

// 5. 检查页面结构
const requiredPages = ['index', 'transactions', 'finances', 'add-task', 'confirm'];
requiredPages.forEach(page => {
  const pageDir = `pages/${page}`;
  const pageFiles = [
    `${pageDir}/${page}.wxml`,
    `${pageDir}/${page}.js`,
    `${pageDir}/${page}.wxss`,
    `${pageDir}/${page}.json`
  ];
  
  const allFilesExist = pageFiles.every(file => fs.existsSync(file));
  check(`页面 ${page}`, allFilesExist, `缺少页面文件: ${pageDir}`);
});

// 6. 检查脚本工具
const scripts = ['starter-init.js', 'create-page.js', 'tdesign-build.js', 'ci-upload.js'];
scripts.forEach(script => {
  check(`脚本 ${script}`, fs.existsSync(`scripts/${script}`), `缺少工具脚本`);
});

// 7. 检查 CI/CD 配置
check('GitHub Actions', fs.existsSync('.github/workflows/ci.yml'), '缺少 CI/CD 配置');

// 8. 检查图标资源
const iconDir = 'assets/icons';
check('图标目录', fs.existsSync(iconDir), '运行: npm run icons:temp');

console.log('');
console.log('=' .repeat(50));
console.log(`📊 检查结果: ${passCount}/${checkCount} 项通过`);

if (passCount === checkCount) {
  console.log('🎉 环境检查全部通过！项目已准备就绪');
  console.log('');
  console.log('📋 下一步操作:');
  console.log('1. 在微信开发者工具中打开项目');
  console.log('2. 点击 工具 → 构建 npm');
  console.log('3. 编译并预览项目');
  console.log('4. 开始 Sprint 1: 用户管理 & 登录');
} else {
  console.log('⚠️  请修复上述问题后重新检查');
  process.exit(1);
}