#!/usr/bin/env node
/**
 * TDesign 构建脚本
 * 检查 TDesign 组件配置并提供构建指导
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 TDesign 构建检查...');

// 检查 node_modules 中的 TDesign
const tdesignPath = 'node_modules/tdesign-miniprogram';
if (fs.existsSync(tdesignPath)) {
  console.log('✅ TDesign 组件库已安装');
  
  // 检查组件列表
  const componentsPath = path.join(tdesignPath, 'miniprogram_dist');
  if (fs.existsSync(componentsPath)) {
    const components = fs.readdirSync(componentsPath);
    console.log(`✅ 可用组件: ${components.length} 个`);
    console.log(`   主要组件: button, icon, cell, input, textarea, popup 等`);
  }
} else {
  console.log('❌ TDesign 组件库未安装');
  console.log('💡 请运行: pnpm install tdesign-miniprogram');
  process.exit(1);
}

// 检查微信开发者工具配置
const projectConfig = 'project.config.json';
if (fs.existsSync(projectConfig)) {
  const config = JSON.parse(fs.readFileSync(projectConfig, 'utf8'));
  
  // 检查 NPM 构建配置
  if (config.setting && config.setting.packNpmManually) {
    console.log('✅ NPM 手动构建已启用');
    
    if (config.setting.packNpmRelationList) {
      console.log('✅ NPM 构建关系配置正确');
    }
  }
  
  // 检查组件配置
  if (config.setting && config.setting.nodeModules) {
    console.log('✅ Node modules 支持已启用');
  }
}

console.log('');
console.log('📋 TDesign 构建步骤:');
console.log('1. 打开微信开发者工具');
console.log('2. 导入当前项目');
console.log('3. 点击菜单: 工具 → 构建 npm');
console.log('4. 等待构建完成');
console.log('5. 点击编译运行');
console.log('');
console.log('✅ TDesign 构建检查完成！');