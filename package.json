{"name": "assistant-miniprogram", "version": "0.1.0", "description": "微信小程序助手应用", "private": true, "scripts": {"dev": "miniprogram-ci preview --pp ./", "build": "pnpm run build:npm && miniprogram-ci upload --pp ./", "build:npm": "node scripts/build-tdesign.js", "starter:init": "node scripts/starter-init.js", "env:check": "node scripts/env-check.js", "page:new": "node scripts/create-page.js", "tdesign:build": "node scripts/build-tdesign.js", "theme:set": "node scripts/set-theme.js", "ci:upload": "miniprogram-ci upload --pp ./", "lint": "echo 'Linting...'", "test": "echo 'Testing...'", "icons:generate": "node scripts/generate-icons.js", "icons:temp": "node scripts/create-temp-icons.js", "icons:check": "node scripts/check-icons.js", "db:init": "node scripts/init-database.js", "cloud:check": "node scripts/deploy-cloud-functions.js", "cloud:install": "cd cloudfunctions/writeRecord && npm install && cd ../userLogin && npm install && cd ../deleteAccount && npm install", "cloud:clean": "rm -rf cloudfunctions/*/node_modules cloudfunctions/*/package-lock.json", "cloud:reinstall": "npm run cloud:clean && npm run cloud:install", "cloud:deploy": "echo '请在微信开发者工具中右键云函数目录，选择\"上传并部署（云端安装依赖）\"'", "cloud:test": "echo '云函数测试：请在开发者工具中进行云函数调试'"}, "keywords": ["miniprogram", "assistant", "wechat"], "author": "", "license": "UNLICENSED", "devDependencies": {"miniprogram-ci": "^1.9.8", "sharp": "^0.34.2"}, "dependencies": {"tdesign-miniprogram": "^1.9.4"}, "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}}