// utils/testDataInitializer.js
// 测试数据初始化工具

class TestDataInitializer {
  constructor() {
    this.userId = 'test_user_123';
  }

  // 初始化所有测试数据
  async initAllTestData() {
    console.log('🧪 开始初始化测试数据...');
    
    try {
      // 清除现有数据
      this.clearExistingData();
      
      // 创建测试数据
      await this.createScheduleTestData();
      await this.createExpenseTestData();
      await this.createTodoTestData();
      
      console.log('✅ 测试数据初始化完成');
      
      // 显示数据统计
      this.showDataSummary();
      
      return true;
    } catch (error) {
      console.error('❌ 测试数据初始化失败:', error);
      return false;
    }
  }

  // 清除现有数据
  clearExistingData() {
    console.log('🗑️ 清除现有测试数据...');
    
    try {
      wx.removeStorageSync('local_schedules');
      wx.removeStorageSync('local_expenses');
      wx.removeStorageSync('local_todos');
      console.log('✅ 现有数据已清除');
    } catch (error) {
      console.warn('⚠️ 清除数据时出现警告:', error);
    }
  }

  // 创建日程测试数据
  async createScheduleTestData() {
    console.log('📅 创建日程测试数据...');
    
    const schedules = [
      {
        _id: 'schedule_test_001',
        type: 'schedule',
        title: '产品评审会议',
        description: '讨论Q2产品规划和功能优先级',
        date: '2025-06-17',
        time: '10:00',
        location: '会议室A',
        participants: ['张三', '李四', '王五'],
        priority: 'high',
        category: '工作',
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'schedule_test_002',
        type: 'schedule',
        title: '客户拜访',
        description: '拜访重要客户，讨论合作细节',
        date: '2025-06-18',
        time: '14:30',
        location: '客户办公室',
        participants: ['客户经理'],
        priority: 'high',
        category: '商务',
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'schedule_test_003',
        type: 'schedule',
        title: '团队建设活动',
        description: '部门团建，增进团队协作',
        date: '2025-06-19',
        time: '15:00',
        location: '户外拓展基地',
        participants: ['全体同事'],
        priority: 'medium',
        category: '团建',
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'schedule_test_004',
        type: 'schedule',
        title: '技术分享会',
        description: 'AI技术在产品中的应用实践',
        date: '2025-06-20',
        time: '16:00',
        location: '大会议室',
        participants: ['技术团队'],
        priority: 'medium',
        category: '学习',
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'schedule_test_005',
        type: 'schedule',
        title: '周末聚餐',
        description: '和朋友聚餐，放松心情',
        date: '2025-06-21',
        time: '18:30',
        location: '海底捞火锅',
        participants: ['朋友们'],
        priority: 'low',
        category: '生活',
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      }
    ];

    // 保存到本地存储
    wx.setStorageSync('local_schedules', schedules);
    console.log(`✅ 已创建 ${schedules.length} 条日程记录`);
    
    return schedules;
  }

  // 创建财务测试数据
  async createExpenseTestData() {
    console.log('💰 创建财务测试数据...');
    
    const expenses = [
      {
        _id: 'expense_test_001',
        type: 'expense',
        title: '星巴克咖啡',
        description: '上午工作时买的咖啡',
        amount: 35,
        category: '餐饮',
        date: '2025-06-16',
        time: '09:30',
        location: '星巴克(国贸店)',
        paymentMethod: '微信支付',
        tags: ['工作', '咖啡'],
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'expense_test_002',
        type: 'expense',
        title: '午餐',
        description: '和同事一起吃的工作餐',
        amount: 68,
        category: '餐饮',
        date: '2025-06-16',
        time: '12:15',
        location: '公司附近餐厅',
        paymentMethod: '支付宝',
        tags: ['工作餐', '同事'],
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'expense_test_003',
        type: 'expense',
        title: '技术书籍',
        description: '购买AI相关的技术书籍',
        amount: 120,
        category: '教育',
        date: '2025-06-15',
        time: '20:00',
        location: '京东商城',
        paymentMethod: '信用卡',
        tags: ['学习', '技术', '书籍'],
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'expense_test_004',
        type: 'expense',
        title: '打车费',
        description: '去客户公司的打车费用',
        amount: 45,
        category: '交通',
        date: '2025-06-14',
        time: '14:00',
        location: '滴滴出行',
        paymentMethod: '微信支付',
        tags: ['工作', '交通', '客户'],
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'expense_test_005',
        type: 'expense',
        title: '办公用品',
        description: '购买笔记本、签字笔等办公用品',
        amount: 85,
        category: '办公',
        date: '2025-06-13',
        time: '16:30',
        location: '办公用品店',
        paymentMethod: '现金',
        tags: ['办公', '用品'],
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      }
    ];

    // 保存到本地存储
    wx.setStorageSync('local_expenses', expenses);
    console.log(`✅ 已创建 ${expenses.length} 条财务记录`);
    
    return expenses;
  }

  // 创建待办测试数据
  async createTodoTestData() {
    console.log('✅ 创建待办测试数据...');
    
    const todos = [
      {
        _id: 'todo_test_001',
        type: 'todo',
        title: '完成季度总结报告',
        description: '整理Q2的工作成果和下季度计划',
        priority: 'high',
        status: 'pending',
        deadline: '2025-06-20',
        category: '工作',
        tags: ['报告', '总结'],
        progress: 30,
        estimatedTime: 4, // 小时
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'todo_test_002',
        type: 'todo',
        title: '准备技术分享PPT',
        description: '为下周的技术分享会准备演示文稿',
        priority: 'medium',
        status: 'in_progress',
        deadline: '2025-06-19',
        category: '工作',
        tags: ['分享', 'PPT', '技术'],
        progress: 60,
        estimatedTime: 3,
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'todo_test_003',
        type: 'todo',
        title: '学习新的AI框架',
        description: '研究最新的AI开发框架和工具',
        priority: 'medium',
        status: 'pending',
        deadline: '2025-06-25',
        category: '学习',
        tags: ['AI', '学习', '框架'],
        progress: 10,
        estimatedTime: 8,
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'todo_test_004',
        type: 'todo',
        title: '整理个人文档',
        description: '整理电脑中的工作文档和资料',
        priority: 'low',
        status: 'pending',
        deadline: '2025-06-30',
        category: '个人',
        tags: ['整理', '文档'],
        progress: 0,
        estimatedTime: 2,
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      },
      {
        _id: 'todo_test_005',
        type: 'todo',
        title: '制定健身计划',
        description: '为下个月制定详细的健身和运动计划',
        priority: 'low',
        status: 'pending',
        deadline: '2025-06-22',
        category: '健康',
        tags: ['健身', '计划', '健康'],
        progress: 0,
        estimatedTime: 1,
        userId: this.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      }
    ];

    // 保存到本地存储
    wx.setStorageSync('local_todos', todos);
    console.log(`✅ 已创建 ${todos.length} 条待办记录`);
    
    return todos;
  }

  // 显示数据统计
  showDataSummary() {
    console.log('\n📊 === 测试数据统计 ===');
    
    const schedules = wx.getStorageSync('local_schedules') || [];
    const expenses = wx.getStorageSync('local_expenses') || [];
    const todos = wx.getStorageSync('local_todos') || [];
    
    console.log(`📅 日程安排: ${schedules.length} 条`);
    schedules.forEach(s => {
      console.log(`   - ${s.title} (${s.date} ${s.time})`);
    });
    
    console.log(`💰 财务记录: ${expenses.length} 条`);
    const totalExpense = expenses.reduce((sum, e) => sum + e.amount, 0);
    console.log(`   - 总支出: ¥${totalExpense}`);
    expenses.forEach(e => {
      console.log(`   - ${e.title}: ¥${e.amount} (${e.category})`);
    });
    
    console.log(`✅ 待办事项: ${todos.length} 条`);
    todos.forEach(t => {
      console.log(`   - ${t.title} [${t.priority}] (${t.progress}%)`);
    });
    
    console.log(`\n📊 总记录数: ${schedules.length + expenses.length + todos.length} 条`);
  }

  // 验证数据是否正确创建
  verifyTestData() {
    console.log('🔍 验证测试数据...');
    
    const schedules = wx.getStorageSync('local_schedules') || [];
    const expenses = wx.getStorageSync('local_expenses') || [];
    const todos = wx.getStorageSync('local_todos') || [];
    
    const isValid = schedules.length > 0 && expenses.length > 0 && todos.length > 0;
    
    if (isValid) {
      console.log('✅ 测试数据验证通过');
      wx.showToast({
        title: '测试数据创建成功',
        icon: 'success',
        duration: 2000
      });
    } else {
      console.log('❌ 测试数据验证失败');
      wx.showToast({
        title: '测试数据创建失败',
        icon: 'error',
        duration: 2000
      });
    }
    
    return isValid;
  }

  // 快速创建单条记录（用于实时测试）
  async createQuickTestRecord(type, data) {
    console.log(`🚀 快速创建${type}记录:`, data);
    
    const record = {
      _id: `${type}_quick_${Date.now()}`,
      type: type,
      ...data,
      userId: this.userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      syncStatus: 'local'
    };
    
    const storageKey = `local_${type}s`;
    const existingRecords = wx.getStorageSync(storageKey) || [];
    existingRecords.push(record);
    wx.setStorageSync(storageKey, existingRecords);
    
    console.log(`✅ 快速记录创建成功: ${record._id}`);
    
    wx.showToast({
      title: `${type}记录已创建`,
      icon: 'success'
    });
    
    return record;
  }
}

// 导出实例
const testDataInitializer = new TestDataInitializer();

// 全局方法，方便在控制台调用
if (typeof global !== 'undefined') {
  global.initTestData = () => testDataInitializer.initAllTestData();
  global.verifyTestData = () => testDataInitializer.verifyTestData();
  global.createQuickRecord = (type, data) => testDataInitializer.createQuickTestRecord(type, data);
}

module.exports = testDataInitializer;
