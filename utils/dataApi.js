// utils/dataApi.js - 数据操作API

const dataSyncManager = require('./dataSync.js');

/**
 * 数据操作API类
 * 提供简化的数据CRUD操作接口
 */
class DataApi {
  constructor() {
    this.syncManager = dataSyncManager;
  }

  /**
   * 初始化数据API
   */
  async init() {
    try {
      await this.syncManager.init();
      console.log('数据API初始化成功');
      return true;
    } catch (error) {
      console.error('数据API初始化失败:', error);
      return false;
    }
  }

  // ===== 日程相关 =====

  /**
   * 创建日程
   */
  async createSchedule(data) {
    try {
      const result = await this.syncManager.saveData('schedule', data);
      return result;
    } catch (error) {
      console.error('创建日程失败:', error);
      throw error;
    }
  }

  /**
   * 更新日程
   */
  async updateSchedule(id, data) {
    try {
      const result = await this.syncManager.updateData('schedule', id, data);
      return result;
    } catch (error) {
      console.error('更新日程失败:', error);
      throw error;
    }
  }

  /**
   * 删除日程
   */
  async deleteSchedule(id) {
    try {
      const result = await this.syncManager.deleteData('schedule', id);
      return result;
    } catch (error) {
      console.error('删除日程失败:', error);
      throw error;
    }
  }

  /**
   * 获取日程列表
   */
  async getSchedules(options = {}) {
    try {
      const result = await this.syncManager.getLocalRecords('schedule', options);
      return result;
    } catch (error) {
      console.error('获取日程失败:', error);
      throw error;
    }
  }

  // ===== 待办相关 =====

  /**
   * 创建待办
   */
  async createTodo(data) {
    try {
      const result = await this.syncManager.saveData('todo', data);
      return result;
    } catch (error) {
      console.error('创建待办失败:', error);
      throw error;
    }
  }

  /**
   * 更新待办
   */
  async updateTodo(id, data) {
    try {
      const result = await this.syncManager.updateData('todo', id, data);
      return result;
    } catch (error) {
      console.error('更新待办失败:', error);
      throw error;
    }
  }

  /**
   * 删除待办
   */
  async deleteTodo(id) {
    try {
      const result = await this.syncManager.deleteData('todo', id);
      return result;
    } catch (error) {
      console.error('删除待办失败:', error);
      throw error;
    }
  }

  /**
   * 获取待办列表
   */
  async getTodos(options = {}) {
    try {
      const result = await this.syncManager.getLocalRecords('todo', options);
      return result;
    } catch (error) {
      console.error('获取待办失败:', error);
      throw error;
    }
  }

  // ===== 财务相关 =====

  /**
   * 创建财务记录
   */
  async createExpense(data) {
    try {
      const result = await this.syncManager.saveData('expense', data);
      return result;
    } catch (error) {
      console.error('创建财务记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新财务记录
   */
  async updateExpense(id, data) {
    try {
      const result = await this.syncManager.updateData('expense', id, data);
      return result;
    } catch (error) {
      console.error('更新财务记录失败:', error);
      throw error;
    }
  }

  /**
   * 删除财务记录
   */
  async deleteExpense(id) {
    try {
      const result = await this.syncManager.deleteData('expense', id);
      return result;
    } catch (error) {
      console.error('删除财务记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取财务记录列表
   */
  async getExpenses(options = {}) {
    try {
      const result = await this.syncManager.getLocalRecords('expense', options);
      return result;
    } catch (error) {
      console.error('获取财务记录失败:', error);
      throw error;
    }
  }

  // ===== 通用方法 =====

  /**
   * 获取最近记录
   */
  async getRecentRecords(limit = 10) {
    try {
      const schedules = await this.getSchedules({ limit: Math.ceil(limit / 3) });
      const todos = await this.getTodos({ limit: Math.ceil(limit / 3) });
      const expenses = await this.getExpenses({ limit: Math.ceil(limit / 3) });

      // 合并并按时间排序
      const allRecords = [...schedules, ...todos, ...expenses]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, limit);

      return allRecords;
    } catch (error) {
      console.error('获取最近记录失败:', error);
      return [];
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return this.syncManager.getSyncStatus();
  }

  /**
   * 手动同步
   */
  async manualSync() {
    try {
      const result = await this.syncManager.forceSyncToCloud();
      return result;
    } catch (error) {
      console.error('手动同步失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const dataApi = new DataApi();

module.exports = dataApi; 