// utils/voiceService.js
// 🎤 语音输入服务 - 集成腾讯ASR插件
const VOICE_STATES = {
  IDLE: 'idle',
  RECORDING: 'recording', 
  PROCESSING: 'processing',
  ERROR: 'error'
};

class VoiceService {
  constructor() {
    this.state = VOICE_STATES.IDLE;
    this.recordManager = null;
    this.asrManager = null;
    this.isPluginReady = false;
    this.currentSession = null;
    this.listeners = new Map();
    
    // 配置参数
    this.config = {
      sampleRate: 16000,        // 采样率
      numberOfChannels: 1,      // 单声道
      encodeBitRate: 48000,     // 比特率
      format: 'mp3',           // 格式
      frameSize: 50,           // 帧大小(ms)
      maxDuration: 60000,      // 最大录音时长(ms)
      minDuration: 1000,       // 最小录音时长(ms)
      confidenceThreshold: 0.6 // 置信度阈值
    };
    
    this.init();
  }

  // 初始化语音服务
  async init() {
    try {
      console.log('🎤 初始化语音服务...');
      
      // 初始化录音管理器
      this.initRecordManager();
      
      // 初始化ASR插件
      await this.initASRPlugin();
      
      console.log('✅ 语音服务初始化完成');
      return true;
    } catch (error) {
      console.error('❌ 语音服务初始化失败:', error);
      return false;
    }
  }

  // 初始化录音管理器
  initRecordManager() {
    this.recordManager = wx.getRecorderManager();
    
    this.recordManager.onStart(() => {
      console.log('🎤 开始录音');
      this.state = VOICE_STATES.RECORDING;
      this.emit('recordStart');
    });

    this.recordManager.onPause(() => {
      console.log('⏸️ 录音暂停');
      this.emit('recordPause');
    });

    this.recordManager.onStop((res) => {
      console.log('⏹️ 录音结束:', res);
      this.state = VOICE_STATES.PROCESSING;
      this.emit('recordStop', res);
      
      // 自动进行语音识别
      if (res.tempFilePath && res.duration >= this.config.minDuration) {
        this.processAudioFile(res);
      } else {
        console.warn('⚠️ 录音时长不足或文件无效');
        this.emit('error', { message: '录音时长不足，请重新录制' });
      }
    });

    this.recordManager.onError((error) => {
      console.error('❌ 录音错误:', error);
      this.state = VOICE_STATES.ERROR;
      this.emit('recordError', error);
    });
  }

  // 初始化ASR插件
  async initASRPlugin() {
    try {
      // 暂时禁用插件，使用云函数备用方案
      console.log('ℹ️ ASR插件暂时不可用，将使用云函数识别方案');
      this.isPluginReady = false;
      return;

      // 以下代码保留，待插件问题解决后可重新启用
      /*
      // 检查插件可用性
      if (!wx.getRealtimeLogManager || typeof wx.requirePlugin !== 'function') {
        throw new Error('当前环境不支持插件功能');
      }

      // 加载腾讯实时语音识别插件
      const plugin = wx.requirePlugin('realtimePlugin');
      if (!plugin) {
        throw new Error('腾讯语音识别插件加载失败');
      }

      this.asrManager = plugin.createRealtimeRecognizer({
        // 插件配置
        lang: 'zh-CN',
        // 可以添加更多配置选项
      });

      // 绑定ASR事件
      this.bindASREvents();
      
      this.isPluginReady = true;
      console.log('✅ ASR插件初始化成功');
      */
      
    } catch (error) {
      console.warn('⚠️ ASR插件初始化失败，使用备用方案:', error.message);
      this.isPluginReady = false;
      // 可以在这里初始化备用的语音识别方案
    }
  }

  // 绑定ASR事件
  bindASREvents() {
    if (!this.asrManager) return;

    // 识别开始
    this.asrManager.onStart(() => {
      console.log('🎯 ASR识别开始');
      this.emit('asrStart');
    });

    // 识别结果
    this.asrManager.onRecognize((res) => {
      console.log('🎯 ASR识别结果:', res);
      this.emit('asrResult', res);
    });

    // 识别结束
    this.asrManager.onEnd((res) => {
      console.log('🎯 ASR识别结束:', res);
      this.state = VOICE_STATES.IDLE;
      this.emit('asrEnd', res);
      
      // 返回最终识别结果
      if (res && res.result) {
        this.emit('voiceResult', {
          text: res.result,
          confidence: res.confidence || 0.8,
          duration: res.duration || 0,
          source: 'tencent_asr'
        });
      }
    });

    // 识别错误
    this.asrManager.onError((error) => {
      console.error('❌ ASR识别错误:', error);
      this.state = VOICE_STATES.ERROR;
      this.emit('asrError', error);
      
      // 尝试备用识别方案
      this.fallbackRecognition();
    });
  }

  // 开始录音
  async startRecording() {
    try {
      // 检查录音权限
      const hasPermission = await this.checkRecordPermission();
      if (!hasPermission) {
        throw new Error('录音权限被拒绝');
      }

      // 检查当前状态
      if (this.state !== VOICE_STATES.IDLE) {
        console.warn('⚠️ 当前正在录音或处理中');
        return false;
      }

      // 生成会话ID
      this.currentSession = Date.now().toString();
      
      console.log('🎤 准备开始录音...');
      
      // 启动录音
      this.recordManager.start({
        duration: this.config.maxDuration,
        sampleRate: this.config.sampleRate,
        numberOfChannels: this.config.numberOfChannels,
        encodeBitRate: this.config.encodeBitRate,
        format: this.config.format,
        frameSize: this.config.frameSize
      });

      // 如果ASR插件可用，同时启动实时识别
      if (this.isPluginReady && this.asrManager) {
        console.log('🎯 启动实时ASR识别');
        this.asrManager.start({
          lang: 'zh-CN'
        });
      }

      return true;
    } catch (error) {
      console.error('❌ 开始录音失败:', error);
      this.emit('error', { message: error.message });
      return false;
    }
  }

  // 停止录音
  stopRecording() {
    try {
      if (this.state === VOICE_STATES.RECORDING) {
        console.log('⏹️ 手动停止录音');
        this.recordManager.stop();
        
        // 停止ASR识别
        if (this.isPluginReady && this.asrManager) {
          this.asrManager.stop();
        }
      }
    } catch (error) {
      console.error('❌ 停止录音失败:', error);
      this.emit('error', { message: error.message });
    }
  }

  // 处理音频文件
  async processAudioFile(audioData) {
    try {
      console.log('🎵 处理音频文件:', audioData);
      
      // 如果已经通过实时ASR获得结果，直接返回
      if (this.isPluginReady) {
        // 实时ASR已在recordStop时处理
        return;
      }

      // 备用方案：使用云函数进行语音识别
      await this.cloudRecognition(audioData);
      
    } catch (error) {
      console.error('❌ 音频处理失败:', error);
      this.fallbackRecognition();
    }
  }

  // 云端语音识别（备用方案）
  async cloudRecognition(audioData) {
    try {
      console.log('☁️ 使用云端语音识别');
      
      // 调用云函数进行语音识别
      const result = await wx.cloud.callFunction({
        name: 'speechRecognition',
        data: {
          audioPath: audioData.tempFilePath,
          duration: audioData.duration,
          fileSize: audioData.fileSize
        }
      });

      if (result.result && result.result.success) {
        const recognitionData = result.result.data;
        this.state = VOICE_STATES.IDLE;
        
        this.emit('voiceResult', {
          text: recognitionData.text,
          confidence: recognitionData.confidence || 0.7,
          duration: audioData.duration,
          source: 'cloud_recognition'
        });
      } else {
        throw new Error(result.result?.message || '云端识别失败');
      }
      
    } catch (error) {
      console.error('❌ 云端识别失败:', error);
      this.fallbackRecognition();
    }
  }

  // 备用识别方案
  fallbackRecognition() {
    console.log('🔄 启用备用识别方案');
    this.state = VOICE_STATES.IDLE;
    
    // 显示手动输入选项
    this.emit('fallbackInput', {
      message: '语音识别失败，请手动输入文本',
      showManualInput: true
    });
  }

  // 检查录音权限
  async checkRecordPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === true) {
            resolve(true);
          } else if (res.authSetting['scope.record'] === false) {
            // 权限被拒绝，引导用户打开设置
            this.showPermissionGuide();
            resolve(false);
          } else {
            // 首次请求权限
            wx.authorize({
              scope: 'scope.record',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: () => resolve(false)
      });
    });
  }

  // 显示权限引导
  showPermissionGuide() {
    wx.showModal({
      title: '需要录音权限',
      content: '语音输入功能需要录音权限，请在设置中开启录音权限',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting();
        }
      }
    });
  }

  // 事件监听
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // 移除事件监听
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ 事件回调执行失败 [${event}]:`, error);
        }
      });
    }
  }

  // 获取当前状态
  getState() {
    return this.state;
  }

  // 销毁服务
  destroy() {
    this.stopRecording();
    this.listeners.clear();
    this.state = VOICE_STATES.IDLE;
    this.currentSession = null;
  }
}

// 创建单例实例
const voiceService = new VoiceService();

export default voiceService; 