// utils/auth.js - 用户认证相关工具函数

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const app = getApp();
  return !!(app.globalData.isLoggedIn && app.globalData.uid);
}

/**
 * 检查是否为访客模式
 * @returns {boolean} 是否为访客模式
 */
function isGuestMode() {
  const app = getApp();
  return app.globalData.isGuest || false;
}

/**
 * 获取当前用户ID
 * @returns {string|null} 用户ID
 */
function getCurrentUserId() {
  const app = getApp();
  return app.globalData.uid || null;
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息
 */
function getCurrentUserInfo() {
  const app = getApp();
  return app.globalData.userInfo || null;
}

/**
 * 要求用户登录，如果未登录则跳转到登录页
 * @param {boolean} showToast 是否显示提示
 * @returns {boolean} 是否已登录
 */
function requireLogin(showToast = true) {
  if (!isLoggedIn()) {
    if (showToast) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
    }
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }, showToast ? 1000 : 0);
    
    return false;
  }
  return true;
}

/**
 * 检查Pro订阅状态
 * @returns {boolean} 是否为Pro用户
 */
function checkProStatus() {
  const app = getApp();
  return app.checkProStatus();
}

/**
 * 要求Pro订阅，如果不是Pro用户则显示升级提示
 * @param {string} feature 功能名称
 * @returns {boolean} 是否为Pro用户
 */
function requirePro(feature = '该功能') {
  if (!checkProStatus()) {
    const app = getApp();
    app.showProUpgrade(feature);
    return false;
  }
  return true;
}

/**
 * 获取带用户ID的数据查询条件
 * @returns {object} 查询条件
 * @throws {Error} 用户未登录时抛出错误
 */
function getUserQuery() {
  const userId = getCurrentUserId();
  if (!userId) {
    throw new Error('用户未登录');
  }
  return { uid: userId };
}

/**
 * 执行登出操作
 */
function logout() {
  const app = getApp();
  app.clearLoginStatus();
  
  wx.reLaunch({
    url: '/pages/login/login'
  });
}

/**
 * 检查用户权限
 * @param {string} permission 权限名称
 * @returns {boolean} 是否有权限
 */
function checkPermission(permission) {
  // 访客模式下的权限限制
  if (isGuestMode()) {
    const guestPermissions = [
      'view_demo',
      'basic_input',
      'local_storage'
    ];
    return guestPermissions.includes(permission);
  }
  
  // 普通用户权限
  const userPermissions = [
    'view_demo',
    'basic_input',
    'local_storage',
    'cloud_sync',
    'calendar_sync',
    'basic_export'
  ];
  
  // Pro用户额外权限
  const proPermissions = [
    'ocr_recognition',
    'budget_alerts',
    'pdf_export',
    'multi_user_sharing',
    'advanced_analytics'
  ];
  
  if (checkProStatus()) {
    return [...userPermissions, ...proPermissions].includes(permission);
  }
  
  return userPermissions.includes(permission);
}

module.exports = {
  isLoggedIn,
  isGuestMode,
  getCurrentUserId,
  getCurrentUserInfo,
  requireLogin,
  checkProStatus,
  requirePro,
  getUserQuery,
  logout,
  checkPermission
};