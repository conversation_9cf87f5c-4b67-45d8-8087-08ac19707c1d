// utils/dataSync.js - 数据同步管理器

class DataSyncManager {
  constructor() {
    this.isOnline = false;
    this.cloudSyncEnabled = false;
    this.syncInProgress = false;
    this.maxRetries = 3;
    this.autoSyncInterval = null;
    
    // 初始化时禁用云同步，避免wx-server-sdk错误
    console.log('📱 数据同步初始化：默认使用本地模式');
  }

  /**
   * 初始化数据同步
   */
  async init() {
    try {
      console.log('🔄 初始化数据同步管理器...');
      
      // 检查网络状态
      this.checkNetworkStatus();
      
      // 尝试检查云开发环境（但不强制要求）
      await this.checkCloudEnvironment();
      
      console.log('✅ 数据同步管理器初始化完成');
      return true;
    } catch (error) {
      console.error('❌ 数据同步初始化失败，将使用本地模式:', error);
      this.cloudSyncEnabled = false;
      this.isOnline = false;
      return false;
    }
  }

  /**
   * 检查云开发环境（非必需）
   */
  async checkCloudEnvironment() {
    try {
      // 简单的云开发环境检查
      if (typeof wx.cloud === 'undefined') {
        console.log('📱 云开发功能不可用，使用本地模式');
        return false;
      }
      
      console.log('☁️ 云开发环境可用，但默认使用本地模式以确保稳定性');
      return false; // 暂时禁用云同步，避免wx-server-sdk问题
    } catch (error) {
      console.log('📱 云开发检查失败，使用本地模式:', error.message);
      return false;
    }
  }

  /**
   * 保存数据（主要入口）
   */
  async saveData(type, data) {
    try {
      console.log(`💾 保存${type}数据:`, data);
      
      // 直接保存到本地存储
      const result = await this.saveToLocalStorage(type, data);
      
      // 如果云同步启用，添加到同步队列（但目前默认禁用）
      if (this.cloudSyncEnabled) {
        await this.addToSyncQueue('create', type, result.id, data);
      }
      
      return { success: true, id: result.id, data: result };
    } catch (error) {
      console.error(`❌ 保存${type}数据失败:`, error);
      throw error;
    }
  }

  /**
   * 更新数据
   */
  async updateData(type, id, data) {
    try {
      console.log(`🔄 更新${type}数据:`, id, data);
      
      const result = await this.updateLocalStorage(type, id, data);
      
      if (this.cloudSyncEnabled) {
        await this.addToSyncQueue('update', type, id, data);
      }
      
      return { success: true, id, data: result };
    } catch (error) {
      console.error(`❌ 更新${type}数据失败:`, error);
      throw error;
    }
  }

  /**
   * 删除数据
   */
  async deleteData(type, id) {
    try {
      console.log(`🗑️ 删除${type}数据:`, id);
      
      await this.deleteFromLocalStorage(type, id);
      
      if (this.cloudSyncEnabled) {
        await this.addToSyncQueue('delete', type, id, null);
      }
      
      return { success: true };
    } catch (error) {
      console.error(`❌ 删除${type}数据失败:`, error);
      throw error;
    }
  }

  /**
   * 获取本地数据
   */
  async getLocalRecords(type, options = {}) {
    try {
      // 修复存储key格式，匹配日程页面的期望
      const storageKey = this.getStorageKey(type);
      const records = wx.getStorageSync(storageKey) || [];
      
      // 简单的筛选和排序
      let filteredRecords = records.filter(record => !record.deleted);
      
      if (options.limit) {
        filteredRecords = filteredRecords.slice(0, options.limit);
      }
      
      return filteredRecords;
    } catch (error) {
      console.error(`获取${type}数据失败:`, error);
      return [];
    }
  }

  /**
   * 保存到本地存储
   */
  async saveToLocalStorage(type, data) {
    const storageKey = this.getStorageKey(type);
    const existingData = wx.getStorageSync(storageKey) || [];
    
    const newRecord = {
      id: this.generateId(),
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deleted: false,
      synced: false
    };
    
    existingData.push(newRecord);
    wx.setStorageSync(storageKey, existingData);
    
    return newRecord;
  }

  /**
   * 更新本地存储
   */
  async updateLocalStorage(type, id, data) {
    const storageKey = this.getStorageKey(type);
    const existingData = wx.getStorageSync(storageKey) || [];
    
    const updatedData = existingData.map(record => {
      if (record.id === id) {
        return {
          ...record,
          ...data,
          updatedAt: new Date().toISOString(),
          synced: false
        };
      }
      return record;
    });
    
    wx.setStorageSync(storageKey, updatedData);
    
    const updatedRecord = updatedData.find(record => record.id === id);
    return updatedRecord;
  }

  /**
   * 从本地存储删除
   */
  async deleteFromLocalStorage(type, id) {
    const storageKey = this.getStorageKey(type);
    const existingData = wx.getStorageSync(storageKey) || [];
    
    const updatedData = existingData.map(record => {
      if (record.id === id) {
        return {
          ...record,
          deleted: true,
          deletedAt: new Date().toISOString(),
          synced: false
        };
      }
      return record;
    });
    
    wx.setStorageSync(storageKey, updatedData);
  }

  /**
   * 获取标准化的存储key
   */
  getStorageKey(type) {
    switch(type) {
      case 'schedule':
        return 'local_schedules';
      case 'todo':
        return 'local_todos';
      case 'expense':
        return 'local_expenses';
      default:
        return `local_${type}s`;
    }
  }

  /**
   * 添加到同步队列（暂时禁用）
   */
  async addToSyncQueue(action, collection, recordId, syncData) {
    // 暂时不实现云同步队列，避免wx-server-sdk错误
    console.log(`📝 同步队列记录: ${action} ${collection} ${recordId} (当前禁用云同步)`);
    console.log('📊 同步数据预览:', syncData ? '有数据' : '无数据');
  }

  /**
   * 云端同步（暂时禁用）
   */
  async syncToCloud(action, collection, recordId, syncData) {
    // 暂时返回本地模式标识
    console.log('☁️ 云同步已禁用，使用本地模式');
    console.log(`📋 同步参数: ${action}, ${collection}, ${recordId}`);
    console.log('📊 同步数据:', syncData ? '有数据' : '无数据');
    return { success: true, data: null, mode: 'local_only' };
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    try {
      wx.getNetworkType({
        success: (res) => {
          this.isOnline = res.networkType !== 'none';
          console.log('🌐 网络状态:', res.networkType, '在线:', this.isOnline);
        },
        fail: () => {
          this.isOnline = false;
          console.log('🌐 网络状态检查失败，假设离线');
        }
      });
    } catch (error) {
      this.isOnline = false;
      console.log('🌐 网络检查异常:', error);
    }
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return 'local_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      cloudSyncEnabled: this.cloudSyncEnabled,
      mode: 'local_only',
      lastSync: null,
      pendingItems: 0
    };
  }

  /**
   * 启用云同步（当云函数问题解决后调用）
   */
  enableCloudSync() {
    console.log('☁️ 尝试启用云同步...');
    this.cloudSyncEnabled = true;
    console.log('✅ 云同步已启用');
  }

  /**
   * 禁用云同步
   */
  disableCloudSync() {
    console.log('📱 禁用云同步，切换到本地模式');
    this.cloudSyncEnabled = false;
    this.isOnline = false;
  }
}

// 创建单例实例
const dataSyncManager = new DataSyncManager();

module.exports = dataSyncManager; 