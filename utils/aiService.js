// utils/aiService.js - DeepSeek AI服务集成

class AIService {
  constructor() {
    // 🎯 多AI提供商配置
    this.providers = {
      deepseek: {
        name: 'DeepSeek',
        apiKey: '***********************************',
        apiBase: 'https://api.deepseek.com',
        model: 'deepseek-chat',
        enabled: true,
        priority: 1
      },
      silicon: {
        name: '硅基流动',
        apiKey: 'sk-slmttbyivskikjlkqccrozdlywchgksvprulgajqjsaaiknn', // 用户提供的API Key
        apiBase: 'https://api.siliconflow.cn', // 🔧 修复：移除重复的/v1
        model: 'deepseek-ai/deepseek-chat', // 硅基流动的DeepSeek模型
        enabled: true,
        priority: 2
      }
    };
    
    // 当前激活的提供商
    this.currentProvider = 'deepseek';
    
    // 🎯 重新启用AI服务
    this.enabled = true;
    
    console.log('🤖 多AI服务提供商已启用');
    console.log('✅ 主要提供商: DeepSeek');
    console.log('🔄 备用提供商: 硅基流动 (API已修复)');
    console.log('🎯 当前状态: AI智能分析已启用');
    
    // 兼容性属性 (保持原有代码正常工作)
    this.apiKey = this.providers.deepseek.apiKey;
    this.apiBase = this.providers.deepseek.apiBase;
    this.model = this.providers.deepseek.model;
  }

  // 🎯 配置硅基流动API
  configureSiliconFlow(apiKey) {
    this.providers.silicon.apiKey = apiKey;
    this.providers.silicon.enabled = !!apiKey;
    
    console.log('🔧 硅基流动API配置:', this.providers.silicon.enabled ? '已启用' : '未启用');
    
    if (this.providers.silicon.enabled) {
      console.log('✅ 硅基流动API已配置为备用方案');
    }
    
    return this.providers.silicon.enabled;
  }

  // 🎯 获取当前可用的提供商
  getAvailableProvider() {
    // 按优先级顺序检查可用的提供商
    const sortedProviders = Object.entries(this.providers)
      .filter(([_, provider]) => provider.enabled && provider.apiKey)
      .sort((a, b) => a[1].priority - b[1].priority);
    
    if (sortedProviders.length > 0) {
      const [providerId, provider] = sortedProviders[0];
      console.log(`🎯 选择提供商: ${provider.name} (优先级 ${provider.priority})`);
      return { id: providerId, ...provider };
    }
    
    console.log('⚠️ 没有可用的AI提供商');
    return null;
  }

  // 初始化API配置
  initConfig(apiKey, apiBase = 'https://api.deepseek.com', model = 'deepseek-chat') {
    this.apiKey = apiKey;
    this.apiBase = apiBase;
    this.model = model; 
    this.enabled = !!apiKey;
    console.log('AI服务配置更新:', this.enabled ? '已启用' : '未启用');
    console.log('使用模型:', this.model);
  }

  // 检查是否已配置
  isConfigured() {
    const hasEnabled = this.enabled;
    const hasApiKey = this.apiKey && this.apiKey.length > 0;
    const isValid = hasEnabled && hasApiKey;
    
    console.log('🔍 API配置检查:', {
      enabled: hasEnabled,
      hasApiKey: hasApiKey,
      apiKeyLength: this.apiKey ? this.apiKey.length : 0,
      isValid: isValid
    });
    
    return isValid;
  }

  // 智能文本分类 - 支持多提供商fallback
  async intelligentClassify(text) {
    console.log('=== 🤖 多AI提供商智能分类开始 ===');
    console.log('📝 输入文本:', text);
    console.log('⚙️ AI服务启用状态:', this.enabled);
    
    if (!this.enabled) {
      console.log('❌ AI服务已禁用，直接使用本地分析');
      return this.fallbackClassify(text);
    }

    // 🎯 依次尝试各个AI提供商
    const availableProviders = Object.entries(this.providers)
      .filter(([_, provider]) => provider.enabled && provider.apiKey)
      .sort((a, b) => a[1].priority - b[1].priority);

    console.log('🔍 可用提供商数量:', availableProviders.length);
    console.log('📋 可用提供商列表:', availableProviders.map(([id, p]) => `${id}:${p.name}(优先级${p.priority})`));

    if (availableProviders.length === 0) {
      console.log('⚠️ 没有可用的AI提供商，直接使用本地分析');
      return this.fallbackClassify(text);
    }

    // 依次尝试每个提供商
    for (const [providerId, provider] of availableProviders) {
      try {
        console.log(`🎯 [${provider.priority}] 开始尝试提供商: ${provider.name}`);
        console.log(`📡 API端点: ${provider.apiBase}`);
        console.log(`🤖 模型: ${provider.model}`);
        console.log(`🔑 API Key: ${provider.apiKey ? `${provider.apiKey.substring(0, 8)}...` : '无'}`);
        
        const result = await this.tryProvider(providerId, provider, text);
        
        console.log(`✅ ${provider.name} 调用成功！`);
        console.log(`📊 AI分析结果:`, result);
        
        const finalResult = {
          items: [result],
          enhanced: true,
          source: provider.name,
          timestamp: Date.now(),
          providerId: providerId
        };
        
        console.log('🎉 AI分类完成，返回结果:', finalResult);
        console.log('=== 🤖 多AI提供商智能分类结束 ===');
        
        return finalResult;
        
      } catch (error) {
        console.error(`❌ ${provider.name} 调用失败:`, error.message);
        console.error(`🚨 错误详情:`, error);
        
        // 如果不是最后一个提供商，继续尝试下一个
        const isLastProvider = providerId === availableProviders[availableProviders.length - 1][0];
        if (!isLastProvider) {
          console.log(`🔄 继续尝试下一个提供商...`);
          continue;
        }
        
        // 所有AI提供商都失败，降级到本地分析
        console.log('🔄 所有AI提供商都失败，开始降级到本地智能分析');
        break;
      }
    }

    // 降级到本地分析
    console.log('🔧 === 开始本地智能分析降级 ===');
    try {
      const fallbackResult = this.fallbackClassify(text);
      console.log('✅ 本地智能分析降级成功');
      
      if (fallbackResult && fallbackResult.items) {
        fallbackResult.enhanced = false;
        fallbackResult.source = '本地智能分析 (所有AI提供商失败)';
      }
      
      console.log('📦 降级结果:', fallbackResult);
      console.log('=== 🤖 多AI提供商智能分类结束 (降级) ===');
      
      return fallbackResult;
    } catch (fallbackError) {
      console.error('❌ 本地分析也失败:', fallbackError);
      const emergencyResult = this.getEmergencyResult(text, fallbackError);
      
      console.log('🆘 使用紧急兜底方案:', emergencyResult);
      console.log('=== 🤖 多AI提供商智能分类结束 (紧急) ===');
      
      return emergencyResult;
    }
  }

  // 🎯 尝试特定AI提供商
  async tryProvider(providerId, provider, text) {
    console.log(`📡 调用 ${provider.name} API (ID: ${providerId})...`);

    const prompt = this.buildClassificationPrompt(text);
    const response = await this.callAIAPI(provider, prompt);

    console.log(`📥 ${provider.name} 响应成功`);
    return this.parseAIResponse(response, text, provider.name);
  }

  // 🎯 通用AI API调用方法
  async callAIAPI(provider, prompt) {
    const url = `${provider.apiBase}/v1/chat/completions`;
    
    const requestBody = {
      model: provider.model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000,
      response_format: { 
        type: "json_object" 
      }
    };
    
    console.log(`📤 发送请求到 ${provider.name}:`, url);
    console.log('📊 请求参数:', {
      model: requestBody.model,
      temperature: requestBody.temperature,
      max_tokens: requestBody.max_tokens
    });
    
    return new Promise((resolve, reject) => {
      // 🚀 强化版请求配置
      const requestConfig = {
        url: url,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${provider.apiKey}`
        },
        data: requestBody,
        timeout: 45000, // 🔧 增加到45秒超时，提高成功率
        enableCache: false, // 禁用缓存确保实时性
        enableHttp2: true, // 启用HTTP2提高性能
        enableQuic: true, // 启用QUIC协议提高连接速度
        success: (res) => {
          console.log(`📥 ${provider.name} API响应状态:`, res.statusCode);
          console.log(`📊 ${provider.name} 响应headers:`, res.header);
          
          // 🔍 详细状态码检查
          if (res.statusCode === 200) {
            try {
              // 验证响应数据结构
              if (!res.data || !res.data.choices || !res.data.choices[0]) {
                throw new Error(`${provider.name} 响应数据结构异常: ${JSON.stringify(res.data)}`);
              }
              
              const content = res.data.choices[0].message.content;
              console.log(`✅ ${provider.name} 返回内容:`, content);
              
              // 解析JSON响应
              let parsedContent;
              try {
                parsedContent = JSON.parse(content);
                console.log(`✅ ${provider.name} JSON解析成功:`, parsedContent);
              } catch (parseError) {
                console.error(`❌ ${provider.name} JSON解析失败:`, parseError);
                throw new Error(`${provider.name} 响应不是有效JSON: ${content}`);
              }
              
              // 验证必要字段
              if (!parsedContent.primaryType) {
                throw new Error(`${provider.name} 响应缺少primaryType字段`);
              }
              
              resolve(parsedContent);
              
            } catch (parseError) {
              console.error(`❌ ${provider.name} 响应处理失败:`, parseError);
              reject(new Error(`${provider.name} 响应处理失败: ${parseError.message}`));
            }
          } else {
            // 处理非200状态码
            const errorMsg = res.data?.error?.message || res.data?.message || '未知错误';
            console.error(`❌ ${provider.name} API错误 ${res.statusCode}:`, errorMsg);
            
            // 根据不同错误码提供更精确的错误信息
            let detailedError = '';
            switch (res.statusCode) {
              case 400:
                detailedError = '请求参数错误，可能是模型名称或prompt格式问题';
                break;
              case 401:
                detailedError = 'API Key无效或已过期';
                break;
              case 403:
                detailedError = 'API访问被拒绝，可能是权限或配额问题';
                break;
              case 404:
                detailedError = 'API端点不存在，请检查URL配置';
                break;
              case 429:
                detailedError = 'API调用频率超限，请稍后重试';
                break;
              case 500:
              case 502:
              case 503:
                detailedError = '服务器内部错误，建议重试';
                break;
              default:
                detailedError = '未知HTTP错误';
            }
            
            reject(new Error(`${provider.name} API调用失败 (${res.statusCode}): ${errorMsg} - ${detailedError}`));
          }
        },
        fail: (error) => {
          console.error(`❌ ${provider.name} 网络请求失败:`, error);
          
          // 🔍 详细的网络错误分析
          let errorType = '网络连接失败';
          let suggestion = '请检查网络连接';
          
          if (error.errMsg) {
            if (error.errMsg.includes('timeout') || error.errMsg.includes('超时')) {
              errorType = '请求超时';
              suggestion = 'API服务响应较慢，已自动重试其他提供商';
            } else if (error.errMsg.includes('fail') && error.errMsg.includes('dns')) {
              errorType = 'DNS解析失败';
              suggestion = '网络DNS配置问题，尝试切换网络';
            } else if (error.errMsg.includes('ssl') || error.errMsg.includes('certificate')) {
              errorType = 'SSL证书错误';
              suggestion = '服务器证书问题，请检查系统时间';
            } else if (error.errMsg.includes('connect')) {
              errorType = '连接被拒绝';
              suggestion = '可能是网络防火墙或代理问题';
            }
          }
          
          const detailedErrorMsg = `${provider.name} ${errorType}: ${error.errMsg} (${suggestion})`;
          console.error(`🚨 详细错误信息:`, detailedErrorMsg);
          
          reject(new Error(detailedErrorMsg));
        }
      };
      
      // 🚀 执行请求
      console.log(`🚀 发起 ${provider.name} API请求...`);
      wx.request(requestConfig);
    });
  }

  // 🎯 生成紧急兜底结果
  getEmergencyResult(text, error) {
    console.log('🆘 使用最终兜底方案');
    
    return {
      items: [{
        primaryType: 'todo',
        isFuture: false,
        finalCategories: ['todo'],
        confidence: 0.5,
        reason: '兜底分类方案',
        originalText: text,
        title: this.extractTitle(text),
        extractedInfo: {
          title: this.extractTitle(text),
          category: '一般',
          priority: 'medium'
        },
        error: error.message
      }],
      enhanced: false,
      source: '最终兜底方案',
      timestamp: Date.now(),
      error: error.message
    };
  }

  // 构建分类提示词 - 针对DeepSeek v3优化
  buildClassificationPrompt(text) {
    // 🗓️ 获取当前时间信息用于相对日期计算
    const now = new Date();
    const todayISO = now.toISOString();
    const todayDate = now.toLocaleDateString('zh-CN');
    const todayWeekday = ['日', '一', '二', '三', '四', '五', '六'][now.getDay()];
    
    return `你是一个专业的智能分类系统，请对用户输入进行精确的分类分析。

📝 用户输入："${text}"
🕐 当前时间：${todayDate} 星期${todayWeekday} (${todayISO})

🎯 请严格按照以下JSON格式返回分析结果：
{
  "primaryType": "schedule|finance",
  "isFuture": true,
  "confidence": 0.95,
  "analysis": {
    "hasTime": true,
    "hasMoney": false,
    "isFutureEvent": true,
    "hasAction": true,
    "keywords": ["7天后", "去", "杭州"],
    "relativeDate": "7天后",
    "targetCity": "杭州"
  },
  "extractedInfo": {
    "title": "7天后去杭州",
    "amount": 0,
    "category": "出差",
    "dateTime": "2024-01-22T14:00:00.000Z",
    "priority": "medium",
    "description": "7天后去杭州",
    "location": "杭州"
  },
  "finalCategories": ["schedule", "todo"],
  "reason": "包含相对日期和地点信息，识别为未来的日程安排"
}

🔧 **核心分类规则（严格执行）：**

**第一步：基础类型识别（二选一）**

1. 🏦 **finance（财务记录）**：
   - 💰 金钱关键词：买、花、元、块、钱、购买、消费、收入、工资、支付、转账、借、还款、缴费、报销
   - 📊 金额模式：数字+元/块/钱（如：50元、一千块）
   - 💳 财务动作：付款、收款、投资、理财、账单
   - ⚡ **强制规则**：包含明确金额的内容必须归类为finance

2. 📅 **schedule（日程安排）**：
   - ⏰ 时间关键词：今天、明天、下周、时间、点钟、小时、分钟、天后、日后
   - 🗓️ **相对日期关键词**：明天、后天、三天后、四天后、五天后、六天后、七天后、八天后、九天后、十天后、X天后、下周、下月
   - 🤝 活动关键词：会议、约会、活动、聚会、预约、安排、开会、面试、聚餐、课程、培训、讲座、去、出差、旅行
   - 🏢 地点关键词：北京、上海、杭州、深圳、广州、南京、成都、西安、武汉、重庆等城市名称
   - 📋 任务关键词：完成、处理、准备、提交、汇报
   - ⚡ **强制规则**：有明确时间、相对日期或活动安排的归类为schedule

**第二步：⚡ 相对日期处理（关键新增）**

3. 🗓️ **相对日期计算指导**：
   - 识别相对日期词：明天(+1天)、后天(+2天)、三天后(+3天)、四天后(+4天)、五天后(+5天)、六天后(+6天)、七天后(+7天)、八天后(+8天)、九天后(+9天)、十天后(+10天)
   - 数字形式：1天后、2天后、3天后...10天后
   - 从当前时间${todayISO}开始计算
   - 🎯 **计算示例**：
     * "明天去上海" → dateTime为明天14:00 (${new Date(now.getTime() + 24*60*60*1000).toISOString()})
     * "7天后去杭州" → dateTime为7天后14:00 (${new Date(now.getTime() + 7*24*60*60*1000).toISOString()})
     * "五天后开会" → dateTime为5天后14:00 (${new Date(now.getTime() + 5*24*60*60*1000).toISOString()})
   - 默认时间：如无具体时间，设为下午14:00:00
   - 🏢 **地点提取**：提取城市名称到location字段

**第三步：时间属性判断**

4. 🔮 **isFuture判断**：
   - ✅ 未来时间词：明天、后天、下周、下月、下个月、下年、稍后、一会儿、将来、未来、X天后、X日后
   - ❌ 当前/过去词：今天、现在、刚才、昨天、已经、完成了
   - ⚡ **特别注意**：任何包含"天后"、"日后"的都是未来时间

**第四步：多重分类处理**

5. 📝 **todo附加规则**：
   - 🎯 未来的finance事件 → ["finance", "todo"]
   - 🎯 未来的schedule事件 → ["schedule", "todo"]
   - 🎯 当前的事件 → 仅主类型["finance"] 或 ["schedule"]

**第五步：优先级评估**

6. ⚡ **priority分级**：
   - 🚨 urgent：包含"紧急"、"立即"、"马上"、"必须"
   - 🔥 high：包含"重要"、"关键"、"优先"、明确deadline
   - 📋 medium：一般任务和安排（默认）
   - 📄 low：可延后的事项

**🎯 相对日期标准示例：**

| 输入文本 | primaryType | isFuture | dateTime计算 | location | finalCategories |
|---------|-------------|----------|-------------|----------|----------------|
| "明天开会" | schedule | true | 明天14:00 | "" | ["schedule", "todo"] |
| "5天后去上海" | schedule | true | 5天后14:00 | "上海" | ["schedule", "todo"] |
| "七天后去杭州" | schedule | true | 7天后14:00 | "杭州" | ["schedule", "todo"] |
| "下周三开会" | schedule | true | 下周三14:00 | "" | ["schedule", "todo"] |
| "10天后返回北京" | schedule | true | 10天后14:00 | "北京" | ["schedule", "todo"] |

**✅ 输出要求：**

- primaryType必须是"schedule"或"finance"之一
- isFuture严格判断是否为未来时间（相对日期都是未来）
- finalCategories根据上述规则包含1-2个分类
- confidence表示分类置信度(0.0-1.0)
- **extractedInfo.dateTime必须是准确的ISO格式时间戳**
- **extractedInfo.location必须包含提取的地点信息**
- 必须返回完整的标准JSON格式
- 所有字段都要填写，不可为空

**🔍 特殊情况处理：**

- 🤔 模糊内容：默认归类为schedule，confidence<0.6
- 💰 无明确金额的财务描述：根据动词判断(买→finance, 去→schedule)
- ⏰ 相对日期：严格按照上述计算规则生成dateTime
- 🏢 地点提取：从文本中识别城市、地标、场所名称
- 🔄 复合内容："7天后买东西花500元" → finance(主要)+todo(未来)

**⚡ 重要提醒：**
1. 相对日期("X天后")必须计算为准确的未来日期时间
2. 地点信息必须提取到location字段
3. 所有未来事件的isFuture必须为true
4. dateTime格式必须为ISO标准：YYYY-MM-DDTHH:mm:ss.sssZ

请仔细分析输入文本，特别注意相对日期的准确计算，严格按照上述规则进行分类，确保返回准确的JSON结果。`;
  }

  // 解析AI响应
  parseAIResponse(response, originalText, providerName) {
    try {
      // 验证响应格式
      if (!response.primaryType) {
        throw new Error('响应缺少primaryType字段');
      }
      
      const result = {
        primaryType: response.primaryType,
        isFuture: response.isFuture || false,
        finalCategories: response.finalCategories || [response.primaryType],
        confidence: response.confidence || 0.8,
        reason: response.reason || '智能分析结果',
        originalText: originalText,
        providerName: providerName
      };
      
      // 根据类型处理特定信息
      if (response.extractedInfo) {
        const info = response.extractedInfo;
        
        if (response.primaryType === 'finance' && info.amount) {
          result.financialInfo = {
            amount: parseFloat(info.amount) || 0,
            category: info.category || '其他',
            description: info.description || info.title || originalText
          };
        }
        
        if (response.primaryType === 'schedule') {
          result.timeInfo = {
            dateTime: info.dateTime || new Date().toISOString(),
            description: info.description || info.title || originalText
          };
          
          // 🏢 重要：确保location信息被正确提取和传递
          if (info.location) {
            result.location = info.location;
            console.log('🏢 AI提取的地点信息:', info.location);
          }
        }
        
        // 未来事件的优先级处理
        if (response.isFuture && info.priority) {
          const priorityMap = { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 };
          result.priority = priorityMap[info.priority] || 2;
        }
        
        // 🎯 通用信息 - 确保包含所有extractedInfo字段
        result.title = info.title || this.extractTitle(originalText);
        result.category = info.category || this.getDefaultCategory(response.primaryType);
        result.description = info.description || originalText;
        
        // 🗓️ 确保dateTime字段被正确传递
        if (info.dateTime) {
          result.dateTime = info.dateTime;
          console.log('🗓️ AI计算的日期时间:', info.dateTime);
        }
        
        // 🏢 确保location字段被正确传递（再次确认）
        if (info.location && !result.location) {
          result.location = info.location;
        }
      }
      
      console.log('🎯 解析后的AI结果:', result);
      return result;
      
    } catch (error) {
      console.error('解析AI响应失败:', error);
      // 返回基础结果
      return {
        primaryType: 'schedule',
        isFuture: false,
        finalCategories: ['schedule'],
        confidence: 0.5,
        reason: `解析失败，使用默认分类: ${error.message}`,
        title: this.extractTitle(originalText),
        originalText: originalText,
        providerName: providerName
      };
    }
  }

  // 获取默认分类
  getDefaultCategory(primaryType) {
    const categoryMap = {
      'finance': '其他',
      'schedule': '一般'
    };
    return categoryMap[primaryType] || '一般';
  }

  // 提取标题（简单清理）
  extractTitle(text) {
    return text.length > 20 ? text.substring(0, 20) + '...' : text;
  }

  // 备用分类方案（使用增强的本地规则）
  fallbackClassify(text) {
    console.log('🔧 === 增强本地智能分析启动 ===');
    console.log('📝 输入文本:', text);
    
    // 🎯 增强版关键词库
    const keywords = {
      // 💰 财务相关（高精度匹配）
      finance: {
        money: ['元', '块', '钱', '￥', '$', '美元', '人民币'],
        actions: ['买', '花', '购', '消费', '支付', '转账', '借', '还款', '缴费', '报销', '收入', '工资', '奖金', '理财', '投资'],
        contexts: ['信用卡', '房租', '保险', '水电费', '话费', '购物', '餐费', '交通费', '医疗费']
      },
      
      // 📅 日程相关（高精度匹配）
      schedule: {
        time: ['今天', '明天', '后天', '昨天', '下周', '下月', '时间', '点', '小时', '分钟', '早上', '下午', '晚上', '中午'],
        activities: ['会议', '开会', '约会', '面试', '聚会', '活动', '课程', '培训', '讲座', '研讨', '预约', '安排', '计划'],
        actions: ['完成', '处理', '准备', '提交', '汇报', '参加', '出席', '参与']
      },
      
      // 🔮 时间属性（未来vs当前）
      time: {
        future: ['明天', '后天', '下周', '下月', '下个月', '下年', '稍后', '一会儿', '将来', '未来', '接下来', '马上', '待会'],
        current: ['今天', '现在', '当前', '目前', '此时', '正在'],
        past: ['昨天', '前天', '上周', '上月', '之前', '刚才', '已经', '完成了', '做过了']
      },
      
      // ⚡ 优先级关键词
      priority: {
        urgent: ['紧急', '立即', '马上', '必须', '急', '火急', '迫切', 'ASAP'],
        high: ['重要', '关键', '优先', '重点', '核心', '关键性'],
        medium: ['一般', '普通', '常规', '正常'],
        low: ['可延后', '不急', '有空时', '闲时', '随便']
      }
    };
    
    // 🔍 文本预处理
    const normalizedText = text.toLowerCase().replace(/\s+/g, '');

    // 📊 特征分析
    const features = {
      normalizedLength: normalizedText.length,
      // 金钱特征
      hasMoneyWords: keywords.finance.money.some(word => text.includes(word)),
      hasFinanceActions: keywords.finance.actions.some(word => text.includes(word)),
      hasFinanceContexts: keywords.finance.contexts.some(word => text.includes(word)),
      
      // 时间特征
      hasTimeWords: keywords.schedule.time.some(word => text.includes(word)),
      hasScheduleActivities: keywords.schedule.activities.some(word => text.includes(word)),
      hasScheduleActions: keywords.schedule.actions.some(word => text.includes(word)),
      
      // 数字特征
      numbers: text.match(/\d+(\.\d+)?/g) || [],
      hasNumbers: false,
      
      // 时间属性
      isFuture: keywords.time.future.some(word => text.includes(word)),
      isCurrent: keywords.time.current.some(word => text.includes(word)),
      isPast: keywords.time.past.some(word => text.includes(word)),
      
      // 优先级
      urgentLevel: 'medium'
    };
    
    features.hasNumbers = features.numbers.length > 0;
    
    // 🎯 优先级判断
    if (keywords.priority.urgent.some(word => text.includes(word))) {
      features.urgentLevel = 'urgent';
    } else if (keywords.priority.high.some(word => text.includes(word))) {
      features.urgentLevel = 'high';
    } else if (keywords.priority.low.some(word => text.includes(word))) {
      features.urgentLevel = 'low';
    }
    
    console.log('🔍 特征分析结果:', features);
    
    // 🧮 智能分类算法
    let primaryType = 'schedule'; // 默认值
    let confidence = 0.5;
    let decisionFactors = [];
    
    // 💰 财务类型判断（更严格的条件）
    const financeScore = 
      (features.hasMoneyWords ? 40 : 0) +
      (features.hasFinanceActions ? 30 : 0) +
      (features.hasFinanceContexts ? 20 : 0) +
      (features.hasNumbers && features.hasMoneyWords ? 20 : 0);
    
    // 📅 日程类型判断
    const scheduleScore = 
      (features.hasTimeWords ? 35 : 0) +
      (features.hasScheduleActivities ? 35 : 0) +
      (features.hasScheduleActions ? 20 : 0) +
      (features.hasNumbers && features.hasTimeWords ? 15 : 0);
    
    console.log('📊 分类评分:', { financeScore, scheduleScore });
    
    // 🎯 主类型决策
    if (financeScore > scheduleScore && financeScore >= 50) {
      primaryType = 'finance';
      confidence = Math.min(0.9, 0.5 + financeScore / 100);
      decisionFactors.push(`财务评分: ${financeScore}`);
    } else if (scheduleScore >= 40) {
      primaryType = 'schedule';
      confidence = Math.min(0.9, 0.5 + scheduleScore / 100);
      decisionFactors.push(`日程评分: ${scheduleScore}`);
    } else {
      // 🤔 低置信度情况：通过关键词做最终判断
      if (features.hasMoneyWords || text.includes('花') || text.includes('买')) {
        primaryType = 'finance';
        confidence = 0.6;
        decisionFactors.push('包含金钱关键词');
      } else {
        primaryType = 'schedule';
        confidence = 0.5;
        decisionFactors.push('默认日程分类');
      }
    }
    
    // 🔮 时间属性最终判断
    let isFuture = features.isFuture;
    if (features.isCurrent || features.isPast) {
      isFuture = false;
    }
    
    // 📋 最终分类构建
    let finalCategories = [primaryType];
    if (isFuture) {
      finalCategories.push('todo');
      confidence += 0.1; // 未来事件增加置信度
    }
    
    console.log('⚖️ 分类决策:', {
      primaryType,
      confidence: Math.min(confidence, 1.0),
      isFuture,
      finalCategories,
      decisionFactors
    });
    
    // 🏗️ 构建结果对象
    const result = {
      primaryType,
      isFuture,
      finalCategories,
      confidence: Math.min(confidence, 1.0),
      reason: `本地智能分析 - ${decisionFactors.join(', ')}`,
      originalText: text,
      title: this.extractTitle(text),
      extractedInfo: {
        title: this.extractTitle(text),
        amount: this.extractAmount(text),
        category: this.getSmartCategory(text, primaryType),
        priority: features.urgentLevel,
        description: `智能分析：${text}`,
        keywords: this.extractKeywords(text, features)
      },
      providerName: '本地智能分析 (Enhanced v2.0)',
      // 🔍 调试信息
      debugInfo: {
        features,
        scores: { financeScore, scheduleScore },
        decisionFactors
      }
    };

    // 💰 财务专用信息
    if (primaryType === 'finance') {
      result.financialInfo = {
        amount: this.extractAmount(text),
        category: this.getSmartCategory(text, primaryType),
        description: text
      };
    }

    // 📅 日程专用信息
    if (primaryType === 'schedule') {
      result.timeInfo = {
        description: text,
        estimatedTime: this.extractTimeInfo(text)
      };
    }

    // 📦 最终响应格式
    const finalResponse = {
      items: [result],
      enhanced: true, // 标记为增强版本
      source: '本地智能分析 (Enhanced v2.0)',
      timestamp: Date.now(),
      analysis: {
        hasTime: features.hasTimeWords,
        hasMoney: features.hasMoneyWords,
        isFutureEvent: isFuture,
        hasAction: features.hasScheduleActions || features.hasFinanceActions,
        confidence: result.confidence
      },
      providerId: 'local_enhanced_v2'
    };
    
    console.log('🎉 本地智能分析完成:', finalResponse);
    console.log('=== 增强本地智能分析结束 ===');

    return finalResponse;
  }

  // 🎯 新增：智能文本分析
  analyzeText(text) {
    return {
      length: text.length,
      hasNumbers: /\d/.test(text),
      hasTime: /\d{1,2}[:：]\d{2}|\d{1,2}点/.test(text),
      hasCurrency: /[￥¥$]\d+|\d+元|\d+块/.test(text),
      hasDate: /\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日号]?/.test(text)
    };
  }

  // 🎯 新增：提取金额
  extractAmount(text) {
    const moneyMatch = text.match(/(\d+(?:\.\d+)?)[元块钱]/);
    return moneyMatch ? parseFloat(moneyMatch[1]) : 0;
  }

  // 🎯 新增：智能分类
  getSmartCategory(text, primaryType) {
    if (primaryType === 'finance') {
      if (text.includes('餐') || text.includes('吃') || text.includes('喝')) return '餐饮';
      if (text.includes('交通') || text.includes('打车') || text.includes('公交')) return '交通';
      if (text.includes('购物') || text.includes('买')) return '购物';
      if (text.includes('娱乐') || text.includes('电影') || text.includes('游戏')) return '娱乐';
      return '其他';
    } else {
      if (text.includes('会议') || text.includes('开会')) return '工作';
      if (text.includes('学习') || text.includes('课程') || text.includes('培训')) return '学习';
      if (text.includes('运动') || text.includes('健身')) return '运动';
      if (text.includes('约会') || text.includes('聚会')) return '社交';
      return '一般';
    }
  }

  // 批量分类
  async batchClassify(texts) {
    const results = [];
    
    for (const text of texts) {
      try {
        const result = await this.intelligentClassify(text);
        results.push({
          text,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          text,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  // 获取服务状态
  getStatus() {
    return {
      configured: this.isConfigured(),
      enabled: this.enabled,
      hasApiKey: !!(this.apiKey && this.apiKey.length > 10),
      apiBase: this.apiBase,
      model: this.model
    };
  }

  // 🎯 添加classifyInput方法（API兼容性）
  async classifyInput(text) {
    console.log('🔄 classifyInput 调用，转发到 intelligentClassify');
    
    try {
      const result = await this.intelligentClassify(text);
      console.log('🎯 处理AI分类结果:', result);
      
      // 确保返回正确的格式
      const data = result.items && result.items.length > 0 ? result.items[0] : {
        primaryType: 'schedule',
        isFuture: false,
        finalCategories: ['schedule'],
        confidence: 0.5,
        originalText: text,
        title: this.extractTitle(text),
        extractedInfo: {
          title: this.extractTitle(text),
          category: '一般',
          priority: 'medium'
        }
      };
      
      console.log('✅ AI分类结果处理成功:', data);
      
      return {
        success: true,
        data: data,
        enhanced: result.enhanced || false,
        source: result.source || '本地智能分析',
        timestamp: result.timestamp || Date.now()
      };
      
    } catch (error) {
      console.error('❌ classifyInput 完全失败:', error);
      
      // 最终兜底方案
      const basicData = {
        primaryType: 'schedule',
        isFuture: false,
        finalCategories: ['schedule'],
        confidence: 0.3,
        originalText: text,
        title: this.extractTitle(text),
        extractedInfo: {
          title: this.extractTitle(text),
          category: '一般',
          priority: 'medium'
        },
        reason: '最终备用方案'
      };
      
      console.log('🆘 使用最终备用方案:', basicData);
      
      return {
        success: true, // 即使失败也返回true，确保功能继续
        data: basicData,
        enhanced: false,
        source: '最终备用方案',
        error: error.message
      };
    }
  }

  // 🔧 辅助方法：提取关键词
  extractKeywords(text, features) {
    const keywords = [];
    
    // 添加检测到的数字
    if (features.numbers.length > 0) {
      keywords.push(...features.numbers);
    }
    
    // 添加时间相关词汇
    const timeWords = ['明天', '后天', '下周', '下月', '今天', '现在'];
    timeWords.forEach(word => {
      if (text.includes(word)) keywords.push(word);
    });
    
    // 添加动作词汇
    const actionWords = ['买', '花', '开会', '会议', '完成', '处理'];
    actionWords.forEach(word => {
      if (text.includes(word)) keywords.push(word);
    });
    
    return keywords.slice(0, 10); // 限制数量
  }

  // 🔧 辅助方法：提取时间信息
  extractTimeInfo(text) {
    const timeInfo = {};
    
    // 提取具体时间
    const timeMatch = text.match(/(\d{1,2})[点:：](\d{1,2})?/);
    if (timeMatch) {
      timeInfo.hour = parseInt(timeMatch[1]);
      timeInfo.minute = timeMatch[2] ? parseInt(timeMatch[2]) : 0;
    }
    
    // 提取时间段
    if (text.includes('早上') || text.includes('上午')) {
      timeInfo.period = 'morning';
    } else if (text.includes('下午')) {
      timeInfo.period = 'afternoon';
    } else if (text.includes('晚上') || text.includes('夜里')) {
      timeInfo.period = 'evening';
    }
    
    return timeInfo;
  }

  // 🔧 辅助方法：网络诊断
  async diagnoseNetworkConnectivity() {
    console.log('🌐 === 开始网络诊断 ===');
    
    const testUrls = [
      'https://api.deepseek.com',
      'https://api.siliconflow.cn',
      'https://www.baidu.com'
    ];
    
    const results = [];
    
    for (const url of testUrls) {
      try {
        console.log(`🔍 测试连接: ${url}`);
        
        const startTime = Date.now();
        const pingResult = await this.pingUrl(url);
        const duration = Date.now() - startTime;

        results.push({
          url,
          success: pingResult,
          duration,
          url,
          success: true,
          duration,
          status: 'ok'
        });
        
        console.log(`✅ ${url} 连接成功 (${duration}ms)`);
        
      } catch (error) {
        results.push({
          url,
          success: false,
          error: error.message,
          status: 'failed'
        });
        
        console.log(`❌ ${url} 连接失败: ${error.message}`);
      }
    }
    
    console.log('🌐 网络诊断完成:', results);
    return results;
  }

  // 🔧 辅助方法：Ping URL
  async pingUrl(baseUrl) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: baseUrl,
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          // 任何HTTP响应都算成功
          resolve(res.statusCode);
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '连接失败'));
        }
      });
    });
  }

  // 🔧 辅助方法：API健康检查
  async checkAPIHealth() {
    console.log('🏥 === API健康检查开始 ===');
    
    const healthResults = {};
    
    // 检查所有提供商
    for (const [providerId, provider] of Object.entries(this.providers)) {
      if (!provider.enabled || !provider.apiKey) {
        healthResults[providerId] = {
          name: provider.name,
          status: 'disabled',
          message: 'API未启用或缺少API Key'
        };
        continue;
      }
      
      try {
        console.log(`🔍 健康检查: ${provider.name}`);
        
        // 用一个简单的测试文本检查API
        const testResult = await this.tryProvider(providerId, provider, '测试');

        healthResults[providerId] = {
          name: provider.name,
          testSuccess: !!testResult,
          status: 'healthy',
          message: '运行正常',
          responseTime: Date.now()
        };
        
        console.log(`✅ ${provider.name} 健康检查通过`);
        
      } catch (error) {
        healthResults[providerId] = {
          name: provider.name,
          status: 'unhealthy',
          message: error.message,
          error: error
        };
        
        console.log(`❌ ${provider.name} 健康检查失败: ${error.message}`);
      }
    }
    
    console.log('🏥 API健康检查完成:', healthResults);
    return healthResults;
  }
}

// 创建全局实例
const aiService = new AIService();

// 导出
module.exports = aiService;