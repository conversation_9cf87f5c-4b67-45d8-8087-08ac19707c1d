// utils/dataModels.js - 数据模型定义

/**
 * 生成唯一ID
 */
function generateId() {
  return 'rec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 日程数据模型
 */
export class ScheduleModel {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.uid = data.uid || null;
    this.title = data.title || '';
    this.description = data.description || '';
    this.startTime = data.startTime || null;
    this.endTime = data.endTime || null;
    this.allDay = data.allDay || false;
    this.location = data.location || '';
    this.reminder = data.reminder || 15; // 默认15分钟提醒
    this.reminderType = data.reminderType || 'none'; // none, 10min, 30min, 1hour, 1day
    this.repeatRule = data.repeatRule || null;
    this.tags = data.tags || [];
    this.color = data.color || '#667eea';
    this.calendar = data.calendar || 'default';
    this.status = data.status || 'pending'; // pending, ongoing, completed, cancelled
    this.source = data.source || 'manual'; // manual, voice, import
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  // 验证数据有效性
  validate() {
    const errors = [];
    
    if (!this.title || this.title.trim().length === 0) {
      errors.push('标题不能为空');
    }
    
    if (!this.startTime) {
      errors.push('开始时间不能为空');
    } else if (isNaN(new Date(this.startTime).getTime())) {
      errors.push('开始时间格式无效');
    }
    
    if (this.endTime && isNaN(new Date(this.endTime).getTime())) {
      errors.push('结束时间格式无效');
    }
    
    if (this.startTime && this.endTime && new Date(this.startTime) >= new Date(this.endTime)) {
      errors.push('开始时间不能晚于或等于结束时间');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 转换为云端存储格式
  toCloudFormat() {
    return {
      id: this.id,
      uid: this.uid,
      title: this.title,
      description: this.description,
      startTime: this.startTime,
      endTime: this.endTime,
      allDay: this.allDay,
      location: this.location,
      reminder: this.reminder,
      reminderType: this.reminderType,
      repeatRule: this.repeatRule,
      tags: this.tags,
      color: this.color,
      calendar: this.calendar,
      status: this.status,
      source: this.source,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // 转换为本地存储格式
  toLocalFormat() {
    return {
      id: this.id,
      uid: this.uid,
      title: this.title,
      description: this.description,
      start_time: this.startTime,
      end_time: this.endTime,
      all_day: this.allDay ? 1 : 0,
      location: this.location,
      reminder: this.reminder,
      reminder_type: this.reminderType,
      repeat_rule: this.repeatRule,
      tags: JSON.stringify(this.tags),
      color: this.color,
      calendar: this.calendar,
      status: this.status,
      source: this.source,
      created_at: this.createdAt,
      updated_at: this.updatedAt
    };
  }
}

/**
 * 财务记录数据模型
 */
export class ExpenseModel {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.uid = data.uid || null;
    this.amount = data.amount || 0;
    this.category = data.category || '';
    this.subCategory = data.subCategory || '';
    this.description = data.description || '';
    this.date = data.date || new Date().toISOString();
    this.paymentMethod = data.paymentMethod || 'cash';
    this.location = data.location || '';
    this.merchant = data.merchant || '';
    this.tags = data.tags || [];
    this.type = data.type || 'expense'; // expense, income
    this.account = data.account || 'default';
    this.currency = data.currency || 'CNY';
    this.receipt = data.receipt || null; // 票据照片
    this.source = data.source || 'manual'; // manual, voice, ocr, import
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  validate() {
    const errors = [];
    
    if (!this.amount || typeof this.amount !== 'number' || this.amount <= 0) {
      errors.push('金额必须是大于0的数字');
    }
    
    if (this.amount > 999999.99) {
      errors.push('金额不能超过999999.99');
    }
    
    if (!this.category || this.category.trim().length === 0) {
      errors.push('分类不能为空');
    }
    
    if (!this.date || isNaN(new Date(this.date).getTime())) {
      errors.push('日期格式无效');
    }
    
    if (!['expense', 'income'].includes(this.type)) {
      errors.push('类型必须是expense或income');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  toCloudFormat() {
    return {
      id: this.id,
      uid: this.uid,
      amount: this.amount,
      category: this.category,
      subCategory: this.subCategory,
      description: this.description,
      date: this.date,
      paymentMethod: this.paymentMethod,
      location: this.location,
      merchant: this.merchant,
      tags: this.tags,
      type: this.type,
      account: this.account,
      currency: this.currency,
      receipt: this.receipt,
      source: this.source,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  toLocalFormat() {
    return {
      id: this.id,
      uid: this.uid,
      amount: this.amount,
      category: this.category,
      sub_category: this.subCategory,
      description: this.description,
      date: this.date,
      payment_method: this.paymentMethod,
      location: this.location,
      merchant: this.merchant,
      tags: JSON.stringify(this.tags),
      type: this.type,
      account: this.account,
      currency: this.currency,
      receipt: this.receipt,
      source: this.source,
      created_at: this.createdAt,
      updated_at: this.updatedAt,
      status: 'pending'
    };
  }
}

/**
 * 待办任务数据模型
 */
export class TodoModel {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.uid = data.uid || null;
    this.title = data.title || '';
    this.description = data.description || '';
    this.completed = data.completed || false;
    this.priority = data.priority || 3; // 1-5, 5最高
    this.urgent = data.urgent || false;
    this.important = data.important || false;
    this.dueDate = data.dueDate || null;
    this.tags = data.tags || [];
    this.category = data.category || 'general';
    this.project = data.project || '';
    this.estimatedMinutes = data.estimatedMinutes || null;
    this.actualMinutes = data.actualMinutes || null;
    this.completedAt = data.completedAt || null;
    this.reminder = data.reminder || null;
    this.subtasks = data.subtasks || [];
    this.source = data.source || 'manual'; // manual, voice, import
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  validate() {
    const errors = [];
    
    if (!this.title || this.title.trim().length === 0) {
      errors.push('标题不能为空');
    }
    
    if (this.priority && (this.priority < 1 || this.priority > 5)) {
      errors.push('优先级必须在1-5范围内');
    }
    
    if (this.dueDate && isNaN(new Date(this.dueDate).getTime())) {
      errors.push('截止日期格式无效');
    }
    
    if (this.estimatedMinutes && (this.estimatedMinutes < 0 || this.estimatedMinutes > 1440)) {
      errors.push('预估时间必须在0-1440分钟范围内');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 完成任务
  complete() {
    this.completed = true;
    this.completedAt = new Date().toISOString();
    this.updatedAt = new Date().toISOString();
  }

  // 取消完成
  uncomplete() {
    this.completed = false;
    this.completedAt = null;
    this.updatedAt = new Date().toISOString();
  }

  // 计算四象限分类
  getQuadrant() {
    if (this.urgent && this.important) return 1; // 重要且紧急
    if (!this.urgent && this.important) return 2; // 重要不紧急
    if (this.urgent && !this.important) return 3; // 紧急不重要
    return 4; // 不重要不紧急
  }

  toCloudFormat() {
    return {
      id: this.id,
      uid: this.uid,
      title: this.title,
      description: this.description,
      completed: this.completed,
      priority: this.priority,
      urgent: this.urgent,
      important: this.important,
      dueDate: this.dueDate,
      tags: this.tags,
      category: this.category,
      project: this.project,
      estimatedMinutes: this.estimatedMinutes,
      actualMinutes: this.actualMinutes,
      completedAt: this.completedAt,
      reminder: this.reminder,
      subtasks: this.subtasks,
      source: this.source,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  toLocalFormat() {
    return {
      id: this.id,
      uid: this.uid,
      title: this.title,
      description: this.description,
      completed: this.completed ? 1 : 0,
      priority: this.priority,
      urgent: this.urgent ? 1 : 0,
      important: this.important ? 1 : 0,
      due_date: this.dueDate,
      tags: JSON.stringify(this.tags),
      category: this.category,
      project: this.project,
      estimated_minutes: this.estimatedMinutes,
      actual_minutes: this.actualMinutes,
      completed_at: this.completedAt,
      reminder: this.reminder,
      subtasks: JSON.stringify(this.subtasks),
      source: this.source,
      created_at: this.createdAt,
      updated_at: this.updatedAt,
      status: 'pending'
    };
  }
}

/**
 * 数据模型工厂
 */
export class DataModelFactory {
  static create(type, data) {
    switch (type) {
      case 'schedule':
        return new ScheduleModel(data);
      case 'expense':
        return new ExpenseModel(data);
      case 'todo':
        return new TodoModel(data);
      default:
        throw new Error(`不支持的数据类型: ${type}`);
    }
  }

  static getCollectionName(type) {
    const mapping = {
      'schedule': 'schedules',
      'expense': 'expenses',
      'todo': 'todos'
    };
    return mapping[type] || type;
  }

  static getLocalTableName(type) {
    const mapping = {
      'schedule': 'local_schedules',
      'expense': 'local_expenses',
      'todo': 'local_todos'
    };
    return mapping[type] || `local_${type}s`;
  }
}

/**
 * 数据转换工具
 */
export class DataConverter {
  // 从本地SQLite格式转换为标准格式
  static fromLocalFormat(type, localData) {
    switch (type) {
      case 'schedule':
        return {
          id: localData.id,
          uid: localData.uid,
          title: localData.title,
          description: localData.description,
          startTime: localData.start_time,
          endTime: localData.end_time,
          allDay: localData.all_day === 1,
          location: localData.location,
          reminder: localData.reminder,
          reminderType: localData.reminder_type || localData.reminder || 'none',
          repeatRule: localData.repeat_rule,
          tags: localData.tags ? JSON.parse(localData.tags) : [],
          color: localData.color,
          calendar: localData.calendar,
          status: localData.status || 'pending',
          source: localData.source,
          createdAt: localData.created_at,
          updatedAt: localData.updated_at
        };
      case 'expense':
        return {
          id: localData.id,
          uid: localData.uid,
          amount: localData.amount,
          category: localData.category,
          subCategory: localData.sub_category,
          description: localData.description,
          date: localData.date,
          paymentMethod: localData.payment_method,
          location: localData.location,
          merchant: localData.merchant,
          tags: localData.tags ? JSON.parse(localData.tags) : [],
          type: localData.type,
          account: localData.account,
          currency: localData.currency,
          receipt: localData.receipt,
          source: localData.source,
          createdAt: localData.created_at,
          updatedAt: localData.updated_at
        };
      case 'todo':
        return {
          id: localData.id,
          uid: localData.uid,
          title: localData.title,
          description: localData.description,
          completed: localData.completed === 1,
          priority: localData.priority,
          urgent: localData.urgent === 1,
          important: localData.important === 1,
          dueDate: localData.due_date,
          tags: localData.tags ? JSON.parse(localData.tags) : [],
          category: localData.category,
          project: localData.project,
          estimatedMinutes: localData.estimated_minutes,
          actualMinutes: localData.actual_minutes,
          completedAt: localData.completed_at,
          reminder: localData.reminder,
          subtasks: localData.subtasks ? JSON.parse(localData.subtasks) : [],
          source: localData.source,
          createdAt: localData.created_at,
          updatedAt: localData.updated_at
        };
      default:
        return localData;
    }
  }
} 