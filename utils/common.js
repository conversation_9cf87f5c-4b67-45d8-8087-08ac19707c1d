// utils/common.js - 通用工具函数

/**
 * 安全的ActionSheet显示函数
 * 自动处理用户取消操作，避免错误日志
 * @param {Object} options - ActionSheet配置
 * @param {Array} options.itemList - 菜单项列表
 * @param {Function} options.success - 成功回调
 * @param {Function} options.fail - 失败回调（可选）
 * @param {Function} options.complete - 完成回调（可选）
 * @param {Boolean} options.silentCancel - 是否静默处理取消操作（默认true）
 */
function safeShowActionSheet(options = {}) {
  const { itemList, success, fail, complete, silentCancel = true } = options;
  
  if (!itemList || !Array.isArray(itemList) || itemList.length === 0) {
    console.error('⚠️ safeShowActionSheet: itemList 参数无效');
    return;
  }
  
  wx.showActionSheet({
    itemList: itemList,
    success: (res) => {
      if (typeof success === 'function') {
        success(res);
      }
    },
    fail: (err) => {
      // 处理用户取消操作
      if (err.errMsg && err.errMsg.includes('cancel')) {
        if (silentCancel) {
          console.log('📋 用户取消了ActionSheet操作');
        } else {
          console.warn('📋 ActionSheet被用户取消:', err.errMsg);
        }
      } else {
        // 其他错误，调用用户提供的fail回调
        console.error('❌ ActionSheet显示失败:', err);
        if (typeof fail === 'function') {
          fail(err);
        }
      }
    },
    complete: (res) => {
      if (typeof complete === 'function') {
        complete(res);
      }
    }
  });
}

/**
 * 格式化日期字符串
 * @param {Date} date - 日期对象
 * @returns {string} - YYYY-MM-DD格式的日期字符串
 */
function formatDate(date = new Date()) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 格式化时间字符串
 * @param {Date} date - 日期对象
 * @returns {string} - HH:MM格式的时间字符串
 */
function formatTime(date = new Date()) {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
}

/**
 * 安全的存储获取
 * @param {string} key - 存储键名
 * @param {*} defaultValue - 默认值
 * @returns {*} - 存储的值或默认值
 */
function safeGetStorage(key, defaultValue = null) {
  try {
    const value = wx.getStorageSync(key);
    return value !== '' ? value : defaultValue;
  } catch (error) {
    console.error(`获取存储失败 ${key}:`, error);
    return defaultValue;
  }
}

/**
 * 安全的存储设置
 * @param {string} key - 存储键名
 * @param {*} value - 要存储的值
 * @returns {boolean} - 是否成功
 */
function safeSetStorage(key, value) {
  try {
    wx.setStorageSync(key, value);
    return true;
  } catch (error) {
    console.error(`设置存储失败 ${key}:`, error);
    return false;
  }
}

/**
 * 显示错误提示
 * @param {string} message - 错误消息
 * @param {number} duration - 显示时长（毫秒）
 */
function showError(message, duration = 3000) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: duration
  });
}

/**
 * 显示成功提示
 * @param {string} message - 成功消息
 * @param {number} duration - 显示时长（毫秒）
 */
function showSuccess(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: duration
  });
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} - 防抖后的函数
 */
function debounce(func, delay = 300) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 节流间隔（毫秒）
 * @returns {Function} - 节流后的函数
 */
function throttle(func, delay = 300) {
  let lastTime = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      lastTime = now;
      func.apply(this, args);
    }
  };
}

// 导出工具函数
module.exports = {
  safeShowActionSheet,
  formatDate,
  formatTime,
  safeGetStorage,
  safeSetStorage,
  showError,
  showSuccess,
  debounce,
  throttle
}; 