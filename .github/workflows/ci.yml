name: WeApp CI/CD
on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: 设置 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 运行检查
        run: |
          npm run icons:check
          npm run starter:init
          npm run lint
          
      - name: 构建检查
        run: npm run tdesign:build
        
  upload-preview:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/dev'
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: 设置 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 生成体验版
        env:
          MINIPROGRAM_PRIVATE_KEY: ${{ secrets.MINIPROGRAM_PRIVATE_KEY }}
        run: |
          echo "暂时跳过上传，需要配置私钥"
          # echo "$MINIPROGRAM_PRIVATE_KEY" > private.key
          # npm run ci:upload
          
  upload-production:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: 设置 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 发布生产版
        env:
          MINIPROGRAM_PRIVATE_KEY: ${{ secrets.MINIPROGRAM_PRIVATE_KEY }}
        run: |
          echo "暂时跳过上传，需要配置私钥"
          # echo "$MINIPROGRAM_PRIVATE_KEY" > private.key
          # npm run build