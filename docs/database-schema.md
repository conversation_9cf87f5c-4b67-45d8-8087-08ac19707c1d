# 数据库架构设计

## 1. 用户集合 (users)

用户基本信息表，用于存储用户身份和设置。

```typescript
interface User {
  _id: string;          // 用户openid
  unionid?: string;     // 微信unionid
  nick: string;         // 昵称
  avatar: string;       // 头像URL
  gender: number;       // 性别：0未知，1男，2女
  country: string;      // 国家
  province: string;     // 省份
  city: string;         // 城市
  language: string;     // 语言
  createdAt: Date;      // 注册时间
  lastLoginAt: Date;    // 最后登录时间
  lastLoginIP: string;  // 最后登录IP
  proUntil?: Date;      // Pro订阅到期时间
  settings: {           // 用户设置
    theme: 'auto' | 'light' | 'dark';
    notifications: boolean;
    syncEnabled: boolean;
  };
  totalUsageDays: number;   // 累计使用天数
  totalRecords: number;     // 累计记录数
}
```

## 2. 日程集合 (schedules)

用户的日程安排数据。

```typescript
interface Schedule {
  _id: string;
  uid: string;          // 用户ID (外键)
  title: string;        // 日程标题
  description?: string; // 详细描述
  startTime: Date;      // 开始时间
  endTime?: Date;       // 结束时间
  allDay: boolean;      // 是否全天
  location?: string;    // 地点
  remind?: number;      // 提醒时间（分钟）
  repeat?: {            // 重复设置
    type: 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;   // 间隔
    endDate?: Date;     // 重复结束日期
  };
  source: 'voice' | 'manual' | 'import';  // 来源
  status: 'active' | 'completed' | 'cancelled';
  tags: string[];       // 标签
  calendarSynced: boolean;  // 是否已同步到日历
  createdAt: Date;
  updatedAt: Date;
}
```

## 3. 财务记录集合 (expenses)

用户的财务收支记录。

```typescript
interface Expense {
  _id: string;
  uid: string;          // 用户ID (外键)
  amount: number;       // 金额（负数为支出，正数为收入）
  category: string;     // 分类
  subcategory?: string; // 子分类
  description: string;  // 描述
  date: Date;          // 发生日期
  paymentMethod?: string; // 支付方式
  location?: string;    // 地点
  tags: string[];       // 标签
  source: 'voice' | 'manual' | 'ocr' | 'import';  // 来源
  ocrData?: {           // OCR识别数据
    originalImage: string;  // 原始图片URL
    confidence: number;     // 识别置信度
    rawText: string;       // 原始识别文本
  };
  status: 'pending' | 'confirmed' | 'disputed';
  createdAt: Date;
  updatedAt: Date;
}
```

## 4. 待办事项集合 (todos)

用户的待办任务。

```typescript
interface Todo {
  _id: string;
  uid: string;          // 用户ID (外键)
  title: string;        // 任务标题
  description?: string; // 详细描述
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  dueDate?: Date;       // 截止日期
  reminderTime?: Date;  // 提醒时间
  tags: string[];       // 标签
  subtasks: {           // 子任务
    title: string;
    completed: boolean;
  }[];
  source: 'voice' | 'manual';  // 来源
  quadrant: {           // 四象限分类
    important: boolean;
    urgent: boolean;
  };
  estimatedTime?: number; // 预估耗时（分钟）
  actualTime?: number;    // 实际耗时（分钟）
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}
```

## 5. 语音记录集合 (records)

用户的语音输入记录。

```typescript
interface VoiceRecord {
  _id: string;
  uid: string;          // 用户ID (外键)
  audioUrl?: string;    // 音频文件URL
  originalText: string; // 原始识别文本
  processedText?: string; // 处理后文本
  confidence: number;   // 识别置信度
  duration: number;     // 音频时长（秒）
  intent: {             // 意图识别结果
    type: 'schedule' | 'expense' | 'todo' | 'unknown';
    confidence: number;
    entities: {         // 实体识别
      time?: Date;
      amount?: number;
      location?: string;
      person?: string[];
    };
  };
  processed: boolean;   // 是否已处理
  targetRecordId?: string; // 关联的目标记录ID
  targetCollection?: string; // 目标集合名
  errorMessage?: string; // 错误信息
  createdAt: Date;
  updatedAt: Date;
}
```

## 6. 同步日志集合 (sync_logs)

数据同步记录。

```typescript
interface SyncLog {
  _id: string;
  uid: string;          // 用户ID (外键)
  type: 'calendar' | 'backup' | 'export';
  action: 'create' | 'update' | 'delete' | 'sync';
  targetId: string;     // 目标记录ID
  targetCollection: string; // 目标集合
  status: 'pending' | 'success' | 'failed' | 'partial';
  errorMessage?: string;
  metadata: any;        // 同步元数据
  retryCount: number;   // 重试次数
  createdAt: Date;
  completedAt?: Date;
}
```

## 7. 用户设置集合 (user_settings)

扩展的用户设置（可选）。

```typescript
interface UserSetting {
  _id: string;
  uid: string;          // 用户ID (外键)
  key: string;          // 设置键
  value: any;           // 设置值
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  category: string;     // 设置分类
  createdAt: Date;
  updatedAt: Date;
}
```

## 数据隔离策略

### 1. 用户数据隔离
- 所有用户数据都通过 `uid` 字段进行隔离
- 查询时必须带上用户条件：`{ uid: context.OPENID }`
- 云函数中强制检查用户身份

### 2. 访客数据隔离
- 访客模式数据仅存储在本地
- 本地使用 SQLite 进行数据存储
- 访客升级为正式用户时，提供数据导入功能

### 3. 数据安全
- 敏感数据加密存储
- 删除用户时级联删除所有相关数据
- 定期清理过期的临时数据

## 索引策略

### 1. 基础索引
```javascript
// users 集合
db.collection('users').createIndex({ "lastLoginAt": -1 });

// schedules 集合  
db.collection('schedules').createIndex({ "uid": 1, "startTime": 1 });
db.collection('schedules').createIndex({ "uid": 1, "status": 1 });

// expenses 集合
db.collection('expenses').createIndex({ "uid": 1, "date": -1 });
db.collection('expenses').createIndex({ "uid": 1, "category": 1 });

// todos 集合
db.collection('todos').createIndex({ "uid": 1, "status": 1, "priority": -1 });
db.collection('todos').createIndex({ "uid": 1, "dueDate": 1 });

// records 集合
db.collection('records').createIndex({ "uid": 1, "createdAt": -1 });
db.collection('records').createIndex({ "uid": 1, "processed": 1 });
```

### 2. 复合索引
```javascript
// 日程查询优化
db.collection('schedules').createIndex({ 
  "uid": 1, 
  "startTime": 1, 
  "status": 1 
});

// 财务统计优化
db.collection('expenses').createIndex({ 
  "uid": 1, 
  "date": -1, 
  "category": 1 
});
```

## 数据迁移策略

### 1. 版本管理
- 使用 `_version` 字段标识数据版本
- 云函数启动时检查数据版本
- 自动执行数据迁移脚本

### 2. 兼容性处理
- 新字段设置合理默认值
- 废弃字段逐步清理
- 提供数据格式转换工具

这个数据库架构支持用户数据隔离、Pro功能区分、访客模式等需求，为后续功能开发提供了坚实的基础。