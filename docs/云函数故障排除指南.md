# 云函数故障排除指南

## 问题描述

在使用微信小程序云开发时，遇到以下错误：

```
Error: Cannot find module 'wx-server-sdk'
errCode: -504002 functions execute fail
```

## 问题原因

这个错误表示云函数在运行时找不到 `wx-server-sdk` 模块，通常由以下原因造成：

1. **依赖未安装**：云函数目录下缺少 `node_modules`
2. **部署问题**：云函数部署时未正确上传依赖
3. **版本兼容性**：wx-server-sdk版本与云函数环境不兼容
4. **网络问题**：部署时网络中断导致依赖安装失败

## 解决方案

### 方案一：本地安装依赖（推荐）

1. **进入云函数目录**：
```bash
cd cloudfunctions/writeRecord
```

2. **安装依赖**：
```bash
npm install
```

3. **验证安装**：
```bash
ls node_modules/wx-server-sdk
```

4. **重复其他云函数**：
```bash
cd ../userLogin && npm install
cd ../deleteAccount && npm install
```

### 方案二：使用自动化脚本

我们提供了自动化脚本来检查和修复所有云函数：

```bash
node scripts/deploy-cloud-functions.js
```

脚本功能：
- 🔍 检查所有云函数依赖状态
- 📦 自动安装缺失的依赖
- ✅ 验证代码语法正确性
- 📊 生成详细的状态报告

### 方案三：微信开发者工具部署

1. **打开微信开发者工具**
2. **右键点击云函数目录**（如 `writeRecord`）
3. **选择"上传并部署（云端安装依赖）"**
4. **等待部署完成**
5. **重复其他云函数**

### 方案四：清理重装

如果上述方案无效，尝试完全重装：

```bash
# 清理所有依赖
rm -rf cloudfunctions/*/node_modules
rm -rf cloudfunctions/*/package-lock.json

# 重新安装
cd cloudfunctions/writeRecord && npm install
cd ../userLogin && npm install  
cd ../deleteAccount && npm install
```

## 验证修复

### 1. 检查本地依赖

确保每个云函数目录下都有：
```
cloudfunctions/
├── writeRecord/
│   ├── index.js
│   ├── package.json
│   └── node_modules/
│       └── wx-server-sdk/    ✅ 关键模块
├── userLogin/
│   └── ...
└── deleteAccount/
    └── ...
```

### 2. 测试云函数

在小程序中执行一个简单的云函数调用：

```javascript
wx.cloud.callFunction({
  name: 'writeRecord',
  data: {
    action: 'test'
  }
}).then(res => {
  console.log('云函数测试成功:', res);
}).catch(err => {
  console.error('云函数测试失败:', err);
});
```

### 3. 查看云函数日志

在微信开发者工具的云开发控制台中：
1. 进入 **云开发** → **云函数**
2. 点击对应的云函数
3. 查看 **日志** 选项卡
4. 确认没有模块加载错误

## 预防措施

### 1. 标准化package.json

确保每个云函数都有正确的 `package.json`：

```json
{
  "name": "writeRecord",
  "version": "1.0.0",
  "description": "统一数据写入云函数",
  "main": "index.js",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

### 2. 版本锁定

使用 `package-lock.json` 锁定依赖版本：
```bash
npm install --save-exact wx-server-sdk@2.6.3
```

### 3. 部署检查清单

每次部署前检查：
- [ ] `package.json` 存在且配置正确
- [ ] 本地已运行 `npm install`
- [ ] `node_modules/wx-server-sdk` 存在
- [ ] 代码中正确引用：`const cloud = require('wx-server-sdk')`
- [ ] 使用"云端安装依赖"选项部署

## 常见错误处理

### 错误1：模块版本冲突
```
Error: Package subpath './package.json' is not defined
```

**解决方案**：升级到最新版本
```bash
npm install wx-server-sdk@latest
```

### 错误2：权限问题
```
Error: EACCES: permission denied
```

**解决方案**：检查文件权限
```bash
sudo chown -R $USER cloudfunctions/
```

### 错误3：网络超时
```
Error: timeout
```

**解决方案**：使用国内镜像
```bash
npm install --registry https://registry.npmmirror.com
```

## 监控和维护

### 1. 定期检查

设置定期检查脚本：
```bash
# 添加到 package.json scripts
"scripts": {
  "check-cloud": "node scripts/deploy-cloud-functions.js",
  "fix-cloud": "cd cloudfunctions && find . -name package.json -execdir npm install \\;"
}
```

### 2. 日志监控

在云函数中添加监控代码：
```javascript
const cloud = require('wx-server-sdk');

// 检查SDK加载状态
console.log('wx-server-sdk version:', require('wx-server-sdk/package.json').version);

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
```

### 3. 错误报告

实现错误自动报告：
```javascript
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // 发送错误报告到监控系统
});
```

## 技术细节

### wx-server-sdk 作用

`wx-server-sdk` 是微信云开发的服务端SDK，提供：
- 数据库操作
- 云存储管理  
- 云函数调用
- 用户身份验证
- 消息推送

### 加载机制

云函数运行时的模块加载顺序：
1. 检查本地 `node_modules`
2. 检查全局模块
3. 从云端缓存加载
4. 如果都没有，报告模块缺失错误

### 最佳实践

1. **环境隔离**：为不同环境使用不同的云函数版本
2. **依赖最小化**：只安装必要的依赖
3. **版本管理**：使用语义化版本控制
4. **错误处理**：完善的错误捕获和日志记录

## 总结

`wx-server-sdk` 模块缺失是云开发中常见的问题，通过本指南的解决方案，您应该能够：

✅ 快速诊断问题根源  
✅ 选择合适的修复方案  
✅ 验证修复效果  
✅ 预防类似问题再次发生  

如果问题仍然存在，请检查：
1. 微信开发者工具版本是否最新
2. 云开发环境是否正常
3. 网络连接是否稳定
4. 云函数配额是否充足 