# 📱 一句话全能助手 - 使用指南

## 🚀 快速开始

### 1. 环境准备
- 安装微信开发者工具 (推荐最新版本)
- Node.js 20+ 和 pnpm 9+

### 2. 项目启动步骤

#### Step 1: 安装依赖
```bash
pnpm install
```

#### Step 2: 构建图标（如果需要）
```bash
pnpm run icons:check  # 检查图标状态
pnpm run icons:temp   # 创建临时图标（已创建）
```

#### Step 3: 在微信开发者工具中构建NPM
1. 打开微信开发者工具
2. 导入项目（选择项目根目录）
3. 点击菜单：工具 → 构建 npm
4. 等待构建完成

#### Step 4: 运行项目
- 点击"编译"按钮
- 选择"真机调试"或使用模拟器预览

## 📋 功能模块说明

### 🏠 今日页面 (`pages/index`)
- **功能**: 显示当日日程安排
- **特色**: 支持日期导航、日程管理
- **操作**: 点击添加按钮创建新任务

### 📊 交易记录 (`pages/transactions`) 
- **功能**: 财务交易记录管理
- **特色**: 分类筛选、金额统计
- **操作**: 支持添加、编辑、删除交易

### 💰 财务概览 (`pages/finances`)
- **功能**: 收支分析和预算管理
- **特色**: 可视化图表、趋势分析
- **操作**: 查看详细财务数据

### ✅ 任务管理 (`pages/tasks`)
- **功能**: 四象限任务管理
- **特色**: 重要紧急矩阵、拖拽操作
- **操作**: 任务分类、优先级设置

### ➕ 新建任务 (`pages/add-task`)
- **功能**: 语音/文本输入创建任务
- **特色**: NLP智能识别、快速分类
- **操作**: 语音识别、自动归类

### ✔️ 确认页面 (`pages/confirm`)
- **功能**: 任务信息确认和编辑
- **特色**: 智能分类建议、表单验证
- **操作**: 信息确认、类别选择

## 🎨 UI设计规范

### 颜色方案
- **主色**: `#1296db` (蓝色)
- **成功色**: `#52c41a` (绿色)  
- **警告色**: `#faad14` (橙色)
- **错误色**: `#ff4d4f` (红色)

### 组件使用
- 基于 **TDesign 小程序组件库**
- 支持深色模式
- 响应式布局

## 🔧 开发工具

### 可用脚本
```bash
# 图标管理
pnpm run icons:check     # 检查图标状态
pnpm run icons:temp      # 创建临时图标
pnpm run icons:generate  # 生成SVG模板

# 开发构建
pnpm run build:npm       # 构建提示（需在开发者工具中操作）
pnpm run dev            # 预览（需配置CI）
pnpm run build          # 打包上传（需配置CI）
```

## 📱 页面导航结构

```
TabBar导航:
├── 今日 (pages/index) - 日程管理主页
├── 记录 (pages/transactions) - 交易记录
└── 财务 (pages/finances) - 财务概览

子页面:
├── 新任务 (pages/add-task) - 语音输入创建
├── 确认 (pages/confirm) - 信息确认
├── 任务管理 (pages/tasks) - 四象限视图
└── 周度总结 (pages/summary) - 数据分析
```

## 🎯 核心功能流程

### 语音添加任务流程
1. 首页点击"语音输入"按钮
2. 跳转到新任务页面 → 语音识别
3. NLP解析用户意图和参数
4. 跳转确认页面 → 编辑详细信息
5. 确认保存 → 返回首页

### 财务记录流程  
1. 交易记录页面 → 点击添加
2. 语音/文本输入消费信息
3. 自动识别金额、商户、分类
4. 确认页面补充信息
5. 保存到数据库 → 更新财务概览

## ⚠️ 注意事项

### 开发阶段
- 当前使用临时图标，正式发布前需替换
- 语音识别功能需要在真机上测试
- NLP解析目前为模拟实现

### 部署前检查
- [ ] 替换正式应用图标
- [ ] 配置真实的云函数
- [ ] 设置正确的域名白名单
- [ ] 完成微信小程序认证

## 🐛 故障排查

### 常见问题
1. **TDesign组件不显示**: 确保已在开发者工具中构建npm
2. **图标缺失**: 运行 `pnpm run icons:temp` 创建临时图标
3. **页面跳转失败**: 检查 app.json 中的页面路径配置
4. **样式异常**: 确认页面json中的组件引用正确

### 调试建议
- 使用真机调试测试语音功能
- 检查控制台输出的错误信息
- 验证数据格式和API调用

---

🎉 **项目已准备就绪！现在可以在微信开发者工具中打开并开始开发了。**