# 📱 图标管理指南

## 🎯 问题解决方案

您遇到的图标文件缺失问题已经解决！现在项目中包含了完整的图标管理系统。

## 🚀 快速开始

### 1. 检查图标状态
```bash
pnpm run icons:check
```

### 2. 创建临时图标（开发用）
```bash
pnpm run icons:temp
```

### 3. 生成 SVG 模板
```bash
pnpm run icons:generate
```

## 📁 文件结构

```
assets/icons/
├── home.png              # 首页图标
├── home_selected.png     # 首页选中图标
├── chat.png              # 对话图标
├── chat_selected.png     # 对话选中图标
├── profile.png           # 我的图标
├── profile_selected.png  # 我的选中图标
├── home.svg              # SVG 模板文件
├── chat.svg              # SVG 模板文件
├── profile.svg           # SVG 模板文件
└── README.md             # 图标说明文档
```

## 🎨 图标规范

### TabBar 图标
- **尺寸**: 81x81px (推荐)
- **格式**: PNG
- **背景**: 透明
- **线条粗细**: 2px
- **颜色**: 
  - 未选中: `#666666`
  - 选中: `#1296db`

### 应用图标
- **小程序图标**: 120x120px, 180x180px
- **审核用图标**: 1024x1024px
- **格式**: PNG
- **圆角**: 20px (自动处理)

## 🔧 开发工作流

### 方案一：使用临时图标（推荐开发阶段）
1. 运行 `pnpm run icons:temp` 创建最小图标
2. 开始开发，不会因图标缺失而阻塞
3. 后续替换为正式图标

### 方案二：创建正式图标
1. 运行 `pnpm run icons:generate` 生成 SVG 模板
2. 访问 [SVG to PNG 转换器](https://svgtopng.com/)
3. 上传 SVG 文件，设置尺寸为 81x81px
4. 下载 PNG 文件，替换 `assets/icons/` 中的文件

### 方案三：使用设计工具
1. 使用 Figma、Sketch 或 Adobe Illustrator
2. 参考 `assets/icons/*.svg` 中的设计
3. 导出为 81x81px PNG 格式
4. 确保背景透明

## 🛠️ 常用命令

| 命令 | 功能 | 使用场景 |
|------|------|----------|
| `pnpm run icons:check` | 检查图标状态 | 开发前检查 |
| `pnpm run icons:temp` | 创建临时图标 | 快速解决缺失 |
| `pnpm run icons:generate` | 生成 SVG 模板 | 设计参考 |

## ⚠️ 注意事项

1. **开发阶段**: 临时图标足够使用，不影响功能开发
2. **提交审核前**: 必须替换为正式设计的图标
3. **图标缓存**: 微信开发者工具可能缓存图标，替换后需要重新编译
4. **真机测试**: 图标在真机上的显示效果可能与模拟器不同

## 🔍 故障排查

### 问题：图标不显示
- 检查文件路径是否正确
- 确认文件格式为 PNG
- 运行 `pnpm run icons:check` 检查状态

### 问题：图标模糊
- 确保图标尺寸为 81x81px
- 检查是否为矢量图导出
- 避免拉伸小尺寸图标

### 问题：编译错误
- 确保所有 `app.json` 中引用的图标文件都存在
- 运行 `pnpm run icons:temp` 创建临时文件

## 📞 技术支持

如果遇到图标相关问题：
1. 首先运行 `pnpm run icons:check` 诊断
2. 查看控制台输出的修复建议
3. 参考本文档的解决方案

---

✅ **当前状态**: 图标问题已解决，可以正常开发！