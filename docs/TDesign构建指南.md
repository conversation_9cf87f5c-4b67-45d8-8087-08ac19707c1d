# TDesign 组件库构建指南

## 🎯 问题描述

当在微信小程序中使用TDesign组件时，遇到以下错误：
```
["usingComponents"]["t-button"]: "tdesign-miniprogram/button/button"，
在 tdesign-miniprogram/button/button 路径下未找到组件
```

这是因为微信小程序需要将`node_modules`中的npm包构建到`miniprogram_npm`目录才能使用。

## 🔧 解决方案

### 方法一：微信开发者工具构建（推荐）

#### 1. 确保项目配置正确

运行我们的自动检查脚本：
```bash
npm run tdesign:build
```

脚本会自动检查和修复：
- ✅ project.config.json 配置
- ✅ package.json 依赖
- ✅ node_modules 安装状态

#### 2. 在微信开发者工具中构建

**步骤详解：**

1. **打开微信开发者工具**
   - 确保版本 ≥ 1.06.0（推荐 1.07.0+）

2. **导入项目**
   ```
   项目路径: /Volumes/panyuchuan/助手
   ```

3. **进入构建流程**
   - 点击顶部菜单：`工具` → `构建 npm`
   - ![构建npm菜单](./assets/build-npm-menu.png)

4. **构建配置**
   在弹出的对话框中：
   - ✅ **勾选** "使用 npm 模块"
   - ✅ **勾选** "构建完成后是否自动注入编译阶段"
   - ![构建配置](./assets/build-npm-dialog.png)

5. **执行构建**
   - 点击 **"构建"** 按钮
   - 等待构建完成（通常需要10-30秒）
   - 看到绿色的 **"构建完成"** 提示

6. **验证构建结果**
   检查项目根目录是否生成了 `miniprogram_npm` 文件夹：
   ```
   miniprogram_npm/
   └── tdesign-miniprogram/
       ├── button/
       ├── icon/
       ├── cell/
       ├── popup/
       └── ...
   ```

#### 3. 测试组件是否正常

1. **访问测试页面**
   - 在开发者工具中，点击 **编译**
   - 在模拟器中访问：`pages/tdesign-test/tdesign-test`

2. **检查控制台**
   - 无组件路径错误
   - 页面正常显示TDesign组件

## 🚨 常见问题与解决

### 问题1：构建失败

**错误提示：**
```
构建 npm 失败，请检查 package.json 依赖
```

**解决方案：**
```bash
# 重新安装依赖
npm install
# 或者
pnpm install

# 然后重新构建
```

### 问题2：构建后仍然报错

**错误提示：**
```
组件路径未找到
```

**解决方案：**
1. 删除 `miniprogram_npm` 文件夹
2. 重新构建npm
3. 检查组件引用路径是否正确

### 问题3：部分组件缺失

**解决方案：**
```bash
# 检查构建状态
node scripts/build-tdesign.js

# 查看缺失的组件
ls -la miniprogram_npm/tdesign-miniprogram/
```

### 问题4：权限问题

**错误提示：**
```
没有权限创建 miniprogram_npm 文件夹
```

**解决方案：**
- macOS/Linux: 使用 `sudo` 运行开发者工具
- Windows: 以管理员身份运行开发者工具

## 🎯 验证清单

构建完成后，请确认以下项目：

- [ ] `miniprogram_npm/tdesign-miniprogram` 文件夹存在
- [ ] 关键组件文件存在：
  - [ ] `button/button.js`
  - [ ] `icon/icon.js`
  - [ ] `cell/cell.js`
  - [ ] `popup/popup.js`
- [ ] 项目编译无错误
- [ ] 测试页面 `pages/tdesign-test/` 可以正常访问
- [ ] TDesign组件可以正常显示

## 📱 真机测试

构建完成后，建议进行真机测试：

1. **生成预览二维码**
   - 在开发者工具中点击 **预览**
   - 用微信扫描二维码

2. **测试关键功能**
   - 登录页面的按钮和图标
   - 用户中心的头像和单元格
   - 首页的FAB和列表组件

## 🔄 持续集成

为了避免团队成员遇到同样问题，建议：

1. **在README中添加构建说明**
2. **在git中忽略miniprogram_npm**（已配置）
3. **每个开发者首次克隆后都需要构建npm**

## 🚀 自动化脚本

我们提供了自动化脚本来简化流程：

```bash
# 检查构建状态和依赖
npm run tdesign:build

# 环境检查（包含TDesign检查）
npm run env:check

# 完整验证（包含组件测试）
node scripts/verify-sprint1.js
```

## 📈 项目状态

当前TDesign相关配置：

- **依赖版本**: `tdesign-miniprogram@^1.9.4`
- **项目配置**: ✅ `packNpmManually: true`
- **组件使用**: 登录页、用户中心、首页、测试页
- **构建状态**: 🔄 需要在开发者工具中构建

## 🎉 完成确认

当你看到以下提示时，说明TDesign构建成功：

```
✅ 所有TDesign组件正常显示
✅ 控制台无组件路径错误
✅ 真机测试通过
✅ 团队成员都能正常开发
```

---

**📞 如需帮助**

如果按照本指南操作后仍有问题，请：
1. 检查微信开发者工具版本（推荐1.07.0+）
2. 确认node_modules中有tdesign-miniprogram
3. 运行 `npm run tdesign:build` 获取诊断信息 