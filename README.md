# 🤖 一句话全能助手 - 微信小程序

> 语音记录，智能分类，轻松管理

一个基于微信小程序的智能助手，支持语音输入、自动分类日程、财务记录和待办任务。采用现代化技术栈，提供流畅的用户体验。

## ✨ 核心特性

🎤 **语音输入** - 支持流式语音识别，准确率高  
📅 **智能日程** - 自动解析时间和事件，同步到系统日历  
💰 **财务管理** - 语音记账，支持OCR识别小票  
✅ **待办管理** - 四象限任务管理，拖拽操作  
☁️ **云端同步** - 数据实时同步，离线操作支持  
🎨 **现代UI** - 基于TDesign设计系统，响应式适配  

## 🚀 快速开始

### 环境要求

- Node.js 20+
- pnpm 9+
- 微信开发者工具 1.07+

### 安装依赖

```bash
# 克隆项目
git clone <your-repo-url>
cd assistant-miniprogram

# 安装依赖
pnpm install

# 构建TDesign组件
npm run tdesign:build
```

### 配置CloudBase环境

1. 访问 [腾讯云控制台](https://console.cloud.tencent.com/tcb)
2. 创建云开发环境
3. 更新`app.js`中的环境ID

```javascript
// app.js
wx.cloud.init({
  env: 'your-environment-id', // 替换为你的环境ID
  traceUser: true,
});
```

### 创建数据库集合

```bash
# 查看数据库设计
npm run db:init
```

按照输出的指南在CloudBase控制台创建以下集合：
- `users` - 用户信息表
- `schedules` - 日程记录表  
- `expenses` - 财务记录表
- `todos` - 待办任务表
- `sync_queue` - 数据同步队列表

### 部署云函数

1. 在微信开发者工具中打开项目
2. 右键`cloudfunctions/userLogin`
3. 选择"上传并部署：云端安装依赖"

### 开发调试

```bash
# 生成图标文件
npm run icons:generate

# 检查环境配置
npm run env:check

# 本地预览
# 在微信开发者工具中预览
```

## 📱 功能模块

### 🔐 用户登录系统（当前进度：Sprint 1）

**状态**：🚧 开发中

**功能列表**：
- [x] 微信一键登录
- [x] 用户状态管理
- [x] 访客模式支持
- [x] 数据同步框架
- [ ] 云函数部署测试
- [ ] 端到端流程验证

**快速验证**：
1. 启动小程序
2. 进入登录页面
3. 点击"微信快捷登录"
4. 观察控制台日志

### 📅 日程管理（计划：Sprint 2-3）

- 语音添加日程
- 日历视图展示
- 系统日历同步
- 提醒通知

### 💰 财务管理（计划：Sprint 2-3）

- 语音记账
- 分类统计
- 月度报表
- OCR小票识别

### ✅ 待办管理（计划：Sprint 4）

- 四象限分类
- 拖拽操作
- 优先级管理
- 完成度统计

## 🛠️ 开发工具

### 常用脚本

```bash
# 数据库相关
npm run db:init          # 查看数据库设计
npm run cloud:deploy     # 云函数部署提示

# UI开发相关  
npm run tdesign:build    # 构建TDesign组件
npm run icons:generate   # 生成图标文件
npm run theme:set        # 设置主题色

# 页面开发
npm run page:new <name>  # 创建新页面

# 构建和部署
npm run build           # 构建项目
npm run ci:upload       # 上传体验版
```

### 项目结构

```
├── pages/              # 页面文件
│   ├── login/         # 登录页面
│   ├── input/         # 语音输入页
│   ├── index/         # 日程首页
│   ├── tasks/         # 待办管理
│   └── finances/      # 财务管理
├── utils/              # 工具类
│   ├── auth.js        # 用户认证
│   └── dataSync.js    # 数据同步
├── cloudfunctions/     # 云函数
│   ├── userLogin/     # 用户登录
│   └── deleteAccount/ # 账户注销
├── assets/            # 静态资源
├── scripts/           # 开发脚本
└── docs/              # 文档
```

## 📋 开发计划

当前处于 **Sprint 1** 阶段，专注于用户登录和身份管理系统。

### 里程碑

- **Week 1-2**：用户登录系统 ✅ 基本完成
- **Week 3-4**：数据持久化和同步
- **Week 5-6**：语音识别MVP
- **Week 7-8**：核心业务功能
- **Week 9-12**：高级功能和优化

详细计划见 [开发计划.md](./开发计划.md)

## 🔧 故障排除

### 常见问题

**Q: 云函数调用失败？**
A: 检查环境ID配置，确保云开发环境已激活

**Q: 用户登录失败？**  
A: 使用降级登录方案，检查网络连接和权限配置

**Q: 页面显示异常？**
A: 运行`npm run tdesign:build`重新构建组件库

**Q: 图标不显示？**
A: 运行`npm run icons:generate`生成图标文件

### 开发调试

- 查看微信开发者工具控制台日志
- 检查CloudBase控制台云函数日志  
- 使用TDesign组件调试工具
- 参考[用户登录系统实施指南](./docs/用户登录系统实施指南.md)

## 📖 文档

- [产品需求蓝皮书](./产品需求%20&%20技术实施蓝皮书%20%20v25.05.md)
- [开发计划](./开发计划.md)
- [用户登录系统实施指南](./docs/用户登录系统实施指南.md)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

## 📄 许可证

UNLICENSED - 私有项目

---

**技术栈**：微信小程序 + CloudBase + TDesign + 腾讯云ASR

## 部署指南

### 云开发环境配置

如果遇到云开发环境错误（如 `env check invalid` 或 `INVALID_ENV`），请按以下步骤解决：

#### 方法一：重新配置云开发环境

1. **登录微信开发者工具**
2. **打开云开发控制台**：
   - 在开发者工具中点击 "云开发" 按钮
   - 或直接访问 [腾讯云开发控制台](https://console.cloud.tencent.com/tcb)

3. **创建新环境**：
   - 点击 "新建环境"
   - 选择 "按量计费" 或 "包年包月"
   - 输入环境名称，如 `assistant-prod`
   - 等待环境创建完成

4. **更新环境ID**：
   - 复制新环境的环境ID
   - 在 `app.js` 中找到 `wx.cloud.init()` 
   - 取消注释并更新 `env` 参数：
   ```javascript
   wx.cloud.init({
     env: 'your-new-env-id', // 替换为新的环境ID
     traceUser: true,
   });
   ```

5. **部署云函数**：
   - 右键 `cloudfunctions` 文件夹
   - 选择 "上传并部署：云端安装依赖"
   - 等待部署完成

#### 方法二：使用默认环境（当前配置）

当前代码已配置为使用默认环境，这样可以避免环境ID问题：

```javascript
wx.cloud.init({
  // 不指定env，使用默认环境
  traceUser: true,
});
```

#### 方法三：临时解决方案

如果云开发暂时不可用，应用会自动切换到本地模式：

- ✅ 所有功能正常使用
- ✅ 数据保存在本地
- ✅ 云端恢复后自动同步
- ℹ️ 会显示"云端同步不可用"提示（可选择不再显示）

### 常见问题

**Q: 为什么会出现环境配置问题？**
A: 可能原因包括：
- 云开发环境被删除或停用
- 环境ID配置错误
- 网络连接问题
- 云开发服务暂时不可用

**Q: 云端不可用会影响使用吗？**
A: 不会。应用设计为本地优先，云端同步为辅助功能。即使云端不可用，所有核心功能都能正常使用。

**Q: 数据会丢失吗？**
A: 不会。数据首先保存在本地，云端同步只是备份。即使云端问题，本地数据都是安全的。

## 🎯 项目重构总结（2024年重大更新）

### 重构目标
基于《产品需求 & 技术实施蓝皮书 v25.05》，回归核心功能：
1. **语音输入** → **AI分类** → **数据存储** → **页面显示**
2. 专注于大模型调用分类和语音识别
3. 消除代码冗余，提高项目可维护性

### 🧹 清理成果
- **删除24个冗余文件**：包括各种调试指南、临时脚本、重复文档
- **代码行数减少90%**：input.js从4083行精简到~400行
- **方法数量减少70%**：从100+个方法精简到~30个核心方法
- **移除所有调试文件**：清理开发过程中的临时文件

### 🔧 核心重构亮点

#### 新的input.js架构
🎤 语音输入模块 - 微信原生API，10秒自动停止
🤖 AI分类模块 - DeepSeek + 硅基流动双重保障  
📱 UI交互模块 - 简洁的用户界面交互
🔧 调试工具模块 - 开发模式下的核心调试功能
```

#### 技术优化
- ✅ **AI服务集成**：强化相对日期处理提示词，支持"7天后去杭州"等自然语言
- ✅ **数据存储统一**：使用标准的`dataApi.createSchedule/createExpense/createTodo`
- ✅ **语音识别优化**：微信原生API + 错误处理和用户提示
- ✅ **调试功能**：开发模式下保留，生产环境自动隐藏

### 🚀 核心流程简化
```
用户输入(语音/文本) 
→ AI智能分类(相对日期+地点识别) 
→ 创建对应数据(日程/财务/待办)
→ 保存到本地存储
→ 各页面正确显示
```

### ✅ 重构成果
通过本次重构，项目实现了：
1. **回归核心**：专注于语音输入和AI分类的核心价值
2. **降低复杂度**：移除90%的冗余代码和调试工具
3. **提高可维护性**：清晰的代码结构和简化的逻辑
4. **符合蓝皮书**：严格按照产品需求文档实施
5. **便于迭代**：为后续功能扩展打下良好基础

### 📋 备份说明
- `pages/input/input_legacy.js` - 保留原复杂版本作为备份
- 核心功能保持完整，移除了过度复杂的调试和降级逻辑
- 所有删除的文档都是开发过程中的临时文件，不影响核心功能