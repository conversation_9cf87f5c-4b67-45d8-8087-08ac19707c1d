/**app.wxss**/

/* 根元素样式 */
page {
  --primary-color: #1296db;
  --success-color: #00c851;
  --warning-color: #ff9800;
  --danger-color: #ff4444;
  --info-color: #33b5e5;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-disabled: #999999;
  --background-color: #f5f5f5;
  --border-color: #e6e6e6;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* 全局字体和基础样式 */
page, view, text, button, input, textarea, label, picker, navigator, image {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  box-sizing: border-box;
}

/* 重置一些默认样式 */
page, view, text, button, input, textarea, label, picker, navigator, image {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 通用容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 常用布局类 */
.flex {
  display: flex;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* TDesign 字体 Fallback */
@font-face {
  font-family: 't-icon';
  src: url('https://tdesign.gtimg.com/icon/0.3.2/fonts/t.woff') format('woff');
  font-display: swap; /* 改善字体加载体验 */
}

/* 字体加载失败时的备用方案 */
.t-icon {
  font-family: 't-icon', 'iconfont', 'Material Icons', sans-serif !important;
}

/* 全局字体设置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 间距工具类 */
.mt-small { margin-top: 16rpx; }
.mt-medium { margin-top: 32rpx; }
.mt-large { margin-top: 48rpx; }

.mb-small { margin-bottom: 16rpx; }
.mb-medium { margin-bottom: 32rpx; }
.mb-large { margin-bottom: 48rpx; }

.ml-small { margin-left: 16rpx; }
.ml-medium { margin-left: 32rpx; }
.ml-large { margin-left: 48rpx; }

.mr-small { margin-right: 16rpx; }
.mr-medium { margin-right: 32rpx; }
.mr-large { margin-right: 48rpx; }

.p-small { padding: 16rpx; }
.p-medium { padding: 32rpx; }
.p-large { padding: 48rpx; }

/* 颜色工具类 */
.color-primary { color: #1976d2; }
.color-success { color: #4caf50; }
.color-warning { color: #ff9800; }
.color-error { color: #f44336; }
.color-text { color: #333; }
.color-text-secondary { color: #666; }
.color-text-disabled { color: #999; }

/* 背景色工具类 */
.bg-primary { background-color: #1976d2; }
.bg-success { background-color: #4caf50; }
.bg-warning { background-color: #ff9800; }
.bg-error { background-color: #f44336; }
.bg-white { background-color: #fff; }
.bg-gray { background-color: #f5f5f5; }

/* 暗黑模式 */
@media (prefers-color-scheme: dark) {
  page[data-theme="dark"] {
    --primary-color: #3a8ee6;
    --secondary-color: #4cd964;
    --background-color: #1a1a1a;
    --text-color: #f2f2f2;
    --text-color-light: #cccccc;
    --text-color-placeholder: #999999;
    --border-color: #333333;
  }
}

/* 间距 */
.margin-xs { margin: 10rpx; }
.margin-sm { margin: 20rpx; }
.margin { margin: 30rpx; }
.margin-lg { margin: 40rpx; }
.margin-xl { margin: 50rpx; }

.margin-top-xs { margin-top: 10rpx; }
.margin-top-sm { margin-top: 20rpx; }
.margin-top { margin-top: 30rpx; }
.margin-top-lg { margin-top: 40rpx; }
.margin-top-xl { margin-top: 50rpx; }

.margin-right-xs { margin-right: 10rpx; }
.margin-right-sm { margin-right: 20rpx; }
.margin-right { margin-right: 30rpx; }
.margin-right-lg { margin-right: 40rpx; }
.margin-right-xl { margin-right: 50rpx; }

.margin-bottom-xs { margin-bottom: 10rpx; }
.margin-bottom-sm { margin-bottom: 20rpx; }
.margin-bottom { margin-bottom: 30rpx; }
.margin-bottom-lg { margin-bottom: 40rpx; }
.margin-bottom-xl { margin-bottom: 50rpx; }

.margin-left-xs { margin-left: 10rpx; }
.margin-left-sm { margin-left: 20rpx; }
.margin-left { margin-left: 30rpx; }
.margin-left-lg { margin-left: 40rpx; }
.margin-left-xl { margin-left: 50rpx; }

/* 内边距 */
.padding-xs { padding: 10rpx; }
.padding-sm { padding: 20rpx; }
.padding { padding: 30rpx; }
.padding-lg { padding: 40rpx; }
.padding-xl { padding: 50rpx; }

/* 文本样式 */
.text-xs { font-size: 24rpx; }
.text-sm { font-size: 26rpx; }
.text-df { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-xxl { font-size: 44rpx; }

.text-bold { font-weight: bold; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-cut-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 颜色 */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-light { color: var(--text-color-light); }
.text-placeholder { color: var(--text-color-placeholder); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-white { background-color: #ffffff; }
.bg-gray { background-color: #f8f8f8; }

/* 边框 */
.border {
  border: 1rpx solid var(--border-color);
}

.border-top {
  border-top: 1rpx solid var(--border-color);
}

.border-right {
  border-right: 1rpx solid var(--border-color);
}

.border-bottom {
  border-bottom: 1rpx solid var(--border-color);
}

.border-left {
  border-left: 1rpx solid var(--border-color);
}

.radius-sm { border-radius: 4rpx; }
.radius { border-radius: 8rpx; }
.radius-lg { border-radius: 16rpx; }
.radius-xl { border-radius: 32rpx; }
.radius-round { border-radius: 1000rpx; }
.radius-circle { border-radius: 50%; }

/* 阴影 */
.shadow-sm {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
  border-radius: 8rpx;
}

.btn::after {
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: #ffffff;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
}

.btn-sm {
  padding: 0 20rpx;
  font-size: 24rpx;
  height: 60rpx;
}

.btn-lg {
  padding: 0 40rpx;
  font-size: 32rpx;
  height: 90rpx;
}

.btn-block {
  display: flex;
  width: 100%;
}

/* 卡片样式 */
.card {
  position: relative;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 列表样式 */
.list {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.list-item {
  position: relative;
  display: flex;
  padding: 30rpx;
  align-items: center;
  border-bottom: 1rpx solid var(--border-color);
}

.list-item:last-child {
  border-bottom: none;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 全局透明背景设置 */
view {
  background: transparent;
}

/* 全局 TDesign 组件透明背景强制设置 */
.t-cell {
  background: transparent !important;
}

.t-cell-group {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.t-cell__content {
  background: transparent !important;
}

.t-cell__title {
  background: transparent !important;
}

.t-cell__description {
  background: transparent !important;
}

.t-cell__left {
  background: transparent !important;
}

.t-cell__right {
  background: transparent !important;
}

.t-cell__note {
  background: transparent !important;
}

/* 按钮组件统一样式 */
.t-button {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  color: #333 !important;
  transition: all 0.3s ease !important;
}

.t-button:active {
  background: rgba(255, 255, 255, 0.5) !important;
}

.t-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3) !important;
}

/* 输入框组件统一样式 */
.t-input {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

.t-textarea {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

/* 其他常见组件透明化 */
.t-popup__content {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

.t-drawer__content {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

.t-dialog__content {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

/* 确保所有文本元素透明 */
text {
  background: transparent !important;
}

/* 微信小程序原生组件透明化 */
textarea {
  background: transparent !important;
}

input {
  background: transparent !important;
}

picker {
  background: transparent !important;
}

/* 所有卡片样式统一 */
.card,
.card-container,
.content-card {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16rpx;
}

/* 列表项统一透明 */
.list-item,
.cell-item {
  background: transparent !important;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2) !important;
}

.list-item:last-child,
.cell-item:last-child {
  border-bottom: none !important;
}

/* 交互反馈统一 */
.list-item:active,
.cell-item:active,
.card:active {
  background: rgba(255, 255, 255, 0.2) !important;
}