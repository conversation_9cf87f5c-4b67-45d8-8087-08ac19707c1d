{"description": "项目配置文件", "miniprogramRoot": "", "setting": {"es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "bundle": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "minifyWXML": true, "showES6CompileOption": false, "minifyWXSS": true, "disableUseStrict": false, "useCompilerPlugins": ["typescript"], "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.6", "appid": "wx5ef6f98248c330d2", "projectname": "assistant-miniprogram", "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}, {"value": "prettier.config.js", "type": "file"}, {"value": "README.md", "type": "file"}, {"value": ".giti<PERSON>re", "type": "file"}, {"value": "node_modules", "type": "folder"}], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}