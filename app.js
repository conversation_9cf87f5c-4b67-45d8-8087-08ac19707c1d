// app.js
App({
  globalData: {
    userInfo: null,
    systemInfo: null,
    uid: null,
    isLoggedIn: false,
    isGuest: false,
    version: '1.0.0',
    settings: {
      theme: 'light',
      fontSize: 'normal',
      language: 'zh_CN'
    }
  },

  onLaunch() {
    console.log('小程序启动');
    
    // 初始化云开发
    this.initCloud();
    
    // 获取系统信息 - 使用新的API替代已弃用的wx.getSystemInfoSync
    try {
      const systemInfo = {
        ...wx.getWindowInfo(),
        ...wx.getDeviceInfo(),
        ...wx.getAppBaseInfo()
      };
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
      // 降级处理，如果新API不可用
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
    }

    // 初始化认证状态
    this.initAuth();
    
    // 加载用户设置
    this.loadUserSettings();
    
    // 检查版本兼容性
    this.checkEnvironment();
  },

  onShow() {
    // 小程序启动或从后台进入前台显示时触发
  },

  onHide() {
    // 小程序从前台进入后台时触发
  },

  onError(error) {
    // 小程序发生脚本错误或 API 调用报错时触发
    console.error('App Error:', error);
    // 可以在这里添加错误上报逻辑
  },

  onPageNotFound(res) {
    // 小程序要打开的页面不存在时触发
    console.warn('Page not found:', res);
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 初始化云开发
  initCloud() {
    try {
      // 检查基础库版本
      if (!wx.cloud) {
        console.error('请使用 2.2.3 或以上的基础库以使用云能力');
        return;
      }
      
      // 尝试初始化云开发环境
      console.log('正在初始化云开发环境...');
      
      // 云开发初始化配置
      const initConfig = {
        traceUser: true,
        // 注意：不指定env参数，让微信开发者工具自动选择当前关联的环境
        // 这样可以避免环境ID硬编码导致的问题
      };
      
      wx.cloud.init(initConfig);
      
      console.log('云开发初始化成功 - 使用当前关联环境');
      
      // 延迟测试连接，确保初始化完成
      setTimeout(() => {
        this.testCloudConnection();
      }, 1000);
      
    } catch (error) {
      console.error('云开发初始化失败:', error);
      
      // 显示友好的错误提示
      wx.showModal({
        title: '云服务初始化失败',
        content: '云端同步功能可能暂时不可用，请先检查云开发环境配置。您仍可以使用本地功能。',
        showCancel: true,
        cancelText: '稍后处理',
        confirmText: '查看帮助',
        success: (res) => {
          if (res.confirm) {
            // 可以跳转到帮助页面或显示更多信息
            console.log('显示云开发配置帮助');
          }
        }
      });
    }
  },

  // 测试云开发连接
  async testCloudConnection() {
    try {
      console.log('测试云开发连接...');
      
      // 使用更轻量的方式测试云开发连接
      // 尝试访问数据库来验证环境连接
      await wx.cloud.database().collection('_test_connection_').limit(1).get();
      
      console.log('云开发连接测试成功');
      
    } catch (error) {
      console.warn('云开发连接测试失败:', error);
      
      // 根据错误类型提供不同的处理
      if (error.errMsg) {
        if (error.errMsg.includes('env check invalid')) {
          console.error('云开发环境配置问题，需要重新配置环境ID');
          
          wx.showModal({
            title: '环境配置问题',
            content: '检测到云开发环境配置问题。请联系开发者更新环境配置，或重新创建云开发环境。',
            showCancel: false,
            confirmText: '我知道了'
          });
        } else if (error.errMsg.includes('collection not exists') || 
                   error.errMsg.includes('该集合不存在')) {
          // 数据库连接正常，只是测试集合不存在（这是预期的）
          console.log('云开发数据库连接正常');
        } else if (error.errMsg.includes('FunctionName parameter could not be found') ||
                   error.errMsg.includes('FUNCTION_NOT_FOUND')) {
          // 云函数相关错误，但数据库可能正常
          console.log('云开发环境连接正常，部分服务可能未部署');
        } else {
          // 其他错误，记录但不影响应用使用
          console.log('云开发服务可能暂时不可用，但不影响本地功能使用');
        }
      }
    }
  },

  // 初始化认证状态
  initAuth() {
    try {
      // 检查本地存储的登录状态
      const uid = wx.getStorageSync('uid');
      const isGuest = wx.getStorageSync('isGuest');
      const userInfo = wx.getStorageSync('userInfo');
      
      if (uid) {
        this.globalData.uid = uid;
        this.globalData.isLoggedIn = true;
        this.globalData.isGuest = isGuest || false;
        
        if (userInfo) {
          this.globalData.userInfo = userInfo;
        }
        
        console.log('恢复登录状态:', { uid, isGuest });
      } else {
        console.log('未登录状态');
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error);
    }
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('user_settings');
      if (settings) {
        this.globalData.settings = { ...this.globalData.settings, ...settings };
      }
    } catch (e) {
      console.error('读取用户设置失败:', e);
    }
  },

  // 检查环境
  checkEnvironment() {
    const systemInfo = this.globalData.systemInfo;
    
    // 检查版本兼容性
    if (systemInfo.SDKVersion < '2.0.0') {
      wx.showModal({
        title: '版本过低',
        content: '请升级微信版本以获得更好的使用体验',
        showCancel: false
      });
    }
    
    // 检查网络状态
    wx.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType);
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络不可用',
            icon: 'none'
          });
        }
      }
    });
  },

  // 设置登录状态
  setLoginStatus(uid, isGuest = false, userInfo = null) {
    this.globalData.uid = uid;
    this.globalData.isLoggedIn = true;
    this.globalData.isGuest = isGuest;
    
    if (userInfo) {
      this.globalData.userInfo = userInfo;
      wx.setStorageSync('userInfo', userInfo);
    }
    
    try {
      wx.setStorageSync('uid', uid);
      wx.setStorageSync('isGuest', isGuest);
      console.log('设置登录状态:', { uid, isGuest });
    } catch (error) {
      console.error('保存登录状态失败:', error);
    }
  },

  // 清除登录状态
  clearLoginStatus() {
    this.globalData.uid = null;
    this.globalData.isLoggedIn = false;
    this.globalData.isGuest = false;
    this.globalData.userInfo = null;
    
    try {
      wx.removeStorageSync('uid');
      wx.removeStorageSync('isGuest');
      wx.removeStorageSync('userInfo');
      console.log('清除登录状态成功');
    } catch (error) {
      console.error('清除登录状态失败:', error);
    }
  },

  // 检查是否需要登录
  requireLogin() {
    if (!this.globalData.isLoggedIn || !this.globalData.uid) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 检查是否为访客模式
  isGuestMode() {
    return this.globalData.isGuest;
  },

  // 获取用户ID
  getUserId() {
    return this.globalData.uid;
  },

  // 获取带用户ID的数据查询条件
  getUserQuery() {
    if (!this.globalData.uid) {
      throw new Error('用户未登录');
    }
    return { uid: this.globalData.uid };
  },

  // 检查Pro订阅状态
  checkProStatus() {
    const userInfo = this.globalData.userInfo;
    if (!userInfo || !userInfo.proUntil) {
      return false;
    }
    
    const expireDate = new Date(userInfo.proUntil);
    const now = new Date();
    return expireDate > now;
  },

  // 显示Pro升级提示
  showProUpgrade(feature = '该功能') {
    wx.showModal({
      title: 'Pro 功能',
      content: `${feature}需要升级到 Pro 版本才能使用。\n\nPro 版本包含：\n• OCR 票据识别\n• 预算预警提醒\n• PDF 报表导出\n• 多人共享账本`,
      confirmText: '立即升级',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/profile/profile'
          });
        }
      }
    });
    return false;
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    this.globalData.userInfo = { ...this.globalData.userInfo, ...userInfo };
    wx.setStorageSync('userInfo', this.globalData.userInfo);
  },

  // 更新设置
  updateSettings(settings) {
    this.globalData.settings = {
      ...this.globalData.settings,
      ...settings
    };
    wx.setStorageSync('user_settings', this.globalData.settings);
  },

  // 微信登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // TODO: 发送 res.code 到后台换取 openId, sessionKey, unionId
            resolve(res);
          } else {
            reject(new Error('登录失败：' + res.errMsg));
          }
        },
        fail: reject
      });
    });
  },

  // 检查登录状态并跳转
  checkLoginAndRedirect() {
    console.log('=== 开始检查登录状态 ===');
    console.log('globalData.isLoggedIn:', this.globalData.isLoggedIn);
    console.log('globalData.uid:', this.globalData.uid);
    console.log('本地存储uid:', wx.getStorageSync('uid'));
    
    // 强制检查：如果没有uid或者isLoggedIn为false，就跳转登录
    const hasUid = this.globalData.uid && wx.getStorageSync('uid');
    const isLoggedIn = this.globalData.isLoggedIn;
    
    if (!hasUid || !isLoggedIn) {
      console.log('❌ 用户未登录，即将跳转到登录页面');
      console.log('原因: hasUid =', hasUid, ', isLoggedIn =', isLoggedIn);
      
      // 使用wx.reLaunch确保页面栈清空，避免用户按返回键回到输入页面
      wx.reLaunch({
        url: '/pages/login/login',
        success: () => {
          console.log('✅ 成功跳转到登录页面');
        },
        fail: (error) => {
          console.error('❌ 跳转登录页面失败:', error);
          // 如果reLaunch失败，尝试navigateTo
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
    } else {
      console.log('✅ 用户已登录，uid:', this.globalData.uid);
    }
    console.log('=== 登录状态检查完成 ===');
  },

  // 强制跳转到登录页面（调试用）
  forceShowLogin() {
    console.log('强制跳转到登录页面');
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 清除所有本地数据（调试用）
  clearAllData() {
    console.log('清除所有本地数据');
    this.clearLoginStatus();
    wx.clearStorageSync();
    this.globalData.isLoggedIn = false;
    this.globalData.uid = null;
    this.globalData.userInfo = null;
    this.globalData.isGuest = false;
  }
});