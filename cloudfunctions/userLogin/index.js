// cloudfunctions/userLogin/index.js
const cloud = require('wx-server-sdk');

// 初始化 cloud
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
});

const db = cloud.database();

/**
 * 用户登录云函数
 * 处理微信code换取openid和unionid，创建或更新用户记录
 */
exports.main = async (event, context) => {
  console.log('用户登录云函数调用:', event);
  
  const { code, userInfo } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    // 获取用户openid和unionid
    const { OPENID, UNIONID } = wxContext;
    
    if (!OPENID) {
      throw new Error('获取用户身份失败');
    }
    
    console.log('用户身份信息:', { OPENID, UNIONID });
    
    // 查询用户是否已存在
    const userCollection = db.collection('users');
    const existingUser = await userCollection.where({
      _id: OPENID
    }).get();
    
    let userData;
    
    if (existingUser.data.length > 0) {
      // 用户已存在，更新登录时间
      userData = existingUser.data[0];
      
      await userCollection.doc(OPENID).update({
        data: {
          lastLoginAt: new Date(),
          lastLoginIP: context.CLIENTIP || 'unknown'
        }
      });
      
      console.log('用户登录成功，更新登录时间:', OPENID);
    } else {
      // 新用户，创建用户记录
      const newUser = {
        _id: OPENID,
        unionid: UNIONID,
        nick: userInfo?.nickName || '新用户',
        avatar: userInfo?.avatarUrl || '',
        gender: userInfo?.gender || 0,
        country: userInfo?.country || '',
        province: userInfo?.province || '',
        city: userInfo?.city || '',
        language: userInfo?.language || 'zh_CN',
        createdAt: new Date(),
        lastLoginAt: new Date(),
        lastLoginIP: context.CLIENTIP || 'unknown',
        proUntil: null, // Pro订阅到期时间
        settings: {
          theme: 'auto',
          notifications: true,
          syncEnabled: true
        },
        totalUsageDays: 0,
        totalRecords: 0
      };
      
      await userCollection.add({
        data: newUser
      });
      
      userData = newUser;
      console.log('新用户创建成功:', OPENID);
    }
    
    // 返回用户数据（不包含敏感信息）
    const responseData = {
      uid: OPENID,
      unionid: UNIONID,
      nick: userData.nick,
      avatar: userData.avatar,
      proUntil: userData.proUntil,
      createdAt: userData.createdAt,
      settings: userData.settings,
      isNewUser: existingUser.data.length === 0
    };
    
    return {
      success: true,
      message: '登录成功',
      data: responseData
    };
    
  } catch (error) {
    console.error('用户登录失败:', error);
    
    return {
      success: false,
      message: error.message || '登录失败',
      error: error.toString()
    };
  }
};