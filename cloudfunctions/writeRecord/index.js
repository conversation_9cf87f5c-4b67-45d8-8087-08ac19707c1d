// cloudfunctions/writeRecord/index.js
const cloud = require('wx-server-sdk');

// 初始化cloud
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 统一数据写入云函数
 * 支持增删改查操作，包含数据验证和权限检查
 */
exports.main = async (event, context) => {
  console.log('writeRecord云函数被调用:', event);
  
  try {
    // 获取微信用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败'
      };
    }
    
    // 解析请求参数
    const { action, collection, recordId, data, batchData } = event;
    
    // 验证必要参数
    if (!action || !collection) {
      return {
        success: false,
        message: '缺少必要参数: action, collection'
      };
    }
    
    // 权限检查：确保用户只能操作自己的数据
    if (data && data.uid && data.uid !== openid) {
      return {
        success: false,
        message: '权限不足：无法操作其他用户数据'
      };
    }
    
    // 根据操作类型执行相应操作
    let result;
    switch (action) {
      case 'create':
        result = await createRecord(collection, openid, data);
        break;
      case 'update':
        result = await updateRecord(collection, openid, recordId, data);
        break;
      case 'delete':
        result = await deleteRecord(collection, openid, recordId);
        break;
      case 'batchCreate':
        result = await batchCreateRecords(collection, openid, batchData);
        break;
      default:
        return {
          success: false,
          message: `不支持的操作类型: ${action}`
        };
    }
    
    return {
      success: true,
      data: result,
      message: '操作成功'
    };
    
  } catch (error) {
    console.error('writeRecord云函数执行出错:', error);
    return {
      success: false,
      message: error.message || '服务器内部错误',
      error: error.toString()
    };
  }
};

/**
 * 创建记录
 */
async function createRecord(collection, uid, data) {
  // 添加系统字段
  const record = {
    ...data,
    uid,
    createdAt: db.serverDate(),
    updatedAt: db.serverDate()
  };
  
  // 数据验证
  const validationResult = validateRecord(collection, record);
  if (!validationResult.valid) {
    throw new Error(`数据验证失败: ${validationResult.message}`);
  }
  
  // 写入数据库
  const result = await db.collection(collection).add({
    data: record
  });
  
  return {
    id: result._id,
    ...record
  };
}

/**
 * 更新记录
 */
async function updateRecord(collection, uid, recordId, data) {
  if (!recordId) {
    throw new Error('记录ID不能为空');
  }
  
  // 检查记录是否存在且属于当前用户
  const existingRecord = await db.collection(collection)
    .doc(recordId)
    .get();
    
  if (!existingRecord.data || existingRecord.data.uid !== uid) {
    throw new Error('记录不存在或无权限访问');
  }
  
  // 更新数据
  const updateData = {
    ...data,
    updatedAt: db.serverDate()
  };
  
  // 数据验证
  const validationResult = validateRecord(collection, updateData, false);
  if (!validationResult.valid) {
    throw new Error(`数据验证失败: ${validationResult.message}`);
  }
  
  await db.collection(collection)
    .doc(recordId)
    .update({
      data: updateData
    });
    
  return {
    id: recordId,
    ...updateData
  };
}

/**
 * 删除记录
 */
async function deleteRecord(collection, uid, recordId) {
  if (!recordId) {
    throw new Error('记录ID不能为空');
  }
  
  // 检查记录是否存在且属于当前用户
  const existingRecord = await db.collection(collection)
    .doc(recordId)
    .get();
    
  if (!existingRecord.data || existingRecord.data.uid !== uid) {
    throw new Error('记录不存在或无权限访问');
  }
  
  // 软删除：标记为已删除而不是物理删除
  await db.collection(collection)
    .doc(recordId)
    .update({
      data: {
        deleted: true,
        deletedAt: db.serverDate(),
        updatedAt: db.serverDate()
      }
    });
    
  return { id: recordId, deleted: true };
}

/**
 * 批量创建记录
 */
async function batchCreateRecords(collection, uid, batchData) {
  if (!Array.isArray(batchData) || batchData.length === 0) {
    throw new Error('批量数据必须是非空数组');
  }
  
  // 限制批量操作数量
  if (batchData.length > 100) {
    throw new Error('批量操作数量不能超过100条');
  }
  
  const results = [];
  
  // 逐条处理（可以优化为真正的批量操作）
  for (const data of batchData) {
    try {
      const result = await createRecord(collection, uid, data);
      results.push({ success: true, data: result });
    } catch (error) {
      results.push({ 
        success: false, 
        error: error.message,
        data: data 
      });
    }
  }
  
  return results;
}

/**
 * 数据验证函数
 */
function validateRecord(collection, data, isCreate = true) {
  try {
    switch (collection) {
      case 'schedules':
        return validateSchedule(data, isCreate);
      case 'expenses':
        return validateExpense(data, isCreate);
      case 'todos':
        return validateTodo(data, isCreate);
      default:
        // 基础验证：所有记录都必须有uid
        if (isCreate && !data.uid) {
          return { valid: false, message: 'uid字段不能为空' };
        }
        return { valid: true };
    }
  } catch (error) {
    return { valid: false, message: error.message };
  }
}

/**
 * 日程数据验证
 */
function validateSchedule(data, isCreate) {
  if (isCreate) {
    if (!data.title || typeof data.title !== 'string') {
      return { valid: false, message: '日程标题不能为空' };
    }
    if (!data.startTime) {
      return { valid: false, message: '开始时间不能为空' };
    }
  }
  
  // 时间格式验证
  if (data.startTime && isNaN(new Date(data.startTime).getTime())) {
    return { valid: false, message: '开始时间格式无效' };
  }
  
  if (data.endTime && isNaN(new Date(data.endTime).getTime())) {
    return { valid: false, message: '结束时间格式无效' };
  }
  
  // 逻辑验证
  if (data.startTime && data.endTime && new Date(data.startTime) >= new Date(data.endTime)) {
    return { valid: false, message: '开始时间不能晚于结束时间' };
  }
  
  return { valid: true };
}

/**
 * 财务数据验证
 */
function validateExpense(data, isCreate) {
  if (isCreate) {
    if (typeof data.amount !== 'number' || data.amount <= 0) {
      return { valid: false, message: '金额必须是大于0的数字' };
    }
    if (!data.category || typeof data.category !== 'string') {
      return { valid: false, message: '分类不能为空' };
    }
  }
  
  // 金额范围验证
  if (data.amount && (data.amount < 0 || data.amount > 999999.99)) {
    return { valid: false, message: '金额必须在0-999999.99范围内' };
  }
  
  return { valid: true };
}

/**
 * 待办数据验证
 */
function validateTodo(data, isCreate) {
  if (isCreate) {
    if (!data.title || typeof data.title !== 'string') {
      return { valid: false, message: '待办标题不能为空' };
    }
  }
  
  // 优先级验证
  if (data.priority && (data.priority < 1 || data.priority > 5)) {
    return { valid: false, message: '优先级必须在1-5范围内' };
  }
  
  return { valid: true };
}