// cloudfunctions/deleteAccount/index.js
const cloud = require('wx-server-sdk');

// 初始化 cloud
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 用户注销账户云函数
 * 删除用户的所有数据，包括用户记录、日程、财务、待办等
 */
exports.main = async (event, context) => {
  console.log('用户注销账户云函数调用');
  
  const wxContext = cloud.getWXContext();
  const { OPENID } = wxContext;
  
  if (!OPENID) {
    return {
      success: false,
      message: '用户身份验证失败'
    };
  }
  
  try {
    console.log('开始删除用户数据:', OPENID);
    
    // 删除用户相关的所有数据集合
    const collections = [
      'users',        // 用户基本信息
      'schedules',    // 日程数据
      'expenses',     // 财务数据  
      'todos',        // 待办事项
      'records',      // 语音记录
      'sync_logs',    // 同步日志
      'user_settings' // 用户设置
    ];
    
    const deletePromises = collections.map(async (collectionName) => {
      try {
        // 删除该集合中属于用户的所有记录
        const result = await db.collection(collectionName)
          .where({
            uid: OPENID
          })
          .remove();
        
        console.log(`删除 ${collectionName} 集合数据:`, result.stats);
        return {
          collection: collectionName,
          deleted: result.stats.removed || 0
        };
      } catch (error) {
        console.error(`删除 ${collectionName} 集合失败:`, error);
        return {
          collection: collectionName,
          deleted: 0,
          error: error.message
        };
      }
    });
    
    // 等待所有删除操作完成
    const deleteResults = await Promise.all(deletePromises);
    
    // 统计删除结果
    const totalDeleted = deleteResults.reduce((sum, result) => sum + result.deleted, 0);
    const errors = deleteResults.filter(result => result.error);
    
    console.log('用户数据删除完成:', {
      totalDeleted,
      details: deleteResults,
      errors
    });
    
    // 删除用户在对象存储中的文件（如果有的话）
    try {
      // TODO: 删除COS中的用户文件
      // 这里可以删除用户上传的语音文件、头像等
      console.log('删除用户文件完成');
    } catch (error) {
      console.error('删除用户文件失败:', error);
    }
    
    return {
      success: true,
      message: '账户注销成功',
      data: {
        deletedRecords: totalDeleted,
        details: deleteResults,
        errors: errors.length > 0 ? errors : null
      }
    };
    
  } catch (error) {
    console.error('用户注销失败:', error);
    
    return {
      success: false,
      message: '注销失败，请稍后重试',
      error: error.toString()
    };
  }
};